﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

namespace Admin.NET.Application;

/// <summary>
/// 蓝牙桌牌设备基础输入参数
/// </summary>
public class DevicesBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 网关ID
    /// </summary>
    public virtual long? ap_id { get; set; }
    
    /// <summary>
    /// 设备ID
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    public virtual string device_id { get; set; }
    
    /// <summary>
    /// 设备名称
    /// </summary>
    public virtual string? device_name { get; set; }
    
    /// <summary>
    /// MAC地址
    /// </summary>
    public virtual string? mac_address { get; set; }
    
    /// <summary>
    /// 1：姓名桌牌；2：价格标签
    /// </summary>
    public virtual int? device_type { get; set; }
    
    /// <summary>
    /// 1：在线；2离线；3：绑定；4:异常
    /// </summary>
    public virtual int? status { get; set; }
    
    /// <summary>
    /// 电量
    /// </summary>
    public virtual int? battery_level { get; set; }
    
    /// <summary>
    /// 固件版本
    /// </summary>
    public virtual string? firmware_version { get; set; }
    
    /// <summary>
    /// 绑定租户Id
    /// </summary>
    public virtual long? BindTenantId { get; set; }
    
    /// <summary>
    /// 绑定用户Id
    /// </summary>
    public virtual long? BindUserId { get; set; }
    
}

/// <summary>
/// 蓝牙桌牌设备分页查询输入参数
/// </summary>
public class PageDevicesInput : BasePageInput
{
    /// <summary>
    /// 设备名称
    /// </summary>
    public string? device_name { get; set; }
    
    /// <summary>
    /// 1：姓名桌牌；2：价格标签
    /// </summary>
    public int? device_type { get; set; }
    
    /// <summary>
    /// 1：在线；2离线；3：绑定；4:异常
    /// </summary>
    public int? status { get; set; }
    
}

/// <summary>
/// 蓝牙桌牌设备增加输入参数
/// </summary>
public class AddDevicesInput
{
    /// <summary>
    /// 网关ID
    /// </summary>
    public long? ap_id { get; set; }
    
    /// <summary>
    /// 设备ID
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    [MaxLength(100, ErrorMessage = "设备ID字符长度不能超过100")]
    public string device_id { get; set; }
    
    /// <summary>
    /// 设备名称
    /// </summary>
    [MaxLength(200, ErrorMessage = "设备名称字符长度不能超过200")]
    public string? device_name { get; set; }
    
    /// <summary>
    /// MAC地址
    /// </summary>
    [MaxLength(17, ErrorMessage = "MAC地址字符长度不能超过17")]
    public string? mac_address { get; set; }
    
    /// <summary>
    /// 1：姓名桌牌；2：价格标签
    /// </summary>
    public int? device_type { get; set; }
    
    /// <summary>
    /// 1：在线；2离线；3：绑定；4:异常
    /// </summary>
    public int? status { get; set; }
    
    /// <summary>
    /// 电量
    /// </summary>
    public int? battery_level { get; set; }
    
    /// <summary>
    /// 固件版本
    /// </summary>
    [MaxLength(50, ErrorMessage = "固件版本字符长度不能超过50")]
    public string? firmware_version { get; set; }
    
    /// <summary>
    /// 绑定租户Id
    /// </summary>
    public long? BindTenantId { get; set; }
    
    /// <summary>
    /// 绑定用户Id
    /// </summary>
    public long? BindUserId { get; set; }
    
}

/// <summary>
/// 蓝牙桌牌设备删除输入参数
/// </summary>
public class DeleteDevicesInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 蓝牙桌牌设备更新输入参数
/// </summary>
public class UpdateDevicesInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 网关ID
    /// </summary>    
    public long? ap_id { get; set; }
    
    /// <summary>
    /// 设备ID
    /// </summary>    
    [Required(ErrorMessage = "设备ID不能为空")]
    [MaxLength(100, ErrorMessage = "设备ID字符长度不能超过100")]
    public string device_id { get; set; }
    
    /// <summary>
    /// 设备名称
    /// </summary>    
    [MaxLength(200, ErrorMessage = "设备名称字符长度不能超过200")]
    public string? device_name { get; set; }
    
    /// <summary>
    /// MAC地址
    /// </summary>    
    [MaxLength(17, ErrorMessage = "MAC地址字符长度不能超过17")]
    public string? mac_address { get; set; }
    
    /// <summary>
    /// 1：姓名桌牌；2：价格标签
    /// </summary>    
    public int? device_type { get; set; }
    
    /// <summary>
    /// 1：在线；2离线；3：绑定；4:异常
    /// </summary>    
    public int? status { get; set; }
    
    /// <summary>
    /// 电量
    /// </summary>    
    public int? battery_level { get; set; }
    
    /// <summary>
    /// 固件版本
    /// </summary>    
    [MaxLength(50, ErrorMessage = "固件版本字符长度不能超过50")]
    public string? firmware_version { get; set; }
    
    /// <summary>
    /// 绑定租户Id
    /// </summary>    
    public long? BindTenantId { get; set; }
    
    /// <summary>
    /// 绑定用户Id
    /// </summary>    
    public long? BindUserId { get; set; }
    
}

/// <summary>
/// 蓝牙桌牌设备主键查询输入参数
/// </summary>
public class QueryByIdDevicesInput : DeleteDevicesInput
{
}

