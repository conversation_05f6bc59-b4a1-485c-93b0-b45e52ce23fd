fail: 2025-07-18 09:44:19.9425294 +08:00 Friday L Admin.NET.Application.MeetingStaffService[0] #19 '00-3ef71afdaa9ff3d0f52e0f773facba6c-24723bdd159dfd3f-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      会议人员更新同步到第三方平台失败 - ID: 698687391182917, 姓名: rrere, 请求路径: /admin-api/meeting/staff/update, 头像URL: 
fail: 2025-07-18 10:16:07.2807497 +08:00 Friday L System.Logging.LoggingMonitor[0] #22 '00-572e12ce7a0e029c623f8af2a6ee7dd9-3b03d288382f8cc7-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.BatchDelete
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       BatchDelete
      ┣ 显示名称：                       批量删除会议人员表
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: batchDelete
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/batchDelete
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                 *******
      ┣ 客户端源端口：                   55519
      ┣ 服务端 IP 地址：                 *******
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-572e12ce7a0e029c623f8af2a6ee7dd9-3b03d288382f8cc7-00
      ┣ 服务线程 ID：                    #42
      ┣ 执行耗时：                       386ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://[::]:5005
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       Admin.NET.Web.Entry
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ nbf (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ exp (integer64)：                ********** (2025-07-25 09:39:59:0000(+08:00) Friday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     keep-alive
      ┣ Host：                           localhost:5005
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ Content-Type：                   application/json
      ┣ Origin：                         http://localhost:8888
      ┣ Referer：                        http://localhost:8888/
      ┣ Content-Length：                 51
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   application/json
      ┣ 
      ┣ input (List`1)：                 [{"id":698937454440517},{"id":698687391182917}]
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<System.Int32>
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.InvalidOperationException
      ┣ 消息：                           Nullable object must have a value.
      ┣ 错误堆栈：                       at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingStaffService.BatchDelete(List`1 input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 263
         at lambda_method446(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Nullable object must have a value.
         at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingStaffService.BatchDelete(List`1 input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 263
         at lambda_method446(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 10:16:15.2516853 +08:00 Friday L System.Logging.LoggingMonitor[0] #9 '00-3f25263515c6a5a0802824e2368d8768-cf2c1359fbbd7b0d-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.BatchDelete
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       BatchDelete
      ┣ 显示名称：                       批量删除会议人员表
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: batchDelete
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/batchDelete
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                 *******
      ┣ 客户端源端口：                   55519
      ┣ 服务端 IP 地址：                 *******
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-3f25263515c6a5a0802824e2368d8768-cf2c1359fbbd7b0d-00
      ┣ 服务线程 ID：                    #9
      ┣ 执行耗时：                       104ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://[::]:5005
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       Admin.NET.Web.Entry
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ nbf (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ exp (integer64)：                ********** (2025-07-25 09:39:59:0000(+08:00) Friday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     keep-alive
      ┣ Host：                           localhost:5005
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ Content-Type：                   application/json
      ┣ Origin：                         http://localhost:8888
      ┣ Referer：                        http://localhost:8888/
      ┣ Content-Length：                 51
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   application/json
      ┣ 
      ┣ input (List`1)：                 [{"id":698937454440517},{"id":698687391182917}]
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<System.Int32>
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.InvalidOperationException
      ┣ 消息：                           Nullable object must have a value.
      ┣ 错误堆栈：                       at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingStaffService.BatchDelete(List`1 input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 263
         at lambda_method446(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Nullable object must have a value.
         at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingStaffService.BatchDelete(List`1 input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 263
         at lambda_method446(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 10:17:51.6116152 +08:00 Friday L System.Logging.LoggingMonitor[0] #41 '00-2e1843840c1746ceac41ce63eb2b4a0b-973301fadaf4d2ea-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.BatchDelete
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       BatchDelete
      ┣ 显示名称：                       批量删除会议人员表
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: batchDelete
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/batchDelete
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                 *******
      ┣ 客户端源端口：                   55519
      ┣ 服务端 IP 地址：                 *******
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-2e1843840c1746ceac41ce63eb2b4a0b-973301fadaf4d2ea-00
      ┣ 服务线程 ID：                    #41
      ┣ 执行耗时：                       78118ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://[::]:5005
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       Admin.NET.Web.Entry
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ nbf (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ exp (integer64)：                ********** (2025-07-25 09:39:59:0000(+08:00) Friday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     keep-alive
      ┣ Host：                           localhost:5005
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ Content-Type：                   application/json
      ┣ Origin：                         http://localhost:8888
      ┣ Referer：                        http://localhost:8888/
      ┣ Content-Length：                 51
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   application/json
      ┣ 
      ┣ input (List`1)：                 [{"id":698937454440517},{"id":698687391182917}]
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<System.Int32>
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.InvalidOperationException
      ┣ 消息：                           Nullable object must have a value.
      ┣ 错误堆栈：                       at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingStaffService.BatchDelete(List`1 input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 263
         at lambda_method446(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Nullable object must have a value.
         at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingStaffService.BatchDelete(List`1 input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 263
         at lambda_method446(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 10:24:43.4611101 +08:00 Friday L System.Logging.LoggingMonitor[0] #51 '00-eae6869a2523f3397c92b6dc5b9139ed-95f804aeb8c6a114-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.Delete
      ┣ 
      ┣ 控制器名称：                             MeetingStaffService
      ┣ 操作名称：                               Delete
      ┣ 显示名称：                               删除会议人员表
      ┣ 路由信息：                               [area]: ; [controller]: meetingStaff; [action]: delete
      ┣ 请求方式：                               POST
      ┣ 请求地址：                               http://localhost:5005/api/meetingStaff/delete
      ┣ HTTP 协议：                              HTTP/1.1
      ┣ 来源地址：                               http://localhost:8888/
      ┣ 请求端源：                               client
      ┣ 浏览器标识：                             Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                         zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                         *******
      ┣ 客户端源端口：                           55520
      ┣ 服务端 IP 地址：                         *******
      ┣ 服务端源端口：                           5005
      ┣ 客户端连接 ID：                          00-eae6869a2523f3397c92b6dc5b9139ed-95f804aeb8c6a114-00
      ┣ 服务线程 ID：                            #51
      ┣ 执行耗时：                               79ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                                 
      ┣ 响应端：                                 
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                               Microsoft Windows 10.0.26100
      ┣ 系统架构：                               X64
      ┣ 基础框架：                               Furion.Pure v4.9.7.93
      ┣ .NET 架构：                              .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                           http://[::]:5005
      ┣ 运行环境：                               Development
      ┣ 启动程序集：                             Admin.NET.Web.Entry
      ┣ 进程名称：                               Admin.NET.Web.Entry
      ┣ 托管程序：                               Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                              Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ 
      ┣ UserId (integer64)：                     *************
      ┣ TenantId (integer64)：                   *************
      ┣ Account (string)：                       superadmin
      ┣ RealName (string)：                      超级管理员
      ┣ AccountType (integer32)：                999
      ┣ OrgId (integer32)：                      0
      ┣ OrgName (JSON_NULL)：                    
      ┣ OrgType (JSON_NULL)：                    
      ┣ iat (integer64)：                        ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ nbf (integer64)：                        ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ exp (integer64)：                        ********** (2025-07-25 09:39:59:0000(+08:00) Friday L)
      ┣ iss (string)：                           Admin.NET
      ┣ aud (string)：                           Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                                 application/json, text/plain, */*
      ┣ Connection：                             keep-alive
      ┣ Host：                                   localhost:5005
      ┣ User-Agent：                             Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                        gzip, deflate, br, zstd
      ┣ Accept-Language：                        zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                          Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ Content-Type：                           application/json
      ┣ Origin：                                 http://localhost:8888
      ┣ Referer：                                http://localhost:8888/
      ┣ Content-Length：                         24
      ┣ sec-ch-ua-platform：                     "Windows"
      ┣ sec-ch-ua：                              "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：                       ?0
      ┣ Sec-Fetch-Site：                         same-site
      ┣ Sec-Fetch-Mode：                         cors
      ┣ Sec-Fetch-Dest：                         empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                           application/json
      ┣ 
      ┣ input (DeleteMeetingStaffInput)：        {"id":698942277550149}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                         200
      ┣ 原始类型：                               System.Threading.Tasks.Task
      ┣ 最终类型：                               
      ┣ 最终返回值：                             null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                                   System.InvalidOperationException
      ┣ 消息：                                   Nullable object must have a value.
      ┣ 错误堆栈：                               at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingStaffService.Delete(DeleteMeetingStaffInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 230
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Nullable object must have a value.
         at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingStaffService.Delete(DeleteMeetingStaffInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 230
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 11:16:31.2452652 +08:00 Friday L System.Logging.UnitOfWork[0] #19 '00-d1ddddcf1bf269cc804d074261907bcd-34bed57fb874e0c0-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      [Database Transaction] Transaction rolled back due to an error.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      MySqlConnector.MySqlException (0x80004005): Failed to read the result set.
       ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
         at MySqlConnector.Protocol.Serialization.SocketByteHandler.DoReadBytesSync(Memory`1 buffer) in /_/src/MySqlConnector/Protocol/Serialization/SocketByteHandler.cs:line 40
      --- End of stack trace from previous location ---
         at MySqlConnector.Protocol.Serialization.BufferedByteReader.ReadBytesAsync(IByteHandler byteHandler, ArraySegment`1 buffer, Int32 totalBytesToRead, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/BufferedByteReader.cs:line 34
         at MySqlConnector.Protocol.Serialization.ProtocolUtility.<ReadPacketAsync>g__AddContinuation|1_0(ValueTask`1 headerBytes, BufferedByteReader bufferedByteReader, IByteHandler byteHandler, Func`1 getNextSequenceNumber, ProtocolErrorBehavior protocolErrorBehavior, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/ProtocolUtility.cs:line 423
         at MySqlConnector.Protocol.Serialization.ProtocolUtility.<DoReadPayloadAsync>g__AddContinuation|5_0(ValueTask`1 readPacketTask, BufferedByteReader bufferedByteReader, IByteHandler byteHandler, Func`1 getNextSequenceNumber, ArraySegmentHolder`1 previousPayloads, ProtocolErrorBehavior protocolErrorBehavior, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/ProtocolUtility.cs:line 494
         at MySqlConnector.Core.ServerSession.ReceiveReplyAsyncAwaited(ValueTask`1 task) in /_/src/MySqlConnector/Core/ServerSession.cs:line 951
         at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 43
         at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 133
         at MySqlConnector.MySqlDataReader.CreateAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 468
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 11:16:32.3138203 +08:00 Friday L System.Logging.LoggingMonitor[0] #19 '00-d1ddddcf1bf269cc804d074261907bcd-34bed57fb874e0c0-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.ImportData
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       ImportData
      ┣ 显示名称：                       导入会议人员表记录
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: import
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/import
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                 *******
      ┣ 客户端源端口：                   60897
      ┣ 服务端 IP 地址：                 *******
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-d1ddddcf1bf269cc804d074261907bcd-34bed57fb874e0c0-00
      ┣ 服务线程 ID：                    #19
      ┣ 执行耗时：                       544373ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://[::]:5005
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       Admin.NET.Web.Entry
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ nbf (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ exp (integer64)：                ********** (2025-07-25 09:39:59:0000(+08:00) Friday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     keep-alive
      ┣ Host：                           localhost:5005
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryBHEDbSMfBMJt7urV
      ┣ Origin：                         http://localhost:8888
      ┣ Referer：                        http://localhost:8888/
      ┣ Content-Length：                 11227
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryBHEDbSMfBMJt7urV
      ┣ 
      ┣ file (IFormFile)：               [name]: 会议人员表导入模板-2025-07-18_105704.xlsx; [size]: 11KB; [content-type]: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       Microsoft.AspNetCore.Mvc.IActionResult
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           MySqlConnector.MySqlException
      ┣ 消息：                           Failed to read the result set.
      ┣ 错误堆栈：                       at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 133
         at MySqlConnector.MySqlDataReader.CreateAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 468
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      MySqlConnector.MySqlException (0x80004005): Failed to read the result set.
       ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
         at MySqlConnector.Protocol.Serialization.SocketByteHandler.DoReadBytesSync(Memory`1 buffer) in /_/src/MySqlConnector/Protocol/Serialization/SocketByteHandler.cs:line 40
      --- End of stack trace from previous location ---
         at MySqlConnector.Protocol.Serialization.BufferedByteReader.ReadBytesAsync(IByteHandler byteHandler, ArraySegment`1 buffer, Int32 totalBytesToRead, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/BufferedByteReader.cs:line 34
         at MySqlConnector.Protocol.Serialization.ProtocolUtility.<ReadPacketAsync>g__AddContinuation|1_0(ValueTask`1 headerBytes, BufferedByteReader bufferedByteReader, IByteHandler byteHandler, Func`1 getNextSequenceNumber, ProtocolErrorBehavior protocolErrorBehavior, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/ProtocolUtility.cs:line 423
         at MySqlConnector.Protocol.Serialization.ProtocolUtility.<DoReadPayloadAsync>g__AddContinuation|5_0(ValueTask`1 readPacketTask, BufferedByteReader bufferedByteReader, IByteHandler byteHandler, Func`1 getNextSequenceNumber, ArraySegmentHolder`1 previousPayloads, ProtocolErrorBehavior protocolErrorBehavior, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/ProtocolUtility.cs:line 494
         at MySqlConnector.Core.ServerSession.ReceiveReplyAsyncAwaited(ValueTask`1 task) in /_/src/MySqlConnector/Core/ServerSession.cs:line 951
         at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 43
         at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 133
         at MySqlConnector.MySqlDataReader.CreateAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 468
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 11:24:32.3951527 +08:00 Friday L System.Logging.StringLogging[0] #25 '00-3a47beb4522794349471aadce9212827-7f4f6e739a35adee-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-18 11:24:32——错误SQL】
      
      [Sql]:INSERT INTO `SysFile`  
                 (`Provider`,`BucketName`,`FileName`,`Suffix`,`FilePath`,`SizeKb`,`SizeInfo`,`Url`,`FileMd5`,`FileType`,`FileAlias`,`IsPublic`,`DataId`,`TenantId`,`OrgId`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`)
           VALUES
                 (@Provider,@BucketName,@FileName,@Suffix,@FilePath,@SizeKb,@SizeInfo,@Url,@FileMd5,@FileType,@FileAlias,@IsPublic,@DataId,@TenantId,@OrgId,@CreateTime,@UpdateTime,@CreateUserId,@CreateUserName,@UpdateUserId,@UpdateUserName,@Id) ; 
      [Pars]:
      [Name]:@Provider [Value]: [Type]:String    
      [Name]:@BucketName [Value]:Local [Type]:String    
      [Name]:@FileName [Value]:会议人员表导入模板-2025-07-18_105704 [Type]:String    
      [Name]:@Suffix [Value]:.xlsx [Type]:String    
      [Name]:@FilePath [Value]:upload/2025/07/18 [Type]:String    
      [Name]:@SizeKb [Value]:10 [Type]:Int64    
      [Name]:@SizeInfo [Value]: [Type]:String    
      [Name]:@Url [Value]:upload/2025/07/18/698957169053765.xlsx [Type]:String    
      [Name]:@FileMd5 [Value]: [Type]:String    
      [Name]:@FileType [Value]: [Type]:String    
      [Name]:@FileAlias [Value]: [Type]:String    
      [Name]:@IsPublic [Value]:False [Type]:Boolean    
      [Name]:@DataId [Value]: [Type]:Int64    
      [Name]:@TenantId [Value]:************* [Type]:Int64    
      [Name]:@OrgId [Value]:0 [Type]:Int64    
      [Name]:@CreateTime [Value]:2025-7-18 11:24:02 [Type]:DateTime    
      [Name]:@UpdateTime [Value]: [Type]:DateTime    
      [Name]:@CreateUserId [Value]:************* [Type]:Int64    
      [Name]:@CreateUserName [Value]:超级管理员 [Type]:String    
      [Name]:@UpdateUserId [Value]: [Type]:Int64    
      [Name]:@UpdateUserName [Value]: [Type]:String    
      [Name]:@Id [Value]:698957169053765 [Type]:Int64    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: The Command Timeout expired before the operation completed.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 11:25:25.9733240 +08:00 Friday L System.Logging.StringLogging[0] #42 '00-c75ca629e06339e093256c806d9ef0cc-9b1f6b543267554b-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-18 11:25:25——错误SQL】
      
      [Sql]:INSERT INTO `SysFile`  
                 (`Provider`,`BucketName`,`FileName`,`Suffix`,`FilePath`,`SizeKb`,`SizeInfo`,`Url`,`FileMd5`,`FileType`,`FileAlias`,`IsPublic`,`DataId`,`TenantId`,`OrgId`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`)
           VALUES
                 (@Provider,@BucketName,@FileName,@Suffix,@FilePath,@SizeKb,@SizeInfo,@Url,@FileMd5,@FileType,@FileAlias,@IsPublic,@DataId,@TenantId,@OrgId,@CreateTime,@UpdateTime,@CreateUserId,@CreateUserName,@UpdateUserId,@UpdateUserName,@Id) ; 
      [Pars]:
      [Name]:@Provider [Value]: [Type]:String    
      [Name]:@BucketName [Value]:Local [Type]:String    
      [Name]:@FileName [Value]:会议人员表导入模板-2025-07-18_105704 [Type]:String    
      [Name]:@Suffix [Value]:.xlsx [Type]:String    
      [Name]:@FilePath [Value]:upload/2025/07/18 [Type]:String    
      [Name]:@SizeKb [Value]:10 [Type]:Int64    
      [Name]:@SizeInfo [Value]: [Type]:String    
      [Name]:@Url [Value]:upload/2025/07/18/698957387808837.xlsx [Type]:String    
      [Name]:@FileMd5 [Value]: [Type]:String    
      [Name]:@FileType [Value]: [Type]:String    
      [Name]:@FileAlias [Value]: [Type]:String    
      [Name]:@IsPublic [Value]:False [Type]:Boolean    
      [Name]:@DataId [Value]: [Type]:Int64    
      [Name]:@TenantId [Value]:************* [Type]:Int64    
      [Name]:@OrgId [Value]:0 [Type]:Int64    
      [Name]:@CreateTime [Value]:2025-7-18 11:24:55 [Type]:DateTime    
      [Name]:@UpdateTime [Value]: [Type]:DateTime    
      [Name]:@CreateUserId [Value]:************* [Type]:Int64    
      [Name]:@CreateUserName [Value]:超级管理员 [Type]:String    
      [Name]:@UpdateUserId [Value]: [Type]:Int64    
      [Name]:@UpdateUserName [Value]: [Type]:String    
      [Name]:@Id [Value]:698957387808837 [Type]:Int64    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: The Command Timeout expired before the operation completed.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 11:36:47.6891884 +08:00 Friday L System.Logging.StringLogging[0] #43 '00-9262221a3ab9ec3f3b587dfcb95d1acd-8a56870aec8ade1a-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-18 11:36:47——错误SQL】
      
      [Sql]:INSERT INTO `SysFile`  
                 (`Provider`,`BucketName`,`FileName`,`Suffix`,`FilePath`,`SizeKb`,`SizeInfo`,`Url`,`FileMd5`,`FileType`,`FileAlias`,`IsPublic`,`DataId`,`TenantId`,`OrgId`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`)
           VALUES
                 (@Provider,@BucketName,@FileName,@Suffix,@FilePath,@SizeKb,@SizeInfo,@Url,@FileMd5,@FileType,@FileAlias,@IsPublic,@DataId,@TenantId,@OrgId,@CreateTime,@UpdateTime,@CreateUserId,@CreateUserName,@UpdateUserId,@UpdateUserName,@Id) ; 
      [Pars]:
      [Name]:@Provider [Value]: [Type]:String    
      [Name]:@BucketName [Value]:Local [Type]:String    
      [Name]:@FileName [Value]:会议人员表导入模板-2025-07-18_105704 [Type]:String    
      [Name]:@Suffix [Value]:.xlsx [Type]:String    
      [Name]:@FilePath [Value]:upload/2025/07/18 [Type]:String    
      [Name]:@SizeKb [Value]:10 [Type]:Int64    
      [Name]:@SizeInfo [Value]: [Type]:String    
      [Name]:@Url [Value]:upload/2025/07/18/698959067435077.xlsx [Type]:String    
      [Name]:@FileMd5 [Value]: [Type]:String    
      [Name]:@FileType [Value]: [Type]:String    
      [Name]:@FileAlias [Value]: [Type]:String    
      [Name]:@IsPublic [Value]:False [Type]:Boolean    
      [Name]:@DataId [Value]: [Type]:Int64    
      [Name]:@TenantId [Value]:************* [Type]:Int64    
      [Name]:@OrgId [Value]:0 [Type]:Int64    
      [Name]:@CreateTime [Value]:2025-7-18 11:31:45 [Type]:DateTime    
      [Name]:@UpdateTime [Value]: [Type]:DateTime    
      [Name]:@CreateUserId [Value]:************* [Type]:Int64    
      [Name]:@CreateUserName [Value]:超级管理员 [Type]:String    
      [Name]:@UpdateUserId [Value]: [Type]:Int64    
      [Name]:@UpdateUserName [Value]: [Type]:String    
      [Name]:@Id [Value]:698959067435077 [Type]:Int64    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: The Command Timeout expired before the operation completed.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 11:37:13.7634800 +08:00 Friday L System.Logging.UnitOfWork[0] #10 '00-9262221a3ab9ec3f3b587dfcb95d1acd-8a56870aec8ade1a-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      [Database Transaction] Transaction rolled back due to an error.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Connection must be Open; current state is Closed
         at MySqlConnector.MySqlConnection.get_Session() in /_/src/MySqlConnector/MySqlConnection.cs:line 745
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 32
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 11:37:13.9324734 +08:00 Friday L System.Logging.LoggingMonitor[0] #10 '00-9262221a3ab9ec3f3b587dfcb95d1acd-8a56870aec8ade1a-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.ImportData
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       ImportData
      ┣ 显示名称：                       导入会议人员表记录
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: import
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/import
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                 *******
      ┣ 客户端源端口：                   65164
      ┣ 服务端 IP 地址：                 *******
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-9262221a3ab9ec3f3b587dfcb95d1acd-8a56870aec8ade1a-00
      ┣ 服务线程 ID：                    #10
      ┣ 执行耗时：                       329155ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://[::]:5005
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       Admin.NET.Web.Entry
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ nbf (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ exp (integer64)：                ********** (2025-07-25 09:39:59:0000(+08:00) Friday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     keep-alive
      ┣ Host：                           localhost:5005
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryU74BoTuVoiAajbk4
      ┣ Origin：                         http://localhost:8888
      ┣ Referer：                        http://localhost:8888/
      ┣ Content-Length：                 11326
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryU74BoTuVoiAajbk4
      ┣ 
      ┣ file (IFormFile)：               [name]: 会议人员表导入模板-2025-07-18_105704.xlsx; [size]: 11KB; [content-type]: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       Microsoft.AspNetCore.Mvc.IActionResult
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.InvalidOperationException
      ┣ 消息：                           Connection must be Open; current state is Closed
      ┣ 错误堆栈：                       at MySqlConnector.MySqlConnection.get_Session() in /_/src/MySqlConnector/MySqlConnection.cs:line 745
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 32
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Connection must be Open; current state is Closed
         at MySqlConnector.MySqlConnection.get_Session() in /_/src/MySqlConnector/MySqlConnection.cs:line 745
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 32
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 13:59:38.9460835 +08:00 Friday L System.Logging.UnitOfWork[0] #44 '00-e692ee18f8184e981e933ac5bbdfb0ad-ba2accdc43665852-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      [Database Transaction] Transaction rolled back due to an error.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 13:59:39.2371978 +08:00 Friday L System.Logging.LoggingMonitor[0] #44 '00-e692ee18f8184e981e933ac5bbdfb0ad-ba2accdc43665852-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.ImportData
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       ImportData
      ┣ 显示名称：                       导入会议人员表记录
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: import
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/import
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                 *******
      ┣ 客户端源端口：                   58504
      ┣ 服务端 IP 地址：                 *******
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-e692ee18f8184e981e933ac5bbdfb0ad-ba2accdc43665852-00
      ┣ 服务线程 ID：                    #44
      ┣ 执行耗时：                       18308ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://[::]:5005
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       Admin.NET.Web.Entry
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ nbf (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ exp (integer64)：                ********** (2025-07-25 09:39:59:0000(+08:00) Friday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     keep-alive
      ┣ Host：                           localhost:5005
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryhsmCoZN51z0KT7Nw
      ┣ Origin：                         http://localhost:8888
      ┣ Referer：                        http://localhost:8888/
      ┣ Content-Length：                 11326
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryhsmCoZN51z0KT7Nw
      ┣ 
      ┣ file (IFormFile)：               [name]: 会议人员表导入模板-2025-07-18_105704.xlsx; [size]: 11KB; [content-type]: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       Microsoft.AspNetCore.Mvc.IActionResult
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.InvalidOperationException
      ┣ 消息：                           This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ┣ 错误堆栈：                       at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 15:04:52.4113120 +08:00 Friday L System.Logging.UnitOfWork[0] #39 '00-e1388fed9a58d9261690721ff63f7623-73592cc443b46238-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      [Database Transaction] Transaction rolled back due to an error.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 15:04:52.5477493 +08:00 Friday L System.Logging.LoggingMonitor[0] #39 '00-e1388fed9a58d9261690721ff63f7623-73592cc443b46238-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.ImportData
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       ImportData
      ┣ 显示名称：                       导入会议人员表记录
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: import
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/import
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                 *******
      ┣ 客户端源端口：                   49684
      ┣ 服务端 IP 地址：                 *******
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-e1388fed9a58d9261690721ff63f7623-73592cc443b46238-00
      ┣ 服务线程 ID：                    #39
      ┣ 执行耗时：                       16341ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://[::]:5005
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       Admin.NET.Web.Entry
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ nbf (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ exp (integer64)：                ********** (2025-07-25 09:39:59:0000(+08:00) Friday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     keep-alive
      ┣ Host：                           localhost:5005
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryebhDdneKX2L4bQAa
      ┣ Origin：                         http://localhost:8888
      ┣ Referer：                        http://localhost:8888/
      ┣ Content-Length：                 11326
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryebhDdneKX2L4bQAa
      ┣ 
      ┣ file (IFormFile)：               [name]: 会议人员表导入模板-2025-07-18_105704.xlsx; [size]: 11KB; [content-type]: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       Microsoft.AspNetCore.Mvc.IActionResult
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.InvalidOperationException
      ┣ 消息：                           This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ┣ 错误堆栈：                       at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 15:05:05.9215903 +08:00 Friday L System.Logging.UnitOfWork[0] #7 '00-0610d78dc52fd47795e984e5b2bfe413-8e554ac2435beb13-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      [Database Transaction] Transaction rolled back due to an error.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 15:05:06.0402864 +08:00 Friday L System.Logging.LoggingMonitor[0] #7 '00-0610d78dc52fd47795e984e5b2bfe413-8e554ac2435beb13-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.ImportData
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       ImportData
      ┣ 显示名称：                       导入会议人员表记录
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: import
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/import
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                 *******
      ┣ 客户端源端口：                   50073
      ┣ 服务端 IP 地址：                 *******
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-0610d78dc52fd47795e984e5b2bfe413-8e554ac2435beb13-00
      ┣ 服务线程 ID：                    #7
      ┣ 执行耗时：                       10517ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://[::]:5005
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       Admin.NET.Web.Entry
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ nbf (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ exp (integer64)：                ********** (2025-07-25 09:39:59:0000(+08:00) Friday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     keep-alive
      ┣ Host：                           localhost:5005
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryzkU2SosRkYf5AwRI
      ┣ Origin：                         http://localhost:8888
      ┣ Referer：                        http://localhost:8888/
      ┣ Content-Length：                 11326
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryzkU2SosRkYf5AwRI
      ┣ 
      ┣ file (IFormFile)：               [name]: 会议人员表导入模板-2025-07-18_105704.xlsx; [size]: 11KB; [content-type]: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       Microsoft.AspNetCore.Mvc.IActionResult
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.InvalidOperationException
      ┣ 消息：                           This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ┣ 错误堆栈：                       at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 15:39:08.1918105 +08:00 Friday L System.Logging.StringLogging[0] #5 '00-e7a91b76116a751b0eddc7391757dfb3-3c0812d934096753-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-18 15:39:08——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,NULL,@position_2,@department_3,@email_4,@phone_5,@avatar_url_6,NULL,@field1_7,@field2_8,@field3_9,@field4_10,@field5_11,@field6_12,@description_13,N'1',0,'2025-07-18 15:39:07.963',NULL,N'*************',@CreateUserName_14,NULL,NULL,N'699019861778501'),  (NULL,@staff_code_15,NULL,@position_16,@department_17,@email_18,@phone_19,@avatar_url_20,NULL,@field1_21,@field2_22,@field3_23,@field4_24,@field5_25,@field6_26,@description_27,N'1',0,'2025-07-18 15:39:07.964',NULL,N'*************',@CreateUserName_28,NULL,NULL,N'699019861786693') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@position_2 [Value]:测试 [Type]:String    
      [Name]:@department_3 [Value]:测试 [Type]:String    
      [Name]:@email_4 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_5 [Value]:13546947512 [Type]:String    
      [Name]:@avatar_url_6 [Value]:http://**************:19080/=DISPIMG("ID_EA483CE1FA2F4CECAFCBF1F63CEEEB0F",1) [Type]:String    
      [Name]:@field1_7 [Value]: [Type]:String    
      [Name]:@field2_8 [Value]: [Type]:String    
      [Name]:@field3_9 [Value]: [Type]:String    
      [Name]:@field4_10 [Value]: [Type]:String    
      [Name]:@field5_11 [Value]: [Type]:String    
      [Name]:@field6_12 [Value]: [Type]:String    
      [Name]:@description_13 [Value]: [Type]:String    
      [Name]:@CreateUserName_14 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_15 [Value]:20250206 [Type]:String    
      [Name]:@position_16 [Value]:测试 [Type]:String    
      [Name]:@department_17 [Value]:测试 [Type]:String    
      [Name]:@email_18 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_19 [Value]:13546947512 [Type]:String    
      [Name]:@avatar_url_20 [Value]:http://**************:19080/=DISPIMG("ID_5CF31A0E36CC4163ACE81EA3BB79CE3B",1) [Type]:String    
      [Name]:@field1_21 [Value]: [Type]:String    
      [Name]:@field2_22 [Value]: [Type]:String    
      [Name]:@field3_23 [Value]: [Type]:String    
      [Name]:@field4_24 [Value]: [Type]:String    
      [Name]:@field5_25 [Value]: [Type]:String    
      [Name]:@field6_26 [Value]: [Type]:String    
      [Name]:@description_27 [Value]: [Type]:String    
      [Name]:@CreateUserName_28 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 15:39:08.6769044 +08:00 Friday L Admin.NET.Application.MeetingStaffService[0] #5 '00-e7a91b76116a751b0eddc7391757dfb3-3c0812d934096753-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      批量处理导入数据时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      MySqlConnector.MySqlException (0x80004005): Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
         at SqlSugar.AdoProvider.ExecuteCommand(String sql, SugarParameter[] parameters)
         at SqlSugar.InsertableProvider`1.ExecuteCommand()
         at Admin.NET.Application.MeetingStaffService.<ProcessImportDataWithImages>b__25_0(List`1 pageItems) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 732
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 15:43:39.6451713 +08:00 Friday L System.Logging.StringLogging[0] #25 '00-10efdf8fba74d4128e41631cc00c5a27-6eb67d8bc3f268d7-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-18 15:43:39——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,NULL,@position_2,@department_3,@email_4,@phone_5,@avatar_url_6,NULL,@field1_7,@field2_8,@field3_9,@field4_10,@field5_11,@field6_12,@description_13,N'1',0,'2025-07-18 15:43:39.539',NULL,N'*************',@CreateUserName_14,NULL,NULL,N'699020974157893'),  (NULL,@staff_code_15,NULL,@position_16,@department_17,@email_18,@phone_19,@avatar_url_20,NULL,@field1_21,@field2_22,@field3_23,@field4_24,@field5_25,@field6_26,@description_27,N'1',0,'2025-07-18 15:43:39.540',NULL,N'*************',@CreateUserName_28,NULL,NULL,N'699020974161989') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@position_2 [Value]:测试 [Type]:String    
      [Name]:@department_3 [Value]:测试 [Type]:String    
      [Name]:@email_4 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_5 [Value]:13546947512 [Type]:String    
      [Name]:@avatar_url_6 [Value]:http://**************:19080/=DISPIMG("ID_EA483CE1FA2F4CECAFCBF1F63CEEEB0F",1) [Type]:String    
      [Name]:@field1_7 [Value]: [Type]:String    
      [Name]:@field2_8 [Value]: [Type]:String    
      [Name]:@field3_9 [Value]: [Type]:String    
      [Name]:@field4_10 [Value]: [Type]:String    
      [Name]:@field5_11 [Value]: [Type]:String    
      [Name]:@field6_12 [Value]: [Type]:String    
      [Name]:@description_13 [Value]: [Type]:String    
      [Name]:@CreateUserName_14 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_15 [Value]:20250206 [Type]:String    
      [Name]:@position_16 [Value]:测试 [Type]:String    
      [Name]:@department_17 [Value]:测试 [Type]:String    
      [Name]:@email_18 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_19 [Value]:13546947512 [Type]:String    
      [Name]:@avatar_url_20 [Value]:http://**************:19080/=DISPIMG("ID_5CF31A0E36CC4163ACE81EA3BB79CE3B",1) [Type]:String    
      [Name]:@field1_21 [Value]: [Type]:String    
      [Name]:@field2_22 [Value]: [Type]:String    
      [Name]:@field3_23 [Value]: [Type]:String    
      [Name]:@field4_24 [Value]: [Type]:String    
      [Name]:@field5_25 [Value]: [Type]:String    
      [Name]:@field6_26 [Value]: [Type]:String    
      [Name]:@description_27 [Value]: [Type]:String    
      [Name]:@CreateUserName_28 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 15:43:39.8825503 +08:00 Friday L Admin.NET.Application.MeetingStaffService[0] #25 '00-10efdf8fba74d4128e41631cc00c5a27-6eb67d8bc3f268d7-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      批量处理导入数据时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      MySqlConnector.MySqlException (0x80004005): Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
         at SqlSugar.AdoProvider.ExecuteCommand(String sql, SugarParameter[] parameters)
         at SqlSugar.InsertableProvider`1.ExecuteCommand()
         at Admin.NET.Application.MeetingStaffService.<ProcessImportDataWithImages>b__25_0(List`1 pageItems) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 732
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 15:59:51.7468262 +08:00 Friday L System.Logging.StringLogging[0] #37 '00-ced9f222ca3cc181c1dbe49079ceb4b1-5de69d2d482dc570-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-18 15:59:51——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,NULL,@position_2,@department_3,@email_4,@phone_5,@avatar_url_6,NULL,@field1_7,@field2_8,@field3_9,@field4_10,@field5_11,@field6_12,@description_13,N'1',0,'2025-07-18 15:59:51.541',NULL,N'*************',@CreateUserName_14,NULL,NULL,N'699024955478085'),  (NULL,@staff_code_15,NULL,@position_16,@department_17,@email_18,@phone_19,@avatar_url_20,NULL,@field1_21,@field2_22,@field3_23,@field4_24,@field5_25,@field6_26,@description_27,N'1',0,'2025-07-18 15:59:51.541',NULL,N'*************',@CreateUserName_28,NULL,NULL,N'699024955478086') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@position_2 [Value]:测试 [Type]:String    
      [Name]:@department_3 [Value]:测试 [Type]:String    
      [Name]:@email_4 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_5 [Value]:13546947512 [Type]:String    
      [Name]:@avatar_url_6 [Value]: [Type]:String    
      [Name]:@field1_7 [Value]: [Type]:String    
      [Name]:@field2_8 [Value]: [Type]:String    
      [Name]:@field3_9 [Value]: [Type]:String    
      [Name]:@field4_10 [Value]: [Type]:String    
      [Name]:@field5_11 [Value]: [Type]:String    
      [Name]:@field6_12 [Value]: [Type]:String    
      [Name]:@description_13 [Value]: [Type]:String    
      [Name]:@CreateUserName_14 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_15 [Value]:20250206 [Type]:String    
      [Name]:@position_16 [Value]:测试 [Type]:String    
      [Name]:@department_17 [Value]:测试 [Type]:String    
      [Name]:@email_18 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_19 [Value]:13546947512 [Type]:String    
      [Name]:@avatar_url_20 [Value]: [Type]:String    
      [Name]:@field1_21 [Value]: [Type]:String    
      [Name]:@field2_22 [Value]: [Type]:String    
      [Name]:@field3_23 [Value]: [Type]:String    
      [Name]:@field4_24 [Value]: [Type]:String    
      [Name]:@field5_25 [Value]: [Type]:String    
      [Name]:@field6_26 [Value]: [Type]:String    
      [Name]:@description_27 [Value]: [Type]:String    
      [Name]:@CreateUserName_28 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 15:59:52.0854058 +08:00 Friday L Admin.NET.Application.MeetingStaffService[0] #37 '00-ced9f222ca3cc181c1dbe49079ceb4b1-5de69d2d482dc570-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      批量处理导入数据时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      MySqlConnector.MySqlException (0x80004005): Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
         at SqlSugar.AdoProvider.ExecuteCommand(String sql, SugarParameter[] parameters)
         at SqlSugar.InsertableProvider`1.ExecuteCommand()
         at Admin.NET.Application.MeetingStaffService.<ProcessImportDataWithImages>b__27_0(List`1 pageItems) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 866
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 16:01:37.3516034 +08:00 Friday L System.Logging.StringLogging[0] #25 '00-87e246941b7308c4c70aa2fc016a4465-3338dd23917e9d7b-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-18 16:01:37——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,NULL,@position_2,@department_3,@email_4,@phone_5,@avatar_url_6,NULL,@field1_7,@field2_8,@field3_9,@field4_10,@field5_11,@field6_12,@description_13,N'1',0,'2025-07-18 16:01:37.211',NULL,N'*************',@CreateUserName_14,NULL,NULL,N'699025388302405'),  (NULL,@staff_code_15,NULL,@position_16,@department_17,@email_18,@phone_19,@avatar_url_20,NULL,@field1_21,@field2_22,@field3_23,@field4_24,@field5_25,@field6_26,@description_27,N'1',0,'2025-07-18 16:01:37.249',NULL,N'*************',@CreateUserName_28,NULL,NULL,N'699025388458053') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@position_2 [Value]:测试 [Type]:String    
      [Name]:@department_3 [Value]:测试 [Type]:String    
      [Name]:@email_4 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_5 [Value]:13546947512 [Type]:String    
      [Name]:@avatar_url_6 [Value]:upload/2025/07/18/699025388109893.jpg [Type]:String    
      [Name]:@field1_7 [Value]: [Type]:String    
      [Name]:@field2_8 [Value]: [Type]:String    
      [Name]:@field3_9 [Value]: [Type]:String    
      [Name]:@field4_10 [Value]: [Type]:String    
      [Name]:@field5_11 [Value]: [Type]:String    
      [Name]:@field6_12 [Value]: [Type]:String    
      [Name]:@description_13 [Value]: [Type]:String    
      [Name]:@CreateUserName_14 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_15 [Value]:20250206 [Type]:String    
      [Name]:@position_16 [Value]:测试 [Type]:String    
      [Name]:@department_17 [Value]:测试 [Type]:String    
      [Name]:@email_18 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_19 [Value]:13546947512 [Type]:String    
      [Name]:@avatar_url_20 [Value]:upload/2025/07/18/699025388314693.jpg [Type]:String    
      [Name]:@field1_21 [Value]: [Type]:String    
      [Name]:@field2_22 [Value]: [Type]:String    
      [Name]:@field3_23 [Value]: [Type]:String    
      [Name]:@field4_24 [Value]: [Type]:String    
      [Name]:@field5_25 [Value]: [Type]:String    
      [Name]:@field6_26 [Value]: [Type]:String    
      [Name]:@description_27 [Value]: [Type]:String    
      [Name]:@CreateUserName_28 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 16:01:37.5732378 +08:00 Friday L Admin.NET.Application.MeetingStaffService[0] #25 '00-87e246941b7308c4c70aa2fc016a4465-3338dd23917e9d7b-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      批量处理导入数据时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      MySqlConnector.MySqlException (0x80004005): Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
         at SqlSugar.AdoProvider.ExecuteCommand(String sql, SugarParameter[] parameters)
         at SqlSugar.InsertableProvider`1.ExecuteCommand()
         at Admin.NET.Application.MeetingStaffService.<ProcessImportDataWithImages>b__27_0(List`1 pageItems) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 866
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 16:38:48.6408697 +08:00 Friday L System.Logging.StringLogging[0] #24 '00-77d4fa858125fabecb59de8db3883f89-6f0160a073557a56-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-18 16:38:48——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,NULL,@position_2,@department_3,@email_4,@phone_5,@avatar_url_6,NULL,@field1_7,@field2_8,@field3_9,@field4_10,@field5_11,@field6_12,@description_13,N'1',0,'2025-07-18 16:38:48.494',NULL,N'*************',@CreateUserName_14,NULL,NULL,N'699034527637573'),  (NULL,@staff_code_15,NULL,@position_16,@department_17,@email_18,@phone_19,@avatar_url_20,NULL,@field1_21,@field2_22,@field3_23,@field4_24,@field5_25,@field6_26,@description_27,N'1',0,'2025-07-18 16:38:48.494',NULL,N'*************',@CreateUserName_28,NULL,NULL,N'699034527637574') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@position_2 [Value]:测试 [Type]:String    
      [Name]:@department_3 [Value]:测试 [Type]:String    
      [Name]:@email_4 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_5 [Value]:13546947512 [Type]:String    
      [Name]:@avatar_url_6 [Value]: [Type]:String    
      [Name]:@field1_7 [Value]: [Type]:String    
      [Name]:@field2_8 [Value]: [Type]:String    
      [Name]:@field3_9 [Value]: [Type]:String    
      [Name]:@field4_10 [Value]: [Type]:String    
      [Name]:@field5_11 [Value]: [Type]:String    
      [Name]:@field6_12 [Value]: [Type]:String    
      [Name]:@description_13 [Value]: [Type]:String    
      [Name]:@CreateUserName_14 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_15 [Value]:20250206 [Type]:String    
      [Name]:@position_16 [Value]:测试 [Type]:String    
      [Name]:@department_17 [Value]:测试 [Type]:String    
      [Name]:@email_18 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_19 [Value]:13546947512 [Type]:String    
      [Name]:@avatar_url_20 [Value]: [Type]:String    
      [Name]:@field1_21 [Value]: [Type]:String    
      [Name]:@field2_22 [Value]: [Type]:String    
      [Name]:@field3_23 [Value]: [Type]:String    
      [Name]:@field4_24 [Value]: [Type]:String    
      [Name]:@field5_25 [Value]: [Type]:String    
      [Name]:@field6_26 [Value]: [Type]:String    
      [Name]:@description_27 [Value]: [Type]:String    
      [Name]:@CreateUserName_28 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 16:38:49.1082351 +08:00 Friday L Admin.NET.Application.MeetingStaffService[0] #24 '00-77d4fa858125fabecb59de8db3883f89-6f0160a073557a56-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      批量处理导入数据时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      MySqlConnector.MySqlException (0x80004005): Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
         at SqlSugar.AdoProvider.ExecuteCommand(String sql, SugarParameter[] parameters)
         at SqlSugar.InsertableProvider`1.ExecuteCommand()
         at Admin.NET.Application.MeetingStaffService.<ProcessImportDataWithImages>b__27_0(List`1 pageItems) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 866
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 16:39:35.2391228 +08:00 Friday L System.Logging.StringLogging[0] #11 '00-919f01091a9502e6f6b1176f2b98c552-be4c3aa76c8bbbe7-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-18 16:39:35——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,NULL,@position_2,@department_3,@email_4,@phone_5,@avatar_url_6,NULL,@field1_7,@field2_8,@field3_9,@field4_10,@field5_11,@field6_12,@description_13,N'1',0,'2025-07-18 16:39:35.123',NULL,N'*************',@CreateUserName_14,NULL,NULL,N'699034718629957'),  (NULL,@staff_code_15,NULL,@position_16,@department_17,@email_18,@phone_19,@avatar_url_20,NULL,@field1_21,@field2_22,@field3_23,@field4_24,@field5_25,@field6_26,@description_27,N'1',0,'2025-07-18 16:39:35.123',NULL,N'*************',@CreateUserName_28,NULL,NULL,N'699034718629958') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@position_2 [Value]:测试 [Type]:String    
      [Name]:@department_3 [Value]:测试 [Type]:String    
      [Name]:@email_4 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_5 [Value]:13546947512 [Type]:String    
      [Name]:@avatar_url_6 [Value]: [Type]:String    
      [Name]:@field1_7 [Value]: [Type]:String    
      [Name]:@field2_8 [Value]: [Type]:String    
      [Name]:@field3_9 [Value]: [Type]:String    
      [Name]:@field4_10 [Value]: [Type]:String    
      [Name]:@field5_11 [Value]: [Type]:String    
      [Name]:@field6_12 [Value]: [Type]:String    
      [Name]:@description_13 [Value]: [Type]:String    
      [Name]:@CreateUserName_14 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_15 [Value]:20250206 [Type]:String    
      [Name]:@position_16 [Value]:测试 [Type]:String    
      [Name]:@department_17 [Value]:测试 [Type]:String    
      [Name]:@email_18 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_19 [Value]:13546947512 [Type]:String    
      [Name]:@avatar_url_20 [Value]: [Type]:String    
      [Name]:@field1_21 [Value]: [Type]:String    
      [Name]:@field2_22 [Value]: [Type]:String    
      [Name]:@field3_23 [Value]: [Type]:String    
      [Name]:@field4_24 [Value]: [Type]:String    
      [Name]:@field5_25 [Value]: [Type]:String    
      [Name]:@field6_26 [Value]: [Type]:String    
      [Name]:@description_27 [Value]: [Type]:String    
      [Name]:@CreateUserName_28 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 16:39:35.5015096 +08:00 Friday L Admin.NET.Application.MeetingStaffService[0] #11 '00-919f01091a9502e6f6b1176f2b98c552-be4c3aa76c8bbbe7-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      批量处理导入数据时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      MySqlConnector.MySqlException (0x80004005): Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
         at SqlSugar.AdoProvider.ExecuteCommand(String sql, SugarParameter[] parameters)
         at SqlSugar.InsertableProvider`1.ExecuteCommand()
         at Admin.NET.Application.MeetingStaffService.<ProcessImportDataWithImages>b__27_0(List`1 pageItems) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 866
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 16:45:44.9063048 +08:00 Friday L System.Logging.StringLogging[0] #13 '00-5705f965dcbee797bf71b813cecfe5e8-f138439bf8467580-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-18 16:45:44——错误SQL】
      
      [Sql]:SELECT `staff_code` FROM `meeting_staff`  WHERE  (`staff_code` IN ('20250205','20250206'))   AND ( `IsDelete` = @IsDelete1 )  
      [Pars]:
      [Name]:@IsDelete1 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 16:45:45.4137087 +08:00 Friday L Admin.NET.Application.MeetingStaffService[0] #13 '00-5705f965dcbee797bf71b813cecfe5e8-f138439bf8467580-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      批量处理导入数据时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 344
         at MySqlConnector.MySqlCommand.ExecuteDbDataReader(CommandBehavior behavior) in /_/src/MySqlConnector/MySqlCommand.cs:line 278
         at SqlSugar.AdoProvider.GetDataReader(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetData[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToList[TResult]()
         at SqlSugar.QueryableProvider`1.ToList()
         at Admin.NET.Application.MeetingStaffService.<>c__DisplayClass18_0.<ImportData>b__1(List`1 pageItems) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 387
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 17:21:09.7859284 +08:00 Friday L System.Logging.StringLogging[0] #30 '00-3e84e25ad072c9adf1addfe7df7ab7ee-fa392a06e8b5be0d-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-18 17:21:09——错误SQL】
      
      [Sql]:SELECT `staff_code` FROM `meeting_staff`  WHERE  (`staff_code` IN ('20250205','20250206'))   AND ( `IsDelete` = @IsDelete1 )  
      [Pars]:
      [Name]:@IsDelete1 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 17:21:10.3064364 +08:00 Friday L Admin.NET.Application.MeetingStaffService[0] #30 '00-3e84e25ad072c9adf1addfe7df7ab7ee-fa392a06e8b5be0d-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      批量处理导入数据时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 344
         at MySqlConnector.MySqlCommand.ExecuteDbDataReader(CommandBehavior behavior) in /_/src/MySqlConnector/MySqlCommand.cs:line 278
         at SqlSugar.AdoProvider.GetDataReader(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetData[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToList[TResult]()
         at SqlSugar.QueryableProvider`1.ToList()
         at Admin.NET.Application.MeetingStaffService.<>c__DisplayClass18_0.<ImportData>b__1(List`1 pageItems) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 387
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 17:26:08.4502604 +08:00 Friday L System.Logging.StringLogging[0] #27 '00-f1a7a8bef05c900571e7c1da785ef8a5-faaba006d663f466-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-18 17:26:08——错误SQL】
      
      [Sql]:SELECT `staff_code` FROM `meeting_staff`  WHERE  (`staff_code` IN ('20250205','20250206'))   AND ( `IsDelete` = @IsDelete1 )  
      [Pars]:
      [Name]:@IsDelete1 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 17:26:09.0259185 +08:00 Friday L Admin.NET.Application.MeetingStaffService[0] #27 '00-f1a7a8bef05c900571e7c1da785ef8a5-faaba006d663f466-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      批量处理导入数据时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 344
         at MySqlConnector.MySqlCommand.ExecuteDbDataReader(CommandBehavior behavior) in /_/src/MySqlConnector/MySqlCommand.cs:line 278
         at SqlSugar.AdoProvider.GetDataReader(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetData[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToList[TResult]()
         at SqlSugar.QueryableProvider`1.ToList()
         at Admin.NET.Application.MeetingStaffService.<>c__DisplayClass18_0.<ImportData>b__1(List`1 pageItems) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 387
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
