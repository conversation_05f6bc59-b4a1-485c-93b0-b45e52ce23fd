﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！
using Magicodes.ExporterAndImporter.Core;
namespace Admin.NET.Application;

/// <summary>
/// 蓝牙桌牌设备输出参数
/// </summary>
public class DevicesOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }    
    
    /// <summary>
    /// 网关ID
    /// </summary>
    public long? ap_id { get; set; }    
    
    /// <summary>
    /// 设备ID
    /// </summary>
    public string device_id { get; set; }    
    
    /// <summary>
    /// 设备名称
    /// </summary>
    public string? device_name { get; set; }    
    
    /// <summary>
    /// MAC地址
    /// </summary>
    public string? mac_address { get; set; }    
    
    /// <summary>
    /// 1：姓名桌牌；2：价格标签
    /// </summary>
    public int? device_type { get; set; }    
    
    /// <summary>
    /// 1：在线；2离线；3：绑定；4:异常
    /// </summary>
    public int? status { get; set; }    
    
    /// <summary>
    /// 电量
    /// </summary>
    public int? battery_level { get; set; }    
    
    /// <summary>
    /// 固件版本
    /// </summary>
    public string? firmware_version { get; set; }    
    
    /// <summary>
    /// 绑定租户Id
    /// </summary>
    public long? BindTenantId { get; set; }    
    
    /// <summary>
    /// 绑定用户Id
    /// </summary>
    public long? BindUserId { get; set; }    
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool IsDelete { get; set; }    
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }    
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }    
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }    
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }    
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }    
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }    
    
}
