﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Mapster;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Application.Entity;
using Admin.NET.Plugin.GreenDisplay.Service;
using Microsoft.Extensions.Logging;
namespace Admin.NET.Application;

/// <summary>
/// 蓝牙桌牌设备服务 🧩
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public partial class DevicesService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<Devices> _devicesRep;
    private readonly GreenDisplayService _greenDisplayService;
    private readonly ILogger<DevicesService> _logger;
     
    public DevicesService(SqlSugarRepository<Devices> devicesRep, GreenDisplayService greenDisplayService, ILogger<DevicesService> logger)
    {
        _devicesRep = devicesRep;
        _greenDisplayService = greenDisplayService;
        _logger = logger;
    }

    /// <summary>
    /// 分页查询蓝牙桌牌设备 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询蓝牙桌牌设备")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<DevicesOutput>> Page(PageDevicesInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _devicesRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.device_name.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.device_name), u => u.device_name.Contains(input.device_name.Trim()))
            .WhereIF(input.device_type != null, u => u.device_type == input.device_type)
            .WhereIF(input.status != null, u => u.status == input.status)
            .Select<DevicesOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取蓝牙桌牌设备详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取蓝牙桌牌设备详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<Devices> Detail([FromQuery] QueryByIdDevicesInput input)
    {
        return await _devicesRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加蓝牙桌牌设备 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加蓝牙桌牌设备")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddDevicesInput input)
    {
        var entity = input.Adapt<Devices>();

        return await _devicesRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新蓝牙桌牌设备 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新蓝牙桌牌设备")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateDevicesInput input)
    {
        var entity = input.Adapt<Devices>();
        await _devicesRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除蓝牙桌牌设备 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除蓝牙桌牌设备")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteDevicesInput input)
    {
        var entity = await _devicesRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _devicesRep.FakeDeleteAsync(entity);   //假删除
        //await _devicesRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除蓝牙桌牌设备 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除蓝牙桌牌设备")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteDevicesInput> input)
    {
        var exp = Expressionable.Create<Devices>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _devicesRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _devicesRep.FakeDeleteAsync(list);   //假删除
        //return await _devicesRep.DeleteAsync(list);   //真删除
    }
}
