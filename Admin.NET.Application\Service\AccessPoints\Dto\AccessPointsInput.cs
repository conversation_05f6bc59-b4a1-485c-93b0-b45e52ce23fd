﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

namespace Admin.NET.Application;

/// <summary>
/// 接入点/网关表基础输入参数
/// </summary>
public class AccessPointsBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// AP名称
    /// </summary>
    public virtual string? ap_name { get; set; }
    
    /// <summary>
    /// 物理地址
    /// </summary>
    public virtual string? mac_address { get; set; }
    
    /// <summary>
    /// IP地址
    /// </summary>
    public virtual string? ip_address { get; set; }
    
    /// <summary>
    /// ap地址
    /// </summary>
    public virtual string? ap_location { get; set; }
    
    /// <summary>
    /// ap状态1：在线；2：离线；3：异常；
    /// </summary>
    public virtual int? ap_status { get; set; }
    
    /// <summary>
    /// 固件版本
    /// </summary>
    public virtual string? firmware_version { get; set; }
    
    /// <summary>
    /// 信号强度
    /// </summary>
    public virtual int? signal_strength { get; set; }
    
    /// <summary>
    /// 链接设备连接数
    /// </summary>
    public virtual int? connected_devices_count { get; set; }
    
    /// <summary>
    /// 最大链接数
    /// </summary>
    public virtual int? max_devices { get; set; }
    
}

/// <summary>
/// 接入点/网关表分页查询输入参数
/// </summary>
public class PageAccessPointsInput : BasePageInput
{
    /// <summary>
    /// AP名称
    /// </summary>
    public string? ap_name { get; set; }
    
    /// <summary>
    /// ap地址
    /// </summary>
    public string? ap_location { get; set; }
    
    /// <summary>
    /// ap状态1：在线；2：离线；3：异常；
    /// </summary>
    public int? ap_status { get; set; }
    
}

/// <summary>
/// 接入点/网关表增加输入参数
/// </summary>
public class AddAccessPointsInput
{
    /// <summary>
    /// AP名称
    /// </summary>
    [MaxLength(200, ErrorMessage = "AP名称字符长度不能超过200")]
    public string? ap_name { get; set; }
    
    /// <summary>
    /// 物理地址
    /// </summary>
    [MaxLength(17, ErrorMessage = "物理地址字符长度不能超过17")]
    public string? mac_address { get; set; }
    
    /// <summary>
    /// IP地址
    /// </summary>
    [MaxLength(15, ErrorMessage = "IP地址字符长度不能超过15")]
    public string? ip_address { get; set; }
    
    /// <summary>
    /// ap地址
    /// </summary>
    [MaxLength(300, ErrorMessage = "ap地址字符长度不能超过300")]
    public string? ap_location { get; set; }
    
    /// <summary>
    /// ap状态1：在线；2：离线；3：异常；
    /// </summary>
    public int? ap_status { get; set; }
    
    /// <summary>
    /// 固件版本
    /// </summary>
    [MaxLength(50, ErrorMessage = "固件版本字符长度不能超过50")]
    public string? firmware_version { get; set; }
    
    /// <summary>
    /// 信号强度
    /// </summary>
    public int? signal_strength { get; set; }
    
    /// <summary>
    /// 链接设备连接数
    /// </summary>
    public int? connected_devices_count { get; set; }
    
    /// <summary>
    /// 最大链接数
    /// </summary>
    public int? max_devices { get; set; }
    
}

/// <summary>
/// 接入点/网关表删除输入参数
/// </summary>
public class DeleteAccessPointsInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 接入点/网关表更新输入参数
/// </summary>
public class UpdateAccessPointsInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// AP名称
    /// </summary>    
    [MaxLength(200, ErrorMessage = "AP名称字符长度不能超过200")]
    public string? ap_name { get; set; }
    
    /// <summary>
    /// 物理地址
    /// </summary>    
    [MaxLength(17, ErrorMessage = "物理地址字符长度不能超过17")]
    public string? mac_address { get; set; }
    
    /// <summary>
    /// IP地址
    /// </summary>    
    [MaxLength(15, ErrorMessage = "IP地址字符长度不能超过15")]
    public string? ip_address { get; set; }
    
    /// <summary>
    /// ap地址
    /// </summary>    
    [MaxLength(300, ErrorMessage = "ap地址字符长度不能超过300")]
    public string? ap_location { get; set; }
    
    /// <summary>
    /// ap状态1：在线；2：离线；3：异常；
    /// </summary>    
    public int? ap_status { get; set; }
    
    /// <summary>
    /// 固件版本
    /// </summary>    
    [MaxLength(50, ErrorMessage = "固件版本字符长度不能超过50")]
    public string? firmware_version { get; set; }
    
    /// <summary>
    /// 信号强度
    /// </summary>    
    public int? signal_strength { get; set; }
    
    /// <summary>
    /// 链接设备连接数
    /// </summary>    
    public int? connected_devices_count { get; set; }
    
    /// <summary>
    /// 最大链接数
    /// </summary>    
    public int? max_devices { get; set; }
    
}

/// <summary>
/// 接入点/网关表主键查询输入参数
/// </summary>
public class QueryByIdAccessPointsInput : DeleteAccessPointsInput
{
}

