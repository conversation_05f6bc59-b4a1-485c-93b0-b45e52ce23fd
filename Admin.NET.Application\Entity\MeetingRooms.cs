﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using SqlSugar;
namespace Admin.NET.Application.Entity;

/// <summary>
/// 会议室表
/// </summary>
[Tenant("1300000000001")]
[SugarTable("meeting_rooms", "会议室表")]
public partial class MeetingRooms : EntityBaseDel
{
    /// <summary>
    /// 房间名称
    /// </summary>
    [SugarColumn(ColumnName = "room_id", ColumnDescription = "平台ID", Length = 200)]
    public virtual int? room_id { get; set; }

    /// <summary>
    /// 房间名称
    /// </summary>
    [SugarColumn(ColumnName = "room_name", ColumnDescription = "房间名称", Length = 200)]
    public virtual string? room_name { get; set; }
    
    /// <summary>
    /// 房间编号
    /// </summary>
    /// 
    [SugarColumn(ColumnName = "room_code", ColumnDescription = "房间编号", Length = 50)]
    public virtual string? room_code { get; set; }
    
    /// <summary>
    /// 房间位置
    /// </summary>
    [SugarColumn(ColumnName = "room_location", ColumnDescription = "房间位置", Length = 300)]
    public virtual string? room_location { get; set; }
    
    /// <summary>
    /// 会议室容量
    /// </summary>
    [SugarColumn(ColumnName = "capacity", ColumnDescription = "会议室容量")]
    public virtual int? capacity { get; set; }
    
    /// <summary>
    /// 绑定设备
    /// </summary>
    [SugarColumn(ColumnName = "equipment", ColumnDescription = "绑定设备")]
    public virtual object? equipment { get; set; }
    
    /// <summary>
    /// 房间状态1：'可用',2：'占用'，3：'维护‘
    /// </summary>
    [SugarColumn(ColumnName = "meeting_status", ColumnDescription = "房间状态1：'可用',2：'占用'，3：'维护‘", DefaultValue = "1")]
    public virtual int? meeting_status { get; set; }
    
}
