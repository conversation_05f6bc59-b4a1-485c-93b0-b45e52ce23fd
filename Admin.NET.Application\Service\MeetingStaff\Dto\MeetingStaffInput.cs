// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

namespace Admin.NET.Application;

/// <summary>
/// 会议人员表基础输入参数
/// </summary>
public class MeetingStaffBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 员工编号
    /// </summary>
    public virtual string? staff_code { get; set; }
    
    /// <summary>
    /// 员工姓名
    /// </summary>
    public virtual string? staff_name { get; set; }
    
    /// <summary>
    /// 职位
    /// </summary>
    public virtual string? position { get; set; }
    
    /// <summary>
    /// 部门
    /// </summary>
    public virtual string? department { get; set; }
    
    /// <summary>
    /// 邮箱
    /// </summary>
    public virtual string? email { get; set; }
    
    /// <summary>
    /// 手机号
    /// </summary>
    public virtual string? phone { get; set; }
    
    /// <summary>
    /// 头像
    /// </summary>
    public virtual string? avatar_url { get; set; }
    
    /// <summary>
    /// 绑定会议室ID
    /// </summary>
    public virtual long? meeting_room_id { get; set; }
    
    /// <summary>
    /// 自定义字段1
    /// </summary>
    public virtual string? field1 { get; set; }
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    public virtual string? field2 { get; set; }
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    public virtual string? field3 { get; set; }
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    public virtual string? field4 { get; set; }
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    public virtual string? field5 { get; set; }
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    public virtual string? field6 { get; set; }
    
    /// <summary>
    /// 描述
    /// </summary>
    public virtual string? description { get; set; }
    
}

/// <summary>
/// 会议人员表分页查询输入参数
/// </summary>
public class PageMeetingStaffInput : BasePageInput
{
    /// <summary>
    /// 员工编号
    /// </summary>
    public string? staff_code { get; set; }
    
    /// <summary>
    /// 员工姓名
    /// </summary>
    public string? staff_name { get; set; }
    
    /// <summary>
    /// 职位
    /// </summary>
    public string? position { get; set; }
    
    /// <summary>
    /// 部门
    /// </summary>
    public string? department { get; set; }
    
    /// <summary>
    /// 邮箱
    /// </summary>
    public string? email { get; set; }
    
    /// <summary>
    /// 手机号
    /// </summary>
    public string? phone { get; set; }
    
    /// <summary>
    /// 头像
    /// </summary>
    public string? avatar_url { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 会议人员表增加输入参数
/// </summary>
public class AddMeetingStaffInput
{
    /// <summary>
    /// 员工编号
    /// </summary>
    [Required(ErrorMessage = "员工编号不能为空")]
    [MaxLength(50, ErrorMessage = "员工编号字符长度不能超过50")]
    public string? staff_code { get; set; }
    
    /// <summary>
    /// 员工姓名
    /// </summary>
    [MaxLength(100, ErrorMessage = "员工姓名字符长度不能超过100")]
    public string? staff_name { get; set; }
    
    /// <summary>
    /// 职位
    /// </summary>
    [MaxLength(100, ErrorMessage = "职位字符长度不能超过100")]
    public string? position { get; set; }
    
    /// <summary>
    /// 部门
    /// </summary>
    [MaxLength(100, ErrorMessage = "部门字符长度不能超过100")]
    public string? department { get; set; }
    
    /// <summary>
    /// 邮箱
    /// </summary>
    [MaxLength(100, ErrorMessage = "邮箱字符长度不能超过100")]
    public string? email { get; set; }
    
    /// <summary>
    /// 手机号
    /// </summary>
    [MaxLength(20, ErrorMessage = "手机号字符长度不能超过20")]
    public string? phone { get; set; }
    
    /// <summary>
    /// 头像
    /// </summary>
    [MaxLength(500, ErrorMessage = "头像字符长度不能超过500")]
    public string? avatar_url { get; set; }
    
    /// <summary>
    /// 绑定会议室ID
    /// </summary>
    public long? meeting_room_id { get; set; }
    
    /// <summary>
    /// 自定义字段1
    /// </summary>
    [MaxLength(255, ErrorMessage = "自定义字段1字符长度不能超过255")]
    public string? field1 { get; set; }
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    [MaxLength(255, ErrorMessage = "自定义字段2字符长度不能超过255")]
    public string? field2 { get; set; }
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    [MaxLength(255, ErrorMessage = "自定义字段2字符长度不能超过255")]
    public string? field3 { get; set; }
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    [MaxLength(255, ErrorMessage = "自定义字段2字符长度不能超过255")]
    public string? field4 { get; set; }
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    [MaxLength(255, ErrorMessage = "自定义字段2字符长度不能超过255")]
    public string? field5 { get; set; }
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    [MaxLength(255, ErrorMessage = "自定义字段2字符长度不能超过255")]
    public string? field6 { get; set; }
    
    /// <summary>
    /// 描述
    /// </summary>
    [MaxLength(255, ErrorMessage = "描述字符长度不能超过255")]
    public string? description { get; set; }
    
}

/// <summary>
/// 会议人员表删除输入参数
/// </summary>
public class DeleteMeetingStaffInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 会议人员表更新输入参数
/// </summary>
public class UpdateMeetingStaffInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 员工编号
    /// </summary>    
    [Required(ErrorMessage = "员工编号不能为空")]
    [MaxLength(50, ErrorMessage = "员工编号字符长度不能超过50")]
    public string? staff_code { get; set; }
    
    /// <summary>
    /// 员工姓名
    /// </summary>    
    [MaxLength(100, ErrorMessage = "员工姓名字符长度不能超过100")]
    public string? staff_name { get; set; }
    
    /// <summary>
    /// 职位
    /// </summary>    
    [MaxLength(100, ErrorMessage = "职位字符长度不能超过100")]
    public string? position { get; set; }
    
    /// <summary>
    /// 部门
    /// </summary>    
    [MaxLength(100, ErrorMessage = "部门字符长度不能超过100")]
    public string? department { get; set; }
    
    /// <summary>
    /// 邮箱
    /// </summary>    
    [MaxLength(100, ErrorMessage = "邮箱字符长度不能超过100")]
    public string? email { get; set; }
    
    /// <summary>
    /// 手机号
    /// </summary>    
    [MaxLength(20, ErrorMessage = "手机号字符长度不能超过20")]
    public string? phone { get; set; }
    
    /// <summary>
    /// 头像
    /// </summary>    
    [MaxLength(500, ErrorMessage = "头像字符长度不能超过500")]
    public string? avatar_url { get; set; }
    
    /// <summary>
    /// 绑定会议室ID
    /// </summary>    
    public long? meeting_room_id { get; set; }
    
    /// <summary>
    /// 自定义字段1
    /// </summary>    
    [MaxLength(255, ErrorMessage = "自定义字段1字符长度不能超过255")]
    public string? field1 { get; set; }
    
    /// <summary>
    /// 自定义字段2
    /// </summary>    
    [MaxLength(255, ErrorMessage = "自定义字段2字符长度不能超过255")]
    public string? field2 { get; set; }
    
    /// <summary>
    /// 自定义字段3
    /// </summary>    
    [MaxLength(255, ErrorMessage = "自定义字段3字符长度不能超过255")]
    public string? field3 { get; set; }
    
    /// <summary>
    /// 自定义字段4
    /// </summary>    
    [MaxLength(255, ErrorMessage = "自定义字段4字符长度不能超过255")]
    public string? field4 { get; set; }
    
    /// <summary>
    /// 自定义字段5
    /// </summary>    
    [MaxLength(255, ErrorMessage = "自定义字段5字符长度不能超过255")]
    public string? field5 { get; set; }
    
    /// <summary>
    /// 自定义字段6
    /// </summary>    
    [MaxLength(255, ErrorMessage = "自定义字段6字符长度不能超过255")]
    public string? field6 { get; set; }
    
    /// <summary>
    /// 描述
    /// </summary>    
    [MaxLength(255, ErrorMessage = "描述字符长度不能超过255")]
    public string? description { get; set; }
    
}

/// <summary>
/// 会议人员表主键查询输入参数
/// </summary>
public class QueryByIdMeetingStaffInput : DeleteMeetingStaffInput
{
}

/// <summary>
/// 会议人员表数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportMeetingStaffInput : BaseImportInput
{
    /// <summary>
    /// 员工编号
    /// </summary>
    [ImporterHeader(Name = "员工编号")]
    [ExporterHeader("员工编号", Format = "", Width = 25, IsBold = true)]
    public string? staff_code { get; set; }
    
    /// <summary>
    /// 员工姓名
    /// </summary>
    [ImporterHeader(Name = "员工姓名")]
    [ExporterHeader("员工姓名", Format = "", Width = 25, IsBold = true)]
    public string? staff_name { get; set; }
    
    /// <summary>
    /// 职位
    /// </summary>
    [ImporterHeader(Name = "职位")]
    [ExporterHeader("职位", Format = "", Width = 25, IsBold = true)]
    public string? position { get; set; }
    
    /// <summary>
    /// 部门
    /// </summary>
    [ImporterHeader(Name = "部门")]
    [ExporterHeader("部门", Format = "", Width = 25, IsBold = true)]
    public string? department { get; set; }
    
    /// <summary>
    /// 邮箱
    /// </summary>
    [ImporterHeader(Name = "邮箱")]
    [ExporterHeader("邮箱", Format = "", Width = 25, IsBold = true)]
    public string? email { get; set; }
    
    /// <summary>
    /// 手机号
    /// </summary>
    [ImporterHeader(Name = "手机号")]
    [ExporterHeader("手机号", Format = "", Width = 25, IsBold = true)]
    public string? phone { get; set; }
    
    /// <summary>
    /// 头像
    /// </summary>
    [ImporterHeader(Name = "头像")]
    [ExporterHeader("头像", Format = "", Width = 25, IsBold = true)]
    public string? avatar_url { get; set; }

    /// <summary>
    /// 绑定会议室ID
    /// </summary>
    [ImporterHeader(Name = "绑定会议室ID")]
    [ExporterHeader("绑定会议室ID", Format = "", Width = 25, IsBold = true)]
    public long? meeting_room_id { get; set; }

    /// <summary>
    /// 自定义字段1
    /// </summary>
    [ImporterHeader(Name = "自定义字段1")]
    [ExporterHeader("自定义字段1", Format = "", Width = 25, IsBold = true)]
    public string? field1 { get; set; }
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    [ImporterHeader(Name = "自定义字段2")]
    [ExporterHeader("自定义字段2", Format = "", Width = 25, IsBold = true)]
    public string? field2 { get; set; }
    
    /// <summary>
    /// 自定义字段3
    /// </summary>
    [ImporterHeader(Name = "自定义字段3")]
    [ExporterHeader("自定义字段3", Format = "", Width = 25, IsBold = true)]
    public string? field3 { get; set; }
    
    /// <summary>
    /// 自定义字段4
    /// </summary>
    [ImporterHeader(Name = "自定义字段4")]
    [ExporterHeader("自定义字段4", Format = "", Width = 25, IsBold = true)]
    public string? field4 { get; set; }
    
    /// <summary>
    /// 自定义字段5
    /// </summary>
    [ImporterHeader(Name = "自定义字段5")]
    [ExporterHeader("自定义字段5", Format = "", Width = 25, IsBold = true)]
    public string? field5 { get; set; }
    
    /// <summary>
    /// 自定义字段6
    /// </summary>
    [ImporterHeader(Name = "自定义字段6")]
    [ExporterHeader("自定义字段6", Format = "", Width = 25, IsBold = true)]
    public string? field6 { get; set; }
    
    /// <summary>
    /// 描述
    /// </summary>
    [ImporterHeader(Name = "描述")]
    [ExporterHeader("描述", Format = "", Width = 25, IsBold = true)]
    public string? description { get; set; }
    
}
