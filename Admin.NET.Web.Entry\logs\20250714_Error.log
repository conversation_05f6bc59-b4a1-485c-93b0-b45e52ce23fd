fail: 2025-07-14 10:08:43.2687903 +08:00 Monday L Admin.NET.Application.MeetingRoomsService[0] #31 '00-ded3989a785201afad19d44f02be455d-647e534645523b2f-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      会议室的第三方平台ID为空，无法同步到第三方平台，会议室ID: 696559284789317
fail: 2025-07-14 10:08:43.6103341 +08:00 Monday L Admin.NET.Application.MeetingRoomsService[0] #31 '00-ded3989a785201afad19d44f02be455d-647e534645523b2f-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      第三方平台同步失败，会议室ID: 696559284789317, 名称: 测试001      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Exception: 第三方平台ID为空，无法同步
         at Admin.NET.Application.MeetingRoomsService.<>c__DisplayClass7_0.<<Update>b__0>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingRooms\MeetingRoomsService.cs:line 169
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-14 10:08:43.8648373 +08:00 Monday L Admin.NET.Application.MeetingRoomsService[0] #10 '00-9501539d8f53012f1f2db512ad807992-cb3593138ab6bafc-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      会议室的第三方平台ID为空，无法同步到第三方平台，会议室ID: 696559284789317
fail: 2025-07-14 10:08:44.0838690 +08:00 Monday L Admin.NET.Application.MeetingRoomsService[0] #10 '00-9501539d8f53012f1f2db512ad807992-cb3593138ab6bafc-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      第三方平台同步失败，会议室ID: 696559284789317, 名称: 测试001      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Exception: 第三方平台ID为空，无法同步
         at Admin.NET.Application.MeetingRoomsService.<>c__DisplayClass7_0.<<Update>b__0>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingRooms\MeetingRoomsService.cs:line 169
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-14 10:08:44.0675715 +08:00 Monday L Admin.NET.Application.MeetingRoomsService[0] #31 '00-ded3989a785201afad19d44f02be455d-647e534645523b2f-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      更新会议室事务失败，错误信息: 第三方平台ID为空，无法同步
fail: 2025-07-14 10:08:44.2867552 +08:00 Monday L Admin.NET.Application.MeetingRoomsService[0] #24 '00-974dc19e8cac7921d5cd0e4a5154e0c7-964755fcbb29d736-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      会议室的第三方平台ID为空，无法同步到第三方平台，会议室ID: 696559284789317
fail: 2025-07-14 10:08:44.3237252 +08:00 Monday L Admin.NET.Application.MeetingRoomsService[0] #24 '00-974dc19e8cac7921d5cd0e4a5154e0c7-964755fcbb29d736-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      第三方平台同步失败，会议室ID: 696559284789317, 名称: 测试001      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Exception: 第三方平台ID为空，无法同步
         at Admin.NET.Application.MeetingRoomsService.<>c__DisplayClass7_0.<<Update>b__0>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingRooms\MeetingRoomsService.cs:line 169
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-14 10:08:44.4070734 +08:00 Monday L Admin.NET.Application.MeetingRoomsService[0] #10 '00-9501539d8f53012f1f2db512ad807992-cb3593138ab6bafc-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      更新会议室事务失败，错误信息: 第三方平台ID为空，无法同步
fail: 2025-07-14 10:08:44.5702298 +08:00 Monday L Admin.NET.Application.MeetingRoomsService[0] #24 '00-974dc19e8cac7921d5cd0e4a5154e0c7-964755fcbb29d736-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      更新会议室事务失败，错误信息: 第三方平台ID为空，无法同步
fail: 2025-07-14 10:08:54.3818727 +08:00 Monday L Admin.NET.Application.MeetingRoomsService[0] #10 '00-a75a459f7b413d6ec03e80d878dc6d60-a8c1bb32465fef4c-00'
      [Admin.NET.Application.dll] async Task<bool> Admin.NET.Application.MeetingRoomsService.Update(UpdateMeetingRoomsInput input)+(?) => { }
      会议室的第三方平台ID为空，无法同步到第三方平台，会议室ID: 696559284789317
fail: 2025-07-14 10:08:54.4135284 +08:00 Monday L Admin.NET.Application.MeetingRoomsService[0] #10 '00-a75a459f7b413d6ec03e80d878dc6d60-a8c1bb32465fef4c-00'
      [Admin.NET.Application.dll] async Task<bool> Admin.NET.Application.MeetingRoomsService.Update(UpdateMeetingRoomsInput input)+(?) => { }
      第三方平台同步失败，会议室ID: 696559284789317, 名称: 测试01      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Exception: 第三方平台ID为空，无法同步
         at Admin.NET.Application.MeetingRoomsService.<>c__DisplayClass7_0.<<Update>b__0>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingRooms\MeetingRoomsService.cs:line 169
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-14 10:08:54.5365801 +08:00 Monday L Admin.NET.Application.MeetingRoomsService[0] #10 '00-a75a459f7b413d6ec03e80d878dc6d60-a8c1bb32465fef4c-00'
      [Admin.NET.Application.dll] async Task<bool> Admin.NET.Application.MeetingRoomsService.Update(UpdateMeetingRoomsInput input)
      更新会议室事务失败，错误信息: 第三方平台ID为空，无法同步
fail: 2025-07-14 10:12:32.6490340 +08:00 Monday L Admin.NET.Application.MeetingRoomsService[0] #22 '00-ce2804a6786c8535eef3e895abb6a1b7-5f73a0f99d7fec27-00'
      [Admin.NET.Application.dll] async Task<bool> Admin.NET.Application.MeetingRoomsService.Update(UpdateMeetingRoomsInput input)+(?) => { }
      会议室的第三方平台ID为空，无法同步到第三方平台，会议室ID: 696559284789317
