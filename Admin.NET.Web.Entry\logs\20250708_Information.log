fail: 2025-07-08 14:33:47.5739738 +08:00 Tuesday L System.Logging.LoggingMonitor[0] #16 '00-f70c6db17c7c4d86b815f9cf4c2dcd1c-466b44ff3e2d3b55-00'
      [Furion.Pure.dll] async Task LoggingMonitorAttribute.MonitorAsync(MethodInfo actionMethod, IDictionary<string, object> parameterValues, FilterContext context, dynamic next)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Core.Service.SysUpdateService.Update
      ┣ 
      ┣ 控制器名称：                     SysUpdateService
      ┣ 操作名称：                       Update
      ┣ 显示名称：                       系统更新
      ┣ 路由信息：                       [area]: ; [controller]: sysUpdate; [action]: update
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/sysUpdate/update
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://127.0.0.1:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-cn
      ┣ 客户端 IP 地址：                 *******
      ┣ 客户端源端口：                   58770
      ┣ 服务端 IP 地址：                 *******
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-f70c6db17c7c4d86b815f9cf4c2dcd1c-466b44ff3e2d3b55-00
      ┣ 服务线程 ID：                    #16
      ┣ 执行耗时：                       292ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://localhost:5005/
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       iisexpress
      ┣ 托管程序：                       iisexpress
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.M9-jfd-R1ZgJFfU-SADbajUmaMdSm7AKI4QNRxvcgaY
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-08 14:33:04:0000(+08:00) Tuesday L)
      ┣ nbf (integer64)：                ********** (2025-07-08 14:33:04:0000(+08:00) Tuesday L)
      ┣ exp (integer64)：                ********** (2025-07-15 14:33:04:0000(+08:00) Tuesday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-cn
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.M9-jfd-R1ZgJFfU-SADbajUmaMdSm7AKI4QNRxvcgaY
      ┣ Connection：                     keep-alive
      ┣ Content-Length：                 0
      ┣ Host：                           localhost:5005
      ┣ Referer：                        http://127.0.0.1:8888/
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Origin：                         http://127.0.0.1:8888
      ┣ Sec-Fetch-Site：                 cross-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.Net.Http.HttpRequestException
      ┣ 消息：                           Response status code does not indicate success: 401 (Unauthorized).
      ┣ 错误堆栈：                       at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
         at System.Net.Http.HttpClient.GetStreamAsyncCore(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Core.GiteeHelper.DownloadRepoZip(String owner, String repo, String accessToken, String ref) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Utils\GiteeHelper.cs:line 33
         at Admin.NET.Core.Service.SysUpdateService.Update() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Update\SysUpdateService.cs:line 122
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: Response status code does not indicate success: 401 (Unauthorized).
         at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
         at System.Net.Http.HttpClient.GetStreamAsyncCore(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Core.GiteeHelper.DownloadRepoZip(String owner, String repo, String accessToken, String ref) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Utils\GiteeHelper.cs:line 33
         at Admin.NET.Core.Service.SysUpdateService.Update() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Update\SysUpdateService.cs:line 122
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-08 14:33:54.8244205 +08:00 Tuesday L System.Logging.LoggingMonitor[0] #48 '00-e1f9cfa4e51f0e0030b05963ef8e2444-c988c30579bf2662-00'
      [Furion.Pure.dll] async Task LoggingMonitorAttribute.MonitorAsync(MethodInfo actionMethod, IDictionary<string, object> parameterValues, FilterContext context, dynamic next)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Core.Service.SysUpdateService.Update
      ┣ 
      ┣ 控制器名称：                     SysUpdateService
      ┣ 操作名称：                       Update
      ┣ 显示名称：                       系统更新
      ┣ 路由信息：                       [area]: ; [controller]: sysUpdate; [action]: update
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/sysUpdate/update
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://127.0.0.1:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-cn
      ┣ 客户端 IP 地址：                 *******
      ┣ 客户端源端口：                   58770
      ┣ 服务端 IP 地址：                 *******
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-e1f9cfa4e51f0e0030b05963ef8e2444-c988c30579bf2662-00
      ┣ 服务线程 ID：                    #48
      ┣ 执行耗时：                       131ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://localhost:5005/
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       iisexpress
      ┣ 托管程序：                       iisexpress
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.M9-jfd-R1ZgJFfU-SADbajUmaMdSm7AKI4QNRxvcgaY
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-08 14:33:04:0000(+08:00) Tuesday L)
      ┣ nbf (integer64)：                ********** (2025-07-08 14:33:04:0000(+08:00) Tuesday L)
      ┣ exp (integer64)：                ********** (2025-07-15 14:33:04:0000(+08:00) Tuesday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-cn
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.M9-jfd-R1ZgJFfU-SADbajUmaMdSm7AKI4QNRxvcgaY
      ┣ Connection：                     keep-alive
      ┣ Content-Length：                 0
      ┣ Host：                           localhost:5005
      ┣ Referer：                        http://127.0.0.1:8888/
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Origin：                         http://127.0.0.1:8888
      ┣ Sec-Fetch-Site：                 cross-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.Net.Http.HttpRequestException
      ┣ 消息：                           Response status code does not indicate success: 401 (Unauthorized).
      ┣ 错误堆栈：                       at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
         at System.Net.Http.HttpClient.GetStreamAsyncCore(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Core.GiteeHelper.DownloadRepoZip(String owner, String repo, String accessToken, String ref) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Utils\GiteeHelper.cs:line 33
         at Admin.NET.Core.Service.SysUpdateService.Update() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Update\SysUpdateService.cs:line 122
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Net.Http.HttpRequestException: Response status code does not indicate success: 401 (Unauthorized).
         at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
         at System.Net.Http.HttpClient.GetStreamAsyncCore(HttpRequestMessage request, CancellationToken cancellationToken)
         at Admin.NET.Core.GiteeHelper.DownloadRepoZip(String owner, String repo, String accessToken, String ref) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Utils\GiteeHelper.cs:line 33
         at Admin.NET.Core.Service.SysUpdateService.Update() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Update\SysUpdateService.cs:line 122
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-08 14:34:32.1502763 +08:00 Tuesday L System.Logging.LoggingMonitor[0] #48 '00-25bcefb295b74062d80d93598eaa071e-149ac7e986890c71-00'
      [Furion.Pure.dll] async Task LoggingMonitorAttribute.MonitorAsync(MethodInfo actionMethod, IDictionary<string, object> parameterValues, FilterContext context, dynamic next)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Core.Service.SysRegionService.Sync
      ┣ 
      ┣ 控制器名称：                     SysRegionService
      ┣ 操作名称：                       Sync
      ┣ 显示名称：                       同步行政区域
      ┣ 路由信息：                       [area]: ; [controller]: sysRegion; [action]: sync
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/sysRegion/sync
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://127.0.0.1:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-cn
      ┣ 客户端 IP 地址：                 *******
      ┣ 客户端源端口：                   58770
      ┣ 服务端 IP 地址：                 *******
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-25bcefb295b74062d80d93598eaa071e-149ac7e986890c71-00
      ┣ 服务线程 ID：                    #49
      ┣ 执行耗时：                       851ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://localhost:5005/
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       iisexpress
      ┣ 托管程序：                       iisexpress
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.M9-jfd-R1ZgJFfU-SADbajUmaMdSm7AKI4QNRxvcgaY
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-08 14:33:04:0000(+08:00) Tuesday L)
      ┣ nbf (integer64)：                ********** (2025-07-08 14:33:04:0000(+08:00) Tuesday L)
      ┣ exp (integer64)：                ********** (2025-07-15 14:33:04:0000(+08:00) Tuesday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-cn
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.M9-jfd-R1ZgJFfU-SADbajUmaMdSm7AKI4QNRxvcgaY
      ┣ Connection：                     keep-alive
      ┣ Content-Length：                 0
      ┣ Host：                           localhost:5005
      ┣ Referer：                        http://127.0.0.1:8888/
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Origin：                         http://127.0.0.1:8888
      ┣ Sec-Fetch-Site：                 cross-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           SqlSugar.SqlSugarException
      ┣ 消息：                           中文提示 : BulkCopy MySql连接字符串需要添加 AllowLoadLocalInfile=true; 添加后如果还不行Mysql数据库执行一下 SET GLOBAL local_infile=1 
      English Message : connection string add : AllowLoadLocalInfile=true
      ┣ 错误堆栈：                       at SqlSugar.Check.ExceptionEasy(String enMessage, String cnMessage)
         at SqlSugar.MySqlFastBuilder.ExecuteBulkCopyAsync(DataTable dt)
         at SqlSugar.FastestProvider`1._BulkCopy(List`1 datas)
         at SqlSugar.FastestProvider`1.BulkCopyAsync(List`1 datas)
         at Admin.NET.Core.Service.SysRegionService.SyncByMap(Int32 syncLevel) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Region\SysRegionService.cs:line 349
         at Admin.NET.Core.Service.SysRegionService.Sync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Region\SysRegionService.cs:line 158
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: 中文提示 : BulkCopy MySql连接字符串需要添加 AllowLoadLocalInfile=true; 添加后如果还不行Mysql数据库执行一下 SET GLOBAL local_infile=1 
      English Message : connection string add : AllowLoadLocalInfile=true
         at SqlSugar.Check.ExceptionEasy(String enMessage, String cnMessage)
         at SqlSugar.MySqlFastBuilder.ExecuteBulkCopyAsync(DataTable dt)
         at SqlSugar.FastestProvider`1._BulkCopy(List`1 datas)
         at SqlSugar.FastestProvider`1.BulkCopyAsync(List`1 datas)
         at Admin.NET.Core.Service.SysRegionService.SyncByMap(Int32 syncLevel) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Region\SysRegionService.cs:line 349
         at Admin.NET.Core.Service.SysRegionService.Sync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Region\SysRegionService.cs:line 158
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-08 14:34:38.7059616 +08:00 Tuesday L System.Logging.LoggingMonitor[0] #44 '00-f5d978917a73e449f5cb7eb3997452fa-feba340d5e577e83-00'
      [Furion.Pure.dll] async Task LoggingMonitorAttribute.MonitorAsync(MethodInfo actionMethod, IDictionary<string, object> parameterValues, FilterContext context, dynamic next)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Core.Service.SysRegionService.Sync
      ┣ 
      ┣ 控制器名称：                     SysRegionService
      ┣ 操作名称：                       Sync
      ┣ 显示名称：                       同步行政区域
      ┣ 路由信息：                       [area]: ; [controller]: sysRegion; [action]: sync
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/sysRegion/sync
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://127.0.0.1:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-cn
      ┣ 客户端 IP 地址：                 *******
      ┣ 客户端源端口：                   58770
      ┣ 服务端 IP 地址：                 *******
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-f5d978917a73e449f5cb7eb3997452fa-feba340d5e577e83-00
      ┣ 服务线程 ID：                    #49
      ┣ 执行耗时：                       409ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://localhost:5005/
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       iisexpress
      ┣ 托管程序：                       iisexpress
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.M9-jfd-R1ZgJFfU-SADbajUmaMdSm7AKI4QNRxvcgaY
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-08 14:33:04:0000(+08:00) Tuesday L)
      ┣ nbf (integer64)：                ********** (2025-07-08 14:33:04:0000(+08:00) Tuesday L)
      ┣ exp (integer64)：                ********** (2025-07-15 14:33:04:0000(+08:00) Tuesday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-cn
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.M9-jfd-R1ZgJFfU-SADbajUmaMdSm7AKI4QNRxvcgaY
      ┣ Connection：                     keep-alive
      ┣ Content-Length：                 0
      ┣ Host：                           localhost:5005
      ┣ Referer：                        http://127.0.0.1:8888/
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Origin：                         http://127.0.0.1:8888
      ┣ Sec-Fetch-Site：                 cross-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           SqlSugar.SqlSugarException
      ┣ 消息：                           中文提示 : BulkCopy MySql连接字符串需要添加 AllowLoadLocalInfile=true; 添加后如果还不行Mysql数据库执行一下 SET GLOBAL local_infile=1 
      English Message : connection string add : AllowLoadLocalInfile=true
      ┣ 错误堆栈：                       at SqlSugar.Check.ExceptionEasy(String enMessage, String cnMessage)
         at SqlSugar.MySqlFastBuilder.ExecuteBulkCopyAsync(DataTable dt)
         at SqlSugar.FastestProvider`1._BulkCopy(List`1 datas)
         at SqlSugar.FastestProvider`1.BulkCopyAsync(List`1 datas)
         at Admin.NET.Core.Service.SysRegionService.SyncByMap(Int32 syncLevel) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Region\SysRegionService.cs:line 349
         at Admin.NET.Core.Service.SysRegionService.Sync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Region\SysRegionService.cs:line 158
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: 中文提示 : BulkCopy MySql连接字符串需要添加 AllowLoadLocalInfile=true; 添加后如果还不行Mysql数据库执行一下 SET GLOBAL local_infile=1 
      English Message : connection string add : AllowLoadLocalInfile=true
         at SqlSugar.Check.ExceptionEasy(String enMessage, String cnMessage)
         at SqlSugar.MySqlFastBuilder.ExecuteBulkCopyAsync(DataTable dt)
         at SqlSugar.FastestProvider`1._BulkCopy(List`1 datas)
         at SqlSugar.FastestProvider`1.BulkCopyAsync(List`1 datas)
         at Admin.NET.Core.Service.SysRegionService.SyncByMap(Int32 syncLevel) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Region\SysRegionService.cs:line 349
         at Admin.NET.Core.Service.SysRegionService.Sync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Region\SysRegionService.cs:line 158
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-08 14:38:54.6718234 +08:00 Tuesday L System.Logging.LoggingMonitor[0] #18 '00-cad3c75dee01667d1acace35e2162931-518c5e16ddde1b39-00'
      [Furion.Pure.dll] async Task LoggingMonitorAttribute.MonitorAsync(MethodInfo actionMethod, IDictionary<string, object> parameterValues, FilterContext context, dynamic next)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Core.Service.SysRegionService.Sync
      ┣ 
      ┣ 控制器名称：                     SysRegionService
      ┣ 操作名称：                       Sync
      ┣ 显示名称：                       同步行政区域
      ┣ 路由信息：                       [area]: ; [controller]: sysRegion; [action]: sync
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/sysRegion/sync
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://127.0.0.1:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-cn
      ┣ 客户端 IP 地址：                 *******
      ┣ 客户端源端口：                   59767
      ┣ 服务端 IP 地址：                 *******
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-cad3c75dee01667d1acace35e2162931-518c5e16ddde1b39-00
      ┣ 服务线程 ID：                    #44
      ┣ 执行耗时：                       696ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://localhost:5005/
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       iisexpress
      ┣ 托管程序：                       iisexpress
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.M9-jfd-R1ZgJFfU-SADbajUmaMdSm7AKI4QNRxvcgaY
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-08 14:33:04:0000(+08:00) Tuesday L)
      ┣ nbf (integer64)：                ********** (2025-07-08 14:33:04:0000(+08:00) Tuesday L)
      ┣ exp (integer64)：                ********** (2025-07-15 14:33:04:0000(+08:00) Tuesday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-cn
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.M9-jfd-R1ZgJFfU-SADbajUmaMdSm7AKI4QNRxvcgaY
      ┣ Connection：                     keep-alive
      ┣ Content-Length：                 0
      ┣ Host：                           localhost:5005
      ┣ Referer：                        http://127.0.0.1:8888/
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Origin：                         http://127.0.0.1:8888
      ┣ Sec-Fetch-Site：                 cross-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           SqlSugar.SqlSugarException
      ┣ 消息：                           中文提示 : BulkCopy MySql连接字符串需要添加 AllowLoadLocalInfile=true; 添加后如果还不行Mysql数据库执行一下 SET GLOBAL local_infile=1 
      English Message : connection string add : AllowLoadLocalInfile=true
      ┣ 错误堆栈：                       at SqlSugar.Check.ExceptionEasy(String enMessage, String cnMessage)
         at SqlSugar.MySqlFastBuilder.ExecuteBulkCopyAsync(DataTable dt)
         at SqlSugar.FastestProvider`1._BulkCopy(List`1 datas)
         at SqlSugar.FastestProvider`1.BulkCopyAsync(List`1 datas)
         at Admin.NET.Core.Service.SysRegionService.SyncByMap(Int32 syncLevel) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Region\SysRegionService.cs:line 349
         at Admin.NET.Core.Service.SysRegionService.Sync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Region\SysRegionService.cs:line 158
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: 中文提示 : BulkCopy MySql连接字符串需要添加 AllowLoadLocalInfile=true; 添加后如果还不行Mysql数据库执行一下 SET GLOBAL local_infile=1 
      English Message : connection string add : AllowLoadLocalInfile=true
         at SqlSugar.Check.ExceptionEasy(String enMessage, String cnMessage)
         at SqlSugar.MySqlFastBuilder.ExecuteBulkCopyAsync(DataTable dt)
         at SqlSugar.FastestProvider`1._BulkCopy(List`1 datas)
         at SqlSugar.FastestProvider`1.BulkCopyAsync(List`1 datas)
         at Admin.NET.Core.Service.SysRegionService.SyncByMap(Int32 syncLevel) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Region\SysRegionService.cs:line 349
         at Admin.NET.Core.Service.SysRegionService.Sync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Region\SysRegionService.cs:line 158
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
crit: 2025-07-08 17:37:35.5364454 +08:00 Tuesday L System.Logging.ScheduleService[0] #19
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogCritical(string message, params object[] args)
      Schedule hosted service is stopped.
crit: 2025-07-08 17:37:35.7162822 +08:00 Tuesday L System.Logging.TaskQueueService[0] #16
      [Furion.Pure.dll] async Task Furion.TaskQueue.TaskQueueHostedService.ExecuteAsync(CancellationToken stoppingToken)
      TaskQueue hosted service is stopped.
fail: 2025-07-08 18:05:37.9865173 +08:00 Tuesday L Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware[1] #22 '00-6ca1303e980000f6d1213f3679a78760-9864a22ac17343f1-00'
      [Microsoft.AspNetCore.Diagnostics.dll] void Microsoft.AspNetCore.Diagnostics.DiagnosticsTelemetry.ReportUnhandledException(ILogger logger, HttpContext context, Exception ex)
      An unhandled exception has occurred while executing the request.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "POST admin-api/api/label/bind-multiple" for actions - Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayLabelService.BindMultipleLabels (Admin.NET.Plugin.GreenDisplay), Admin.NET.Plugin.GreenDisplay.Controller.GreenDisplayController.BindMultipleLabels (Admin.NET.Plugin.GreenDisplay). Actions require a unique method/path combination for Swagger/OpenAPI 2.0 and 3.0. Use ConflictingActionsResolver as a workaround or provide your own implementation of PathGroupSelector.
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.PrepareGenerateOperation(IGrouping`2 group)
         at System.Linq.Enumerable.SelectEnumerableIterator`2.MoveNext()
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
         at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
         at IGeekFan.AspNetCore.Knife4jUI.Knife4jUIMiddleware.Invoke(HttpContext httpContext)
         at Furion.Schedule.ScheduleUIMiddleware.InvokeAsync(HttpContext context)
         at AspNetCoreRateLimit.RateLimitMiddleware`1.Invoke(HttpContext context) in C:\Users\<USER>\Documents\Github\AspNetCoreRateLimit\src\AspNetCoreRateLimit\Middleware\RateLimitMiddleware.cs:line 124
         at AspNetCoreRateLimit.RateLimitMiddleware`1.Invoke(HttpContext context) in C:\Users\<USER>\Documents\Github\AspNetCoreRateLimit\src\AspNetCoreRateLimit\Middleware\RateLimitMiddleware.cs:line 124
         at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
         at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
         at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)
         at Furion.UnifyResult.UnifyResultStatusCodesMiddleware.InvokeAsync(HttpContext context)
         at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)
         at Admin.NET.Web.Core.Startup.<>c.<<Configure>b__1_0>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Web.Core\Startup.cs:line 283
      --- End of stack trace from previous location ---
         at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-08 18:08:33.9535691 +08:00 Tuesday L Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware[1] #36 '00-1ec73351f9af34e3a7357f006c27ca8f-8650723e6b96ca5d-00'
      [Microsoft.AspNetCore.Diagnostics.dll] void Microsoft.AspNetCore.Diagnostics.DiagnosticsTelemetry.ReportUnhandledException(ILogger logger, HttpContext context, Exception ex)
      An unhandled exception has occurred while executing the request.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "POST admin-api/api/label/bind-multiple" for actions - Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayLabelService.BindMultipleLabels (Admin.NET.Plugin.GreenDisplay), Admin.NET.Plugin.GreenDisplay.Controller.GreenDisplayController.BindMultipleLabels (Admin.NET.Plugin.GreenDisplay). Actions require a unique method/path combination for Swagger/OpenAPI 2.0 and 3.0. Use ConflictingActionsResolver as a workaround or provide your own implementation of PathGroupSelector.
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.PrepareGenerateOperation(IGrouping`2 group)
         at System.Linq.Enumerable.SelectEnumerableIterator`2.MoveNext()
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
         at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
         at IGeekFan.AspNetCore.Knife4jUI.Knife4jUIMiddleware.Invoke(HttpContext httpContext)
         at Furion.Schedule.ScheduleUIMiddleware.InvokeAsync(HttpContext context)
         at AspNetCoreRateLimit.RateLimitMiddleware`1.Invoke(HttpContext context) in C:\Users\<USER>\Documents\Github\AspNetCoreRateLimit\src\AspNetCoreRateLimit\Middleware\RateLimitMiddleware.cs:line 124
         at AspNetCoreRateLimit.RateLimitMiddleware`1.Invoke(HttpContext context) in C:\Users\<USER>\Documents\Github\AspNetCoreRateLimit\src\AspNetCoreRateLimit\Middleware\RateLimitMiddleware.cs:line 124
         at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
         at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
         at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)
         at Furion.UnifyResult.UnifyResultStatusCodesMiddleware.InvokeAsync(HttpContext context)
         at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)
         at Admin.NET.Web.Core.Startup.<>c.<<Configure>b__1_0>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Web.Core\Startup.cs:line 283
      --- End of stack trace from previous location ---
         at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
crit: 2025-07-08 18:08:44.6277124 +08:00 Tuesday L System.Logging.ScheduleService[0] #41
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogCritical(string message, params object[] args)
      Schedule hosted service is stopped.
crit: 2025-07-08 18:08:44.6310487 +08:00 Tuesday L System.Logging.TaskQueueService[0] #39
      [Furion.Pure.dll] async Task Furion.TaskQueue.TaskQueueHostedService.ExecuteAsync(CancellationToken stoppingToken)
      TaskQueue hosted service is stopped.
fail: 2025-07-08 18:09:26.6852583 +08:00 Tuesday L Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware[1] #21 '00-de2777daea1f3150f6474fafc7c90c9b-f8e857bce00fc063-00'
      [Microsoft.AspNetCore.Diagnostics.dll] void Microsoft.AspNetCore.Diagnostics.DiagnosticsTelemetry.ReportUnhandledException(ILogger logger, HttpContext context, Exception ex)
      An unhandled exception has occurred while executing the request.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "POST admin-api/api/label/bind-multiple" for actions - Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayLabelService.BindMultipleLabels (Admin.NET.Plugin.GreenDisplay), Admin.NET.Plugin.GreenDisplay.Controller.GreenDisplayController.BindMultipleLabels (Admin.NET.Plugin.GreenDisplay). Actions require a unique method/path combination for Swagger/OpenAPI 2.0 and 3.0. Use ConflictingActionsResolver as a workaround or provide your own implementation of PathGroupSelector.
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.PrepareGenerateOperation(IGrouping`2 group)
         at System.Linq.Enumerable.SelectEnumerableIterator`2.MoveNext()
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
         at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
         at IGeekFan.AspNetCore.Knife4jUI.Knife4jUIMiddleware.Invoke(HttpContext httpContext)
         at Furion.Schedule.ScheduleUIMiddleware.InvokeAsync(HttpContext context)
         at AspNetCoreRateLimit.RateLimitMiddleware`1.Invoke(HttpContext context) in C:\Users\<USER>\Documents\Github\AspNetCoreRateLimit\src\AspNetCoreRateLimit\Middleware\RateLimitMiddleware.cs:line 124
         at AspNetCoreRateLimit.RateLimitMiddleware`1.Invoke(HttpContext context) in C:\Users\<USER>\Documents\Github\AspNetCoreRateLimit\src\AspNetCoreRateLimit\Middleware\RateLimitMiddleware.cs:line 124
         at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
         at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
         at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)
         at Furion.UnifyResult.UnifyResultStatusCodesMiddleware.InvokeAsync(HttpContext context)
         at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)
         at Admin.NET.Web.Core.Startup.<>c.<<Configure>b__1_0>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Web.Core\Startup.cs:line 283
      --- End of stack trace from previous location ---
         at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-08 18:09:50.0504317 +08:00 Tuesday L Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware[1] #21 '00-290d33164b7fd479d70f1bc9bc5ad4f0-7c2e2305c2cc8b5d-00'
      [Microsoft.AspNetCore.Diagnostics.dll] void Microsoft.AspNetCore.Diagnostics.DiagnosticsTelemetry.ReportUnhandledException(ILogger logger, HttpContext context, Exception ex)
      An unhandled exception has occurred while executing the request.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "POST admin-api/api/label/bind-multiple" for actions - Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayLabelService.BindMultipleLabels (Admin.NET.Plugin.GreenDisplay), Admin.NET.Plugin.GreenDisplay.Controller.GreenDisplayController.BindMultipleLabels (Admin.NET.Plugin.GreenDisplay). Actions require a unique method/path combination for Swagger/OpenAPI 2.0 and 3.0. Use ConflictingActionsResolver as a workaround or provide your own implementation of PathGroupSelector.
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.PrepareGenerateOperation(IGrouping`2 group)
         at System.Linq.Enumerable.SelectEnumerableIterator`2.MoveNext()
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
         at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
         at IGeekFan.AspNetCore.Knife4jUI.Knife4jUIMiddleware.Invoke(HttpContext httpContext)
         at Furion.Schedule.ScheduleUIMiddleware.InvokeAsync(HttpContext context)
         at AspNetCoreRateLimit.RateLimitMiddleware`1.Invoke(HttpContext context) in C:\Users\<USER>\Documents\Github\AspNetCoreRateLimit\src\AspNetCoreRateLimit\Middleware\RateLimitMiddleware.cs:line 124
         at AspNetCoreRateLimit.RateLimitMiddleware`1.Invoke(HttpContext context) in C:\Users\<USER>\Documents\Github\AspNetCoreRateLimit\src\AspNetCoreRateLimit\Middleware\RateLimitMiddleware.cs:line 124
         at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
         at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
         at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)
         at Furion.UnifyResult.UnifyResultStatusCodesMiddleware.InvokeAsync(HttpContext context)
         at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)
         at Admin.NET.Web.Core.Startup.<>c.<<Configure>b__1_0>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Web.Core\Startup.cs:line 283
      --- End of stack trace from previous location ---
         at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
