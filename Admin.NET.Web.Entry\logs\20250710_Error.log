fail: 2025-07-10 09:24:24.5766668 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #45 '00-153f5908bfeec8d9fd5aae1a51c1cd9d-563299ad5a8dd440-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 09:24:24.7519695 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #45 '00-153f5908bfeec8d9fd5aae1a51c1cd9d-563299ad5a8dd440-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync()
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 09:26:03.5230119 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #23 '00-e92c8a7047d65ba91604f800b7e0f767-ebad067f9e8a914a-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 61
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 09:26:03.7313372 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #23 '00-e92c8a7047d65ba91604f800b7e0f767-ebad067f9e8a914a-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 61
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 635
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:05:45.2318052 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #45 '00-da4e21cfd03c961c0da5c0803ee9583c-947f93a441f9a2eb-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:05:45.4842631 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #45 '00-da4e21cfd03c961c0da5c0803ee9583c-947f93a441f9a2eb-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 798
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:27:51.3738584 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #22 '00-7b540be1bd2816c97d5ee61a8c8b6208-d92e4b2f8314995d-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:27:51.6896249 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #22 '00-7b540be1bd2816c97d5ee61a8c8b6208-d92e4b2f8314995d-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync()
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync()
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:28:32.7104536 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #21 '00-d6af12acca1141a38db496fab20f3285-1db8a83196dbad9f-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:28:33.0656862 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #21 '00-d6af12acca1141a38db496fab20f3285-1db8a83196dbad9f-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 798
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:28:47.9868564 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #21 '00-30c57cdd1678770005104a4fd6d1f953-220fc29436af6d90-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:28:48.1591271 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #21 '00-30c57cdd1678770005104a4fd6d1f953-220fc29436af6d90-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 798
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:29:07.1536098 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #24 '00-9438270e931765fc1a6434d76b23290f-2958d3427fef7986-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:29:07.2876976 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #24 '00-9438270e931765fc1a6434d76b23290f-2958d3427fef7986-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 798
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:30:08.5683367 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #50 '00-6c307b14e898fcb420c751b5b990d78e-b6c92a64f0764f11-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:30:08.7173647 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #50 '00-6c307b14e898fcb420c751b5b990d78e-b6c92a64f0764f11-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 798
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:30:31.6349866 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #24 '00-50ac202ea1836a0134dd4f12db11ef97-5c2e0b1c91805169-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:30:31.7822683 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #24 '00-50ac202ea1836a0134dd4f12db11ef97-5c2e0b1c91805169-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 798
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:32:13.5740697 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #43 '00-8ef1aa25313a05ddbf6707a0a2ef2a73-fcb2f860d11fa467-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:32:13.8419596 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #43 '00-8ef1aa25313a05ddbf6707a0a2ef2a73-fcb2f860d11fa467-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 98
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 799
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:36:59.4429135 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #47 '00-e90d1087940b94a1b027705fb8aa1401-4fdabff5112d7428-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:36:59.6162685 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #47 '00-e90d1087940b94a1b027705fb8aa1401-4fdabff5112d7428-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 98
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 799
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:38:40.5488650 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #47 '00-432c302bd78061f71f5aab2c7574be84-6123467062643d59-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:38:40.7171296 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #47 '00-432c302bd78061f71f5aab2c7574be84-6123467062643d59-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 98
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 799
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:40:49.8943753 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #24 '00-08af0c5cdb8ef1cb7e5f9f89d8fcc15a-d70c0418d6957ce2-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:40:51.7065641 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #24 '00-08af0c5cdb8ef1cb7e5f9f89d8fcc15a-d70c0418d6957ce2-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 98
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 799
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:45:06.3628982 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #45 '00-5a06c5edcab470c89d8608918b93f6c0-e1c05af66ea7515a-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:45:06.6637796 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #45 '00-5a06c5edcab470c89d8608918b93f6c0-e1c05af66ea7515a-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 98
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 799
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:45:34.5899217 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #45 '00-15e2c2a3d05a414ee690f9dda4e4b597-2ae23dd9ec652500-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:45:34.7546873 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #45 '00-15e2c2a3d05a414ee690f9dda4e4b597-2ae23dd9ec652500-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 98
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 799
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:46:03.7361451 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #47 '00-7fb0d9fa6ca2be3dfa188310e0f11e68-919e55fad6b7af41-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:46:03.9196432 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #47 '00-7fb0d9fa6ca2be3dfa188310e0f11e68-919e55fad6b7af41-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 98
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 799
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:51:50.4000677 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54 '00-3078483d48aa2d37f051a682b426505e-9e171e75b0fbc0d4-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Threading.Tasks.TaskCanceledException: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
       ---> System.TimeoutException: A task was canceled.
       ---> System.Threading.Tasks.TaskCanceledException: A task was canceled.
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         --- End of inner exception stack trace ---
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpClient.HandleFailure(Exception e, Boolean telemetryStarted, HttpResponseMessage response, CancellationTokenSource cts, CancellationToken cancellationToken, CancellationTokenSource pendingRequestsCts)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:51:50.5538821 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54 '00-3078483d48aa2d37f051a682b426505e-9e171e75b0fbc0d4-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: 获取访问令牌失败
       ---> System.Threading.Tasks.TaskCanceledException: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
       ---> System.TimeoutException: A task was canceled.
       ---> System.Threading.Tasks.TaskCanceledException: A task was canceled.
         at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
         at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
         at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
         at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         --- End of inner exception stack trace ---
         --- End of inner exception stack trace ---
         at System.Net.Http.HttpClient.HandleFailure(Exception e, Boolean telemetryStarted, HttpResponseMessage response, CancellationTokenSource cts, CancellationToken cancellationToken, CancellationTokenSource pendingRequestsCts)
         at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendCoreAsync(HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, Func`5 sendAsyncMethod, Func`5 sendMethod, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 98
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 799
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:52:21.7873643 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54 '00-d71418d18498d0471bf9b2255b74187e-a7863cdb6e335d3e-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 10:52:21.9501049 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #54 '00-d71418d18498d0471bf9b2255b74187e-a7863cdb6e335d3e-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 67
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 98
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 799
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 11:01:59.1451705 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #21 '00-9d468105edce3c54f3ee5a8a0fe0a301-5928c5fa05454d67-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      GreenDisplay登录失败：未知错误
fail: 2025-07-10 11:02:00.9624961 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #21 '00-9d468105edce3c54f3ee5a8a0fe0a301-5928c5fa05454d67-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.UnauthorizedAccessException: 登录失败：未知错误
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 87
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 799
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 11:09:33.6680178 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #24 '00-8faabc448b8942313dc1f36e88ba14b4-11dd587e4fbaae43-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '0x1F' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 11:09:34.0356886 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #24 '00-8faabc448b8942313dc1f36e88ba14b4-11dd587e4fbaae43-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: '0x1F' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync()
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync()
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 11:09:44.1045493 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #44 '00-a6f93fd15c341d8d819a53aa7236ebd9-64d8ed7d28cc9ae6-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '0x1F' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 11:09:44.2534692 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #44 '00-a6f93fd15c341d8d819a53aa7236ebd9-64d8ed7d28cc9ae6-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: '0x1F' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync()
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync()
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 11:25:30.9917688 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #23 '00-58f26dd9c2da9d45cc836e8e940b36df-9b8a606ecb593e86-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      GreenDisplay登录失败：未知错误
fail: 2025-07-10 11:25:31.3387420 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #23 '00-58f26dd9c2da9d45cc836e8e940b36df-9b8a606ecb593e86-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.UnauthorizedAccessException: 登录失败：未知错误
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 87
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 799
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 11:30:40.8488634 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #23 '00-906bb8455fc1cbf2adb73e5ea6a220a6-b18bf32a499a612c-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      GreenDisplay登录失败：未知错误
fail: 2025-07-10 11:30:41.1950261 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #23 '00-906bb8455fc1cbf2adb73e5ea6a220a6-b18bf32a499a612c-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.UnauthorizedAccessException: 登录失败：未知错误
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 87
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 799
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 12:02:44.9171458 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #22 '00-72880b26209c2ede4788596b65f22248-225ac7cf75cba3bc-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      GreenDisplay登录失败：未知错误
fail: 2025-07-10 12:02:45.4194347 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #22 '00-72880b26209c2ede4788596b65f22248-225ac7cf75cba3bc-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayAuthException: 登录失败：未知错误
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 117
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 889
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 15:05:52.9043680 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #24 '00-9cd68f8c9c5fafc0248d7be11b1cfcde-d3568df1b50ddfc0-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      GreenDisplay登录失败：未知错误
fail: 2025-07-10 15:05:53.3319169 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #24 '00-9cd68f8c9c5fafc0248d7be11b1cfcde-d3568df1b50ddfc0-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayAuthException: 登录失败：未知错误
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 117
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 889
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 15:18:30.4118299 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #11 '00-f14bc3e1c9b30c0f01bb56a2ac645a35-1f3fc06ccecbaf99-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生未知异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: The JSON value could not be converted to System.Int32. Path: $.data.expiresTime | LineNumber: 0 | BytePositionInLine: 157.
       ---> System.FormatException: Either the JSON value is not in a supported format, or is out of bounds for an Int32.
         at System.Text.Json.ThrowHelper.ThrowFormatException(NumericType numericType)
         at System.Text.Json.Utf8JsonReader.GetInt32()
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 15:18:30.8927707 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #11 '00-f14bc3e1c9b30c0f01bb56a2ac645a35-1f3fc06ccecbaf99-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: The JSON value could not be converted to System.Int32. Path: $.data.expiresTime | LineNumber: 0 | BytePositionInLine: 157.
       ---> System.FormatException: Either the JSON value is not in a supported format, or is out of bounds for an Int32.
         at System.Text.Json.ThrowHelper.ThrowFormatException(NumericType numericType)
         at System.Text.Json.Utf8JsonReader.GetInt32()
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 137
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 889
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 15:18:50.4791578 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #19 '00-2e1c2080e1cf5c7f1c35354ddbd547d9-7f6aa7f6b383b6bb-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生未知异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: The JSON value could not be converted to System.Int32. Path: $.data.expiresTime | LineNumber: 0 | BytePositionInLine: 157.
       ---> System.FormatException: Either the JSON value is not in a supported format, or is out of bounds for an Int32.
         at System.Text.Json.ThrowHelper.ThrowFormatException(NumericType numericType)
         at System.Text.Json.Utf8JsonReader.GetInt32()
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 15:18:50.9309663 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #19 '00-2e1c2080e1cf5c7f1c35354ddbd547d9-7f6aa7f6b383b6bb-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: The JSON value could not be converted to System.Int32. Path: $.data.expiresTime | LineNumber: 0 | BytePositionInLine: 157.
       ---> System.FormatException: Either the JSON value is not in a supported format, or is out of bounds for an Int32.
         at System.Text.Json.ThrowHelper.ThrowFormatException(NumericType numericType)
         at System.Text.Json.Utf8JsonReader.GetInt32()
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 137
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 889
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 15:18:59.8638975 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #18 '00-a091156de6b095c2d495250a61715a8a-f98265355c92268c-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生未知异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: The JSON value could not be converted to System.Int32. Path: $.data.expiresTime | LineNumber: 0 | BytePositionInLine: 157.
       ---> System.FormatException: Either the JSON value is not in a supported format, or is out of bounds for an Int32.
         at System.Text.Json.ThrowHelper.ThrowFormatException(NumericType numericType)
         at System.Text.Json.Utf8JsonReader.GetInt32()
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 15:19:00.2359984 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #18 '00-a091156de6b095c2d495250a61715a8a-f98265355c92268c-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: The JSON value could not be converted to System.Int32. Path: $.data.expiresTime | LineNumber: 0 | BytePositionInLine: 157.
       ---> System.FormatException: Either the JSON value is not in a supported format, or is out of bounds for an Int32.
         at System.Text.Json.ThrowHelper.ThrowFormatException(NumericType numericType)
         at System.Text.Json.Utf8JsonReader.GetInt32()
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 97
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 137
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 75
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 889
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 15:22:18.9718131 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #17 '00-999073fb35e3ae3380d6cfa33b659150-809e5bf402766026-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生未知异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: The JSON value could not be converted to System.Int32. Path: $.data.expiresTime | LineNumber: 0 | BytePositionInLine: 157.
       ---> System.FormatException: Either the JSON value is not in a supported format, or is out of bounds for an Int32.
         at System.Text.Json.ThrowHelper.ThrowFormatException(NumericType numericType)
         at System.Text.Json.Utf8JsonReader.GetInt32()
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 15:22:19.3733576 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #17 '00-999073fb35e3ae3380d6cfa33b659150-809e5bf402766026-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: The JSON value could not be converted to System.Int32. Path: $.data.expiresTime | LineNumber: 0 | BytePositionInLine: 157.
       ---> System.FormatException: Either the JSON value is not in a supported format, or is out of bounds for an Int32.
         at System.Text.Json.ThrowHelper.ThrowFormatException(NumericType numericType)
         at System.Text.Json.Utf8JsonReader.GetInt32()
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 15:22:24.0496742 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #49 '00-50f0fd1b8187f1f1b8d9a8155243cdd8-a84941ee75e825cb-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生未知异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: The JSON value could not be converted to System.Int32. Path: $.data.expiresTime | LineNumber: 0 | BytePositionInLine: 157.
       ---> System.FormatException: Either the JSON value is not in a supported format, or is out of bounds for an Int32.
         at System.Text.Json.ThrowHelper.ThrowFormatException(NumericType numericType)
         at System.Text.Json.Utf8JsonReader.GetInt32()
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 15:22:24.3948953 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #49 '00-50f0fd1b8187f1f1b8d9a8155243cdd8-a84941ee75e825cb-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: The JSON value could not be converted to System.Int32. Path: $.data.expiresTime | LineNumber: 0 | BytePositionInLine: 157.
       ---> System.FormatException: Either the JSON value is not in a supported format, or is out of bounds for an Int32.
         at System.Text.Json.ThrowHelper.ThrowFormatException(NumericType numericType)
         at System.Text.Json.Utf8JsonReader.GetInt32()
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 15:22:38.0386800 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #47 '00-6906c2262410590f4a31ee13c9ca51b8-57ac35b998ac80e9-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生未知异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: The JSON value could not be converted to System.Int32. Path: $.data.expiresTime | LineNumber: 0 | BytePositionInLine: 157.
       ---> System.FormatException: Either the JSON value is not in a supported format, or is out of bounds for an Int32.
         at System.Text.Json.ThrowHelper.ThrowFormatException(NumericType numericType)
         at System.Text.Json.Utf8JsonReader.GetInt32()
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 15:22:38.3625640 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #47 '00-6906c2262410590f4a31ee13c9ca51b8-57ac35b998ac80e9-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常，名称：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Admin.NET.Plugin.GreenDisplay.Exceptions.GreenDisplayException: 获取访问令牌失败
       ---> System.Text.Json.JsonException: The JSON value could not be converted to System.Int32. Path: $.data.expiresTime | LineNumber: 0 | BytePositionInLine: 157.
       ---> System.FormatException: Either the JSON value is not in a supported format, or is out of bounds for an Int32.
         at System.Text.Json.ThrowHelper.ThrowFormatException(NumericType numericType)
         at System.Text.Json.Utf8JsonReader.GetInt32()
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.ReadJsonAndSetMember(Object obj, ReadStack& state, Utf8JsonReader& reader)
         at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
         at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
         --- End of inner exception stack trace ---
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.RefreshTokenAsync()
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync(Boolean forceRefresh)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 17:11:41.6588785 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #24 '00-1d67bdcec8b05d5d55b351992c873b7f-1dc44db7728999a0-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      删除网关时发生异常，MAC：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: No `[HttpMethod]` annotation was found in method `System.Threading.Tasks.Task<Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayResponse<System.Object>> DeleteAPAsync(System.String, System.String)` of type `Admin.NET.Plugin.GreenDisplay.Service.IGreenDisplayApi`.
         at Furion.HttpRemote.HttpDeclarativeBuilder.Build(HttpRemoteOptions httpRemoteOptions)
         at Furion.HttpRemote.DeclarativeManager..ctor(IHttpRemoteService httpRemoteService, HttpDeclarativeBuilder httpDeclarativeBuilder)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[T](HttpDeclarativeBuilder httpDeclarativeBuilder)
         at Furion.HttpRemote.HttpRemoteService.DeclarativeAsync[T](MethodInfo method, Object[] args)
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.DeleteAPAsync(String apMac) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 1003
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 17:13:06.6191001 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #16 '00-c239741236b5238b5b99c7e06bb112dd-a1ee0b61e06d366d-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      删除网关时发生异常，MAC：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: No `[HttpMethod]` annotation was found in method `System.Threading.Tasks.Task<Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayResponse<System.Object>> DeleteAPAsync(System.String, System.String)` of type `Admin.NET.Plugin.GreenDisplay.Service.IGreenDisplayApi`.
         at Furion.HttpRemote.HttpDeclarativeBuilder.Build(HttpRemoteOptions httpRemoteOptions)
         at Furion.HttpRemote.DeclarativeManager..ctor(IHttpRemoteService httpRemoteService, HttpDeclarativeBuilder httpDeclarativeBuilder)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[T](HttpDeclarativeBuilder httpDeclarativeBuilder)
         at Furion.HttpRemote.HttpRemoteService.DeclarativeAsync[T](MethodInfo method, Object[] args)
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.DeleteAPAsync(String apMac) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 1003
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 17:16:03.2168553 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #26 '00-1c9064e781e9ec1312004efd75b1ac5c-23bca30ac73188dd-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      删除网关时发生异常，MAC：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: No `[HttpMethod]` annotation was found in method `System.Threading.Tasks.Task<Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayResponse<System.Object>> DeleteAPAsync(System.String, System.String)` of type `Admin.NET.Plugin.GreenDisplay.Service.IGreenDisplayApi`.
         at Furion.HttpRemote.HttpDeclarativeBuilder.Build(HttpRemoteOptions httpRemoteOptions)
         at Furion.HttpRemote.DeclarativeManager..ctor(IHttpRemoteService httpRemoteService, HttpDeclarativeBuilder httpDeclarativeBuilder)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[T](HttpDeclarativeBuilder httpDeclarativeBuilder)
         at Furion.HttpRemote.HttpRemoteService.DeclarativeAsync[T](MethodInfo method, Object[] args)
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.DeleteAPAsync(String apMac) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 1003
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 17:16:34.1189214 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #17 '00-cb37eab32d6d4334acdb6f306078ff29-385490e282055499-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      删除网关时发生异常，MAC：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: No `[HttpMethod]` annotation was found in method `System.Threading.Tasks.Task<Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayResponse<System.Object>> DeleteAPAsync(System.String, System.String)` of type `Admin.NET.Plugin.GreenDisplay.Service.IGreenDisplayApi`.
         at Furion.HttpRemote.HttpDeclarativeBuilder.Build(HttpRemoteOptions httpRemoteOptions)
         at Furion.HttpRemote.DeclarativeManager..ctor(IHttpRemoteService httpRemoteService, HttpDeclarativeBuilder httpDeclarativeBuilder)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[T](HttpDeclarativeBuilder httpDeclarativeBuilder)
         at Furion.HttpRemote.HttpRemoteService.DeclarativeAsync[T](MethodInfo method, Object[] args)
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.DeleteAPAsync(String apMac) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 1003
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-10 17:31:11.2625852 +08:00 Thursday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #44 '00-1d808fafdaf689163ca6404082d5fc5b-aeb0fa1b8fc03aab-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      删除网关时发生异常，MAC：string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: No `[HttpMethod]` annotation was found in method `System.Threading.Tasks.Task<Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayResponse<System.Object>> DeleteAPAsync(System.String, System.String)` of type `Admin.NET.Plugin.GreenDisplay.Service.IGreenDisplayApi`.
         at Furion.HttpRemote.HttpDeclarativeBuilder.Build(HttpRemoteOptions httpRemoteOptions)
         at Furion.HttpRemote.DeclarativeManager..ctor(IHttpRemoteService httpRemoteService, HttpDeclarativeBuilder httpDeclarativeBuilder)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[T](HttpDeclarativeBuilder httpDeclarativeBuilder)
         at Furion.HttpRemote.HttpRemoteService.DeclarativeAsync[T](MethodInfo method, Object[] args)
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.DeleteAPAsync(String apMac) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 1003
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
