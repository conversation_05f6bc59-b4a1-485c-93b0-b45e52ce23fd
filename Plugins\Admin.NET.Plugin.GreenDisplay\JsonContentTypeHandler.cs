// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Admin.NET.Plugin.GreenDisplay;

/// <summary>
/// 自动设置JSON Content-Type的HTTP消息处理器
/// </summary>
public class JsonContentTypeHandler : DelegatingHandler
{
    /// <summary>
    /// 发送HTTP请求
    /// </summary>
    /// <param name="request">HTTP请求消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>HTTP响应消息</returns>
    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        // 如果请求有内容，强制设置Content-Type为application/json
        if (request.Content != null)
        {
            request.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/json")
            {
                CharSet = "utf-8"
            };
        }
        
        // 继续处理请求
        return await base.SendAsync(request, cancellationToken);
    }
}