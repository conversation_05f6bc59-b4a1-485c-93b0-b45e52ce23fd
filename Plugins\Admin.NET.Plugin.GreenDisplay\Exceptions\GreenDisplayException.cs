// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Plugin.GreenDisplay.Exceptions;

/// <summary>
/// GreenDisplay插件基础异常类
/// </summary>
public class GreenDisplayException : Exception
{
    /// <summary>
    /// 错误代码
    /// </summary>
    public string ErrorCode { get; }

    /// <summary>
    /// 错误详情
    /// </summary>
    public object ErrorDetails { get; }

    public GreenDisplayException(string message) : base(message)
    {
        ErrorCode = "GENERAL_ERROR";
    }

    public GreenDisplayException(string message, Exception innerException) : base(message, innerException)
    {
        ErrorCode = "GENERAL_ERROR";
    }

    public GreenDisplayException(string errorCode, string message) : base(message)
    {
        ErrorCode = errorCode;
    }

    public GreenDisplayException(string errorCode, string message, object errorDetails) : base(message)
    {
        ErrorCode = errorCode;
        ErrorDetails = errorDetails;
    }

    public GreenDisplayException(string errorCode, string message, Exception innerException) : base(message, innerException)
    {
        ErrorCode = errorCode;
    }
}

/// <summary>
/// API调用异常
/// </summary>
public class GreenDisplayApiException : GreenDisplayException
{
    /// <summary>
    /// HTTP状态码
    /// </summary>
    public int? StatusCode { get; }

    /// <summary>
    /// API响应内容
    /// </summary>
    public string ResponseContent { get; }

    public GreenDisplayApiException(string message) : base("API_ERROR", message)
    {
    }

    public GreenDisplayApiException(string message, int statusCode, string responseContent) 
        : base("API_ERROR", message)
    {
        StatusCode = statusCode;
        ResponseContent = responseContent;
    }

    public GreenDisplayApiException(string message, Exception innerException) 
        : base("API_ERROR", message, innerException)
    {
    }
}

/// <summary>
/// 认证异常
/// </summary>
public class GreenDisplayAuthException : GreenDisplayException
{
    public GreenDisplayAuthException(string message) : base("AUTH_ERROR", message)
    {
    }

    public GreenDisplayAuthException(string message, Exception innerException) 
        : base("AUTH_ERROR", message, innerException)
    {
    }
}

/// <summary>
/// 配置异常
/// </summary>
public class GreenDisplayConfigException : GreenDisplayException
{
    public GreenDisplayConfigException(string message) : base("CONFIG_ERROR", message)
    {
    }

    public GreenDisplayConfigException(string message, Exception innerException) 
        : base("CONFIG_ERROR", message, innerException)
    {
    }
}

/// <summary>
/// 设备操作异常
/// </summary>
public class GreenDisplayDeviceException : GreenDisplayException
{
    /// <summary>
    /// 设备MAC地址
    /// </summary>
    public string DeviceMac { get; }

    public GreenDisplayDeviceException(string message, string deviceMac = null) 
        : base("DEVICE_ERROR", message)
    {
        DeviceMac = deviceMac;
    }

    public GreenDisplayDeviceException(string message, string deviceMac, Exception innerException) 
        : base("DEVICE_ERROR", message, innerException)
    {
        DeviceMac = deviceMac;
    }
}

/// <summary>
/// 网络连接异常
/// </summary>
public class GreenDisplayNetworkException : GreenDisplayException
{
    public GreenDisplayNetworkException(string message) : base("NETWORK_ERROR", message)
    {
    }

    public GreenDisplayNetworkException(string message, Exception innerException) 
        : base("NETWORK_ERROR", message, innerException)
    {
    }
}