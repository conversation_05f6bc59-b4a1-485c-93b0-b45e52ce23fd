crit: 2025-07-09 10:30:41.3399762 +08:00 Wednesday L System.Logging.ScheduleService[0] #27
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogCritical(string message, params object[] args)
      Schedule hosted service is stopped.
crit: 2025-07-09 10:37:23.0368269 +08:00 Wednesday L System.Logging.ScheduleService[0] #10
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogCritical(string message, params object[] args)
      Schedule hosted service is stopped.
crit: 2025-07-09 10:56:40.5132219 +08:00 Wednesday L System.Logging.ScheduleService[0] #34
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogCritical(string message, params object[] args)
      Schedule hosted service is stopped.
crit: 2025-07-09 10:56:40.5370830 +08:00 Wednesday L System.Logging.TaskQueueService[0] #10
      [Furion.Pure.dll] async Task Furion.TaskQueue.TaskQueueHostedService.ExecuteAsync(CancellationToken stoppingToken)
      TaskQueue hosted service is stopped.
crit: 2025-07-09 12:06:55.2924376 +08:00 Wednesday L System.Logging.ScheduleService[0] #22
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogCritical(string message, params object[] args)
      Schedule hosted service is stopped.
fail: 2025-07-09 12:15:57.7183852 +08:00 Wednesday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayAuthService[0] #30 '00-714df10bed2605011fdaf9fae35a61ac-90865900d3840ced-00'
      [Admin.NET.Plugin.GreenDisplay.dll] async Task<GreenDisplayLoginOutput> Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayAuthService.Login(GreenDisplayLoginInput input)
      用户登录失败: string      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: 'System.Text.Json.JsonElement' does not contain a definition for 'accessToken'
         at CallSite.Target(Closure, CallSite, Object)
         at System.Dynamic.UpdateDelegates.UpdateAndExecute1[T0,TRet](CallSite site, T0 arg0)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayAuthService.Login(GreenDisplayLoginInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\Auth\GreenDisplayAuthService.cs:line 44
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-09 18:06:47.6104468 +08:00 Wednesday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #46 '00-e64ec7254f09b15bc792b0963b2e2f97-645d89c491f2c969-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 61
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-09 18:06:47.8611294 +08:00 Wednesday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #46 '00-e64ec7254f09b15bc792b0963b2e2f97-645d89c491f2c969-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 61
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 635
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-09 18:07:41.7052975 +08:00 Wednesday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #49 '00-0cf6a55c4be7e505b3480e48e25b12ea-48f452b08c884265-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 61
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-09 18:07:41.8869018 +08:00 Wednesday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #49 '00-0cf6a55c4be7e505b3480e48e25b12ea-48f452b08c884265-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 61
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 635
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-09 18:10:35.7360089 +08:00 Wednesday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #46 '00-c344a641214a72b8b67a6837014ac3ad-862ed6b7e77963db-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 61
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-09 18:10:35.9562965 +08:00 Wednesday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #46 '00-c344a641214a72b8b67a6837014ac3ad-862ed6b7e77963db-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 61
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 635
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-09 18:10:51.4962527 +08:00 Wednesday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #46 '00-a0abf80b1666d806d2f0d23b7aa8e9ff-714a3185561086f8-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 61
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-09 18:10:51.6839279 +08:00 Wednesday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #46 '00-a0abf80b1666d806d2f0d23b7aa8e9ff-714a3185561086f8-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 61
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 635
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-09 18:12:40.4652079 +08:00 Wednesday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #51 '00-dc653fc5f31d03c7f2b39560dcd7bb70-55acc45447cf3483-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 61
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-09 18:12:40.6285916 +08:00 Wednesday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #51 '00-dc653fc5f31d03c7f2b39560dcd7bb70-55acc45447cf3483-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 61
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 635
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-09 18:13:08.7703410 +08:00 Wednesday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #52 '00-dc653fc5f31d03c7f2b39560dcd7bb70-55acc45447cf3483-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 61
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-09 18:14:08.3943472 +08:00 Wednesday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #17 '00-f62baa7d5a7f0294d3965962b2633c16-de021300c364d62c-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 61
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-09 18:14:08.5870094 +08:00 Wednesday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #17 '00-f62baa7d5a7f0294d3965962b2633c16-de021300c364d62c-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 61
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 635
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-09 18:14:46.2828357 +08:00 Wednesday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #46 '00-f62baa7d5a7f0294d3965962b2633c16-de021300c364d62c-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 61
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-09 18:14:46.4331056 +08:00 Wednesday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #46 '00-f62baa7d5a7f0294d3965962b2633c16-de021300c364d62c-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 61
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 635
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-09 18:15:37.8813321 +08:00 Wednesday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #50 '00-f62baa7d5a7f0294d3965962b2633c16-de021300c364d62c-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-09 18:15:38.0412602 +08:00 Wednesday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #50 '00-f62baa7d5a7f0294d3965962b2633c16-de021300c364d62c-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync()
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-09 18:16:17.5973010 +08:00 Wednesday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #25 '00-f62baa7d5a7f0294d3965962b2633c16-de021300c364d62c-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      获取GreenDisplay访问令牌时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-09 18:16:17.7544886 +08:00 Wednesday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #25 '00-f62baa7d5a7f0294d3965962b2633c16-de021300c364d62c-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建网关时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Text.Json.JsonException: '<' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
       ---> System.Text.Json.JsonReaderException: '<' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
         at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
         at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
         at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
         at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
         at System.Text.Json.Utf8JsonReader.Read()
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         --- End of inner exception stack trace ---
         at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
         at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.ContinueDeserialize(ReadBufferState& bufferState, JsonReaderState& jsonReaderState, ReadStack& readStack)
         at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.DeserializeAsync(Stream utf8Json, CancellationToken cancellationToken)
         at System.Net.Http.Json.HttpContentJsonExtensions.ReadFromJsonAsyncCore[T](HttpContent content, JsonSerializerOptions options, CancellationToken cancellationToken)
         at Furion.HttpRemote.ObjectContentConverter`1.ReadAsync(HttpResponseMessage httpResponseMessage, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpContentConverterFactory.ReadAsync[TResult](HttpResponseMessage httpResponseMessage, IHttpContentConverter[] converters, CancellationToken cancellationToken)
         at Furion.HttpRemote.HttpRemoteService.SendAsAsync[TResult](HttpRequestBuilder httpRequestBuilder, HttpCompletionOption completionOption, CancellationToken cancellationToken)
         at Furion.HttpRemote.DeclarativeManager.StartAsync[T]()
         at Furion.HttpRemote.HttpDeclarativeDispatchProxy.InvokeAsyncT[T](MethodInfo method, Object[] args)
         at System.Reflection.AsyncDispatchProxyGenerator.InvokeAsync[T](Object[] args)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.GetAccessTokenAsync()
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateAPAsync(CreateAPInput input)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
