// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System.Net.Http;
using System.Text;

namespace Admin.NET.Plugin.GreenDisplay.Handler;

/// <summary>
/// 自定义JSON HttpContent，确保正确的序列化和Content-Type
/// </summary>
public class JsonHttpContent : StringContent
{
    /// <summary>
    /// 创建JSON HttpContent
    /// </summary>
    /// <param name="value">要序列化的对象</param>
    public JsonHttpContent(object value) : base(SerializeObject(value), Encoding.UTF8, "application/json")
    {
    }

    /// <summary>
    /// 序列化对象为JSON字符串
    /// </summary>
    /// <param name="value">要序列化的对象</param>
    /// <returns>JSON字符串</returns>
    private static string SerializeObject(object value)
    {
        var settings = new JsonSerializerSettings
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            DateFormatString = "yyyy-MM-dd HH:mm:ss",
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            NullValueHandling = NullValueHandling.Ignore
        };

        return JsonConvert.SerializeObject(value, settings);
    }
}