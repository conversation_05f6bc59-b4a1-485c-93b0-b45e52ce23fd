<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
    <NoWarn>1701;1702;1591;8632</NoWarn>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <Copyright>Admin.NET</Copyright>
    <Description>Admin.NET 通用权限开发平台</Description>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="Configuration\**\*">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="wwwroot\**\*">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DocumentFormat.OpenXml" Version="3.3.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp.Scripting" Version="4.14.0" />
    <PackageReference Include="Rezero.Api" Version="1.8.24" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Admin.NET.Core\Admin.NET.Core.csproj" />
  </ItemGroup>

</Project>
