{"apifoxProject": "1.0.0", "$schema": {"app": "apifox", "type": "project", "version": "1.2.0"}, "info": {"name": "Admin.Net", "description": "", "mockRule": {"rules": [], "enableSystemRule": true}}, "apiCollection": [{"name": "根目录", "id": 29711526, "auth": {}, "parentId": 0, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "methodAndPath", "bodyType": ""}}, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "aPIJSON", "id": 29711668, "auth": {}, "parentId": 0, "serverId": "", "description": "说明文档：https://github.com/Tencent/APIJSON/blob/master/Document.md#3.1", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": ""}}, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "统一查询入口", "api": {"id": "151219333", "method": "post", "path": "/api/aPIJSON/get", "parameters": {"query": [], "path": [], "cookie": [], "header": []}, "auth": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "405647728", "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer"}, "type": {"type": "string"}, "message": {"type": "string"}, "result": {"type": "object", "properties": {"[]": {"type": "array", "items": {"type": "object", "properties": {}, "x-apifox-orders": []}}, "page": {"type": "integer", "description": "当前页码"}, "count": {"type": "integer", "description": "每页条数"}, "max": {"type": "integer", "description": "最大页数"}, "total": {"type": "integer", "description": "总条数"}}, "x-apifox-orders": ["[]", "page", "count", "max", "total"]}, "extras": {"type": "null"}, "time": {"type": "string"}}, "required": ["code", "type", "message", "result", "extras", "time"], "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}], "responseExamples": [], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"type": "object", "additionalProperties": {"$ref": "#/definitions/84275307"}, "x-apifox-orders": [], "properties": {}}, "example": "{\r\n    \"table1\": {\r\n        \"@column\": \"id\",\r\n       \"httpmethod\": \"Get\",\r\n    }\r\n}"}, "description": "参数：{\"[]\":{\"SYS_LOG_OP\":{}}}", "tags": ["aPIJSON"], "status": "released", "serverId": "", "operationId": "api-aPIJSON-Post", "sourceUrl": "", "ordering": 0, "cases": [{"id": 143284761, "type": "http", "path": null, "name": "单条查询", "responseId": 405647728, "parameters": {"query": [], "path": [], "cookie": [], "header": []}, "commonParameters": {"query": [], "body": [], "header": [], "cookie": []}, "requestBody": {"parameters": [], "data": "{\r\n    \"table1\": {\r\n        \"@column\": \"id,createtime,Actionname,loglevel,httpmethod,RequestParam\",//显示列\r\n        //\"Actionname\":\"SwaggerCheckUrl\",\r\n        //\"loglevel\":2,\r\n        //\"httpmethod!\":[\"POST\",\"GET\"],//!  not in\r\n        \"createtime{}\": \">=2024-3-1\", //逻辑运算\r\n        //\"isdelete\":0,           //bool 支持 1、0、true、false \r\n        \"RequestParam!\": null    //not null\r\n    }\r\n}", "generateMode": "normal"}, "auth": {}, "advancedSettings": {"disabledSystemHeaders": {}, "isDefaultUrlEncoding": 2, "disableUrlEncoding": false}, "requestResult": "{\"id\":\"temp.decba6c4-b3e2-40af-a30e-8c8d09865bf1\",\"cursor\":{\"position\":0,\"iteration\":0,\"length\":1,\"cycles\":1,\"empty\":false,\"eof\":false,\"bof\":true,\"cr\":false,\"ref\":\"decba6c4-b3e2-40af-a30e-8c8d09865bf1\",\"requestIndex\":0,\"httpRequestId\":\"7e107ae6-74d4-44c8-8582-e9bfefb5e611\"},\"type\":\"http\",\"response\":{\"id\":\"7b5fc148-4f73-4e56-b054-26bebe9db57a\",\"status\":\"OK\",\"code\":200,\"header\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Wed, 13 Mar 2024 08:19:05 GMT\"},{\"key\":\"Server\",\"value\":\"Kestrel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"Admin.NET\",\"value\":\"Admin.NET\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86375\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-14T07:18:51.6721222Z\"}],\"trailer\":[],\"stream\":{\"type\":\"Buffer\",\"data\":[123,34,99,111,100,101,34,58,50,48,48,44,34,116,121,112,101,34,58,34,115,117,99,99,101,115,115,34,44,34,109,101,115,115,97,103,101,34,58,34,34,44,34,114,101,115,117,108,116,34,58,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,52,54,57,54,51,53,53,48,48,50,49,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,48,58,51,51,58,50,51,46,51,56,55,53,57,48,51,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,76,111,103,76,101,118,101,108,34,58,50,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,91,93,92,34,58,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,125,44,92,34,112,97,103,101,92,34,58,51,44,92,34,99,111,117,110,116,92,34,58,50,44,92,34,113,117,101,114,121,92,34,58,51,125,44,92,34,116,111,116,97,108,64,92,34,58,92,34,47,91,93,47,116,111,116,97,108,92,34,125,34,125,125,44,34,101,120,116,114,97,115,34,58,110,117,108,108,44,34,116,105,109,101,34,58,34,50,48,50,52,45,48,51,45,49,51,32,49,54,58,49,57,58,48,54,34,125]},\"cookie\":[],\"responseTime\":99,\"responseSize\":328,\"type\":\"http\",\"tempFilePath\":\"\",\"timingPhases\":{\"prepare\":2.698800027370453,\"wait\":0.3564000129699707,\"dns\":0,\"tcp\":0,\"firstByte\":96.46359997987747,\"download\":1.736199975013733,\"process\":0.03470003604888916,\"total\":101.28970003128052}},\"request\":{\"url\":{\"protocol\":\"http\",\"port\":\"5005\",\"path\":[\"api\",\"aPIJSON\",\"get\"],\"host\":[\"localhost\"],\"query\":[],\"variable\":[]},\"header\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"system\":true},{\"key\":\"Accept\",\"value\":\"*/*\",\"system\":true},{\"key\":\"Host\",\"value\":\"localhost:5005\",\"system\":true},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\",\"system\":true},{\"key\":\"Connection\",\"value\":\"keep-alive\",\"system\":true},{\"key\":\"Content-Length\",\"value\":157,\"system\":true}],\"method\":\"POST\",\"baseUrl\":\"http://localhost:5005\",\"body\":{\"mode\":\"raw\",\"raw\":\"{\\n  \\\"table1\\\": {\\n    \\\"@column\\\": \\\"id,createtime,Actionname,loglevel,httpmethod,RequestParam\\\",\\n    \\\"createtime{}\\\": \\\">=2024-3-1\\\",\\n    \\\"RequestParam!\\\": null\\n  }\\n}\",\"generateMode\":\"normal\",\"type\":\"application/json\"},\"auth\":{\"type\":\"bearer\",\"bearer\":[{\"type\":\"any\",\"value\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"key\":\"token\"}]},\"type\":\"http\"},\"history\":{\"execution\":{\"verbose\":false,\"sessions\":{},\"data\":[{\"request\":{\"method\":\"POST\",\"href\":\"http://localhost:5005/api/aPIJSON/get\",\"headers\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\"},{\"key\":\"Accept\",\"value\":\"*/*\"},{\"key\":\"Host\",\"value\":\"localhost:5005\"},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\"},{\"key\":\"Connection\",\"value\":\"keep-alive\"},{\"key\":\"Content-Length\",\"value\":\"157\"}],\"httpVersion\":\"1.1\"},\"response\":{\"statusCode\":200,\"headers\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Wed, 13 Mar 2024 08:19:05 GMT\"},{\"key\":\"Server\",\"value\":\"Kestrel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"Admin.NET\",\"value\":\"Admin.NET\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86375\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-14T07:18:51.6721222Z\"}],\"httpVersion\":\"1.1\"},\"timings\":{\"start\":1710317946491,\"requestStart\":1710317946493,\"offset\":{\"request\":2.698800027370453,\"socket\":3.0552000403404236,\"response\":99.5188000202179,\"end\":101.25499999523163,\"lookup\":3.0552000403404236,\"connect\":3.0552000403404236,\"done\":101.28970003128052}}}]}},\"responseValidation\":{\"schema\":{\"valid\":true,\"message\":\"\",\"errors\":null},\"responseCode\":{\"valid\":true}},\"passed\":true,\"metaInfo\":{\"httpApiId\":151219333,\"httpApiCaseId\":143284761,\"httpApiName\":\"统一入口\",\"httpApiPath\":\"/api/aPIJSON/get\",\"httpApiMethod\":\"post\",\"httpApiCaseName\":\"单条查询\"}}", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}, {"id": 143272865, "type": "http", "path": null, "name": "列表查询", "responseId": 0, "parameters": {"query": [], "path": [], "cookie": [], "header": []}, "commonParameters": {"query": [], "body": [], "header": [], "cookie": []}, "requestBody": {"parameters": [], "data": "{\r\n    \"[]\": {\r\n        \"table1\": {\r\n            \"@column\": \"id,createtime,httpmethod,RequestUrl,actionname,RequestParam,Elapsed\",//需要显示的列名\r\n            //\"httpmethod\": \"POST\",//条件查询\r\n            //\"RequestUrl$\": \"%swagger%\", //$模糊查询\r\n            //\"@order\": \"RequestParam desc,createtime,actionname desc\", //按最新时间排序:-/desc 均为倒序       \r\n            //\"@count\": \"10\", //前n条  很少用到 \r\n            //\"RequestParam\":null,//匹配null or '',\r\n            \"createtime%\":\"2024-3-5,2024-3-14\",// between 日期过滤\r\n            \"Elapsed%\":\"1,20\"\r\n    }\r\n}", "generateMode": "normal"}, "auth": {}, "advancedSettings": {"disabledSystemHeaders": {}, "isDefaultUrlEncoding": 2, "disableUrlEncoding": false}, "requestResult": "{\"id\":\"temp.1c48ba22-4372-4ab1-a874-a79449144e5d\",\"cursor\":{\"position\":0,\"iteration\":0,\"length\":1,\"cycles\":1,\"empty\":false,\"eof\":false,\"bof\":true,\"cr\":false,\"ref\":\"1c48ba22-4372-4ab1-a874-a79449144e5d\",\"requestIndex\":0,\"httpRequestId\":\"70f19533-b333-460b-beeb-2e5ecbc5298c\"},\"type\":\"http\",\"response\":{\"id\":\"0f169b35-6729-4512-956d-2081d9fdb91d\",\"status\":\"OK\",\"code\":200,\"header\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Thu, 14 Mar 2024 02:00:47 GMT\"},{\"key\":\"Server\",\"value\":\"Ke<PERSON><PERSON>\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"Admin.NET\",\"value\":\"Admin.NET\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86398\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-15T02:00:05.6689995Z\"}],\"trailer\":[],\"stream\":{\"type\":\"Buffer\",\"data\":[123,34,99,111,100,101,34,58,50,48,48,44,34,116,121,112,101,34,58,34,115,117,99,99,101,115,115,34,44,34,109,101,115,115,97,103,101,34,58,34,34,44,34,114,101,115,117,108,116,34,58,123,34,91,93,34,58,91,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,51,48,49,56,48,54,55,57,50,51,55,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,53,32,49,54,58,50,48,58,52,53,46,50,57,56,49,57,49,57,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,115,119,97,103,103,101,114,47,99,104,101,99,107,85,114,108,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,83,119,97,103,103,101,114,67,104,101,99,107,85,114,108,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,110,117,108,108,44,34,69,108,97,112,115,101,100,34,58,49,56,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,52,54,50,54,50,54,48,51,48,55,55,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,48,57,58,52,55,58,52,53,46,51,49,51,48,53,50,53,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,115,119,97,103,103,101,114,47,99,104,101,99,107,85,114,108,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,83,119,97,103,103,101,114,67,104,101,99,107,85,114,108,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,110,117,108,108,44,34,69,108,97,112,115,101,100,34,58,49,55,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,52,54,51,50,57,56,51,50,55,55,51,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,48,57,58,53,50,58,48,55,46,57,50,57,50,54,52,57,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,115,119,97,103,103,101,114,47,99,104,101,99,107,85,114,108,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,83,119,97,103,103,101,114,67,104,101,99,107,85,114,108,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,110,117,108,108,44,34,69,108,97,112,115,101,100,34,58,49,53,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,52,54,51,54,51,52,53,55,56,54,49,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,48,57,58,53,52,58,49,57,46,50,55,55,52,54,53,55,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,115,119,97,103,103,101,114,47,99,104,101,99,107,85,114,108,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,83,119,97,103,103,101,114,67,104,101,99,107,85,114,108,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,110,117,108,108,44,34,69,108,97,112,115,101,100,34,58,49,57,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,52,54,51,56,54,55,57,55,56,57,51,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,48,57,58,53,53,58,53,48,46,52,52,57,55,52,57,54,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,115,119,97,103,103,101,114,47,99,104,101,99,107,85,114,108,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,83,119,97,103,103,101,114,67,104,101,99,107,85,114,108,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,110,117,108,108,44,34,69,108,97,112,115,101,100,34,58,49,53,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,52,54,57,50,57,50,49,52,50,55,55,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,48,58,51,49,58,48,57,46,50,54,51,55,53,49,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,115,119,97,103,103,101,114,47,99,104,101,99,107,85,114,108,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,83,119,97,103,103,101,114,67,104,101,99,107,85,114,108,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,110,117,108,108,44,34,69,108,97,112,115,101,100,34,58,49,54,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,52,55,48,50,52,48,55,57,57,52,49,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,48,58,51,55,58,49,57,46,56,51,50,49,53,50,56,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,91,93,92,34,58,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,125,44,92,34,112,97,103,101,92,34,58,49,44,92,34,99,111,117,110,116,92,34,58,50,44,92,34,113,117,101,114,121,92,34,58,51,125,44,92,34,116,111,116,97,108,64,92,34,58,92,34,47,91,93,47,116,111,116,97,108,92,34,125,34,44,34,69,108,97,112,115,101,100,34,58,49,55,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,52,55,49,56,56,53,57,57,49,48,57,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,48,58,52,56,58,48,50,46,52,56,53,55,57,50,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,83,89,83,76,79,71,79,80,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,66,121,84,97,98,108,101,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,92,34,83,89,83,76,79,71,79,80,92,34,34,44,34,69,108,97,112,115,101,100,34,58,49,57,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,52,55,53,52,57,57,55,57,52,54,49,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,49,58,49,49,58,51,52,46,49,50,55,53,49,56,55,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,91,93,92,34,58,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,125,44,92,34,112,97,103,101,92,34,58,49,44,92,34,99,111,117,110,116,92,34,58,50,44,92,34,113,117,101,114,121,92,34,58,49,125,44,92,34,116,111,116,97,108,64,92,34,58,92,34,47,91,93,47,116,111,116,97,108,92,34,125,34,44,34,69,108,97,112,115,101,100,34,58,49,56,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,52,55,53,53,52,49,49,53,57,48,57,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,49,58,49,49,58,53,48,46,50,56,53,52,56,54,52,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,91,93,92,34,58,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,125,44,92,34,112,97,103,101,92,34,58,49,44,92,34,99,111,117,110,116,92,34,58,50,44,92,34,113,117,101,114,121,92,34,58,48,125,44,92,34,116,111,116,97,108,64,92,34,58,92,34,47,91,93,47,116,111,116,97,108,92,34,125,34,44,34,69,108,97,112,115,101,100,34,58,49,51,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,52,55,53,53,55,51,56,52,48,48,53,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,49,58,49,50,58,48,51,46,48,53,49,50,55,54,54,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,91,93,92,34,58,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,125,44,92,34,112,97,103,101,92,34,58,49,44,92,34,99,111,117,110,116,92,34,58,50,125,44,92,34,116,111,116,97,108,64,92,34,58,92,34,47,91,93,47,116,111,116,97,108,92,34,125,34,44,34,69,108,97,112,115,101,100,34,58,49,54,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,52,55,53,54,54,57,51,57,57,55,51,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,49,58,49,50,58,52,48,46,51,55,57,52,48,56,55,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,91,93,92,34,58,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,125,44,92,34,112,97,103,101,92,34,58,49,44,92,34,99,111,117,110,116,92,34,58,50,125,125,34,44,34,69,108,97,112,115,101,100,34,58,49,52,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,52,56,49,56,50,52,56,53,51,49,55,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,49,58,53,50,58,52,52,46,56,53,51,53,57,52,54,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,115,119,97,103,103,101,114,47,99,104,101,99,107,85,114,108,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,83,119,97,103,103,101,114,67,104,101,99,107,85,114,108,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,110,117,108,108,44,34,69,108,97,112,115,101,100,34,58,49,57,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,48,50,57,49,51,50,52,55,52,49,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,52,58,49,48,58,48,50,46,53,48,55,49,56,48,52,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,115,119,97,103,103,101,114,47,99,104,101,99,107,85,114,108,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,83,119,97,103,103,101,114,67,104,101,99,107,85,114,108,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,110,117,108,108,44,34,69,108,97,112,115,101,100,34,58,49,56,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,48,52,50,50,50,57,55,49,53,55,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,52,58,49,56,58,51,52,46,49,49,56,52,57,56,53,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,125,44,92,34,116,111,116,97,108,64,92,34,58,92,34,47,91,93,47,116,111,116,97,108,92,34,125,34,44,34,69,108,97,112,115,101,100,34,58,49,54,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,48,52,51,48,57,53,54,56,54,57,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,52,58,49,57,58,48,55,46,57,52,53,57,49,56,55,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,125,44,92,34,116,111,116,97,108,64,92,34,58,92,34,47,91,93,47,116,111,116,97,108,92,34,125,34,44,34,69,108,97,112,115,101,100,34,58,49,54,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,48,52,52,56,49,56,55,55,49,55,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,52,58,50,48,58,49,53,46,50,53,51,55,52,49,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,92,34,113,117,101,114,121,92,34,58,92,34,50,92,34,125,44,92,34,116,111,116,97,108,64,92,34,58,92,34,47,91,93,47,116,111,116,97,108,92,34,125,34,44,34,69,108,97,112,115,101,100,34,58,49,55,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,49,48,52,49,52,53,53,52,50,57,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,52,58,53,56,58,53,50,46,55,48,53,55,50,56,53,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,92,34,64,99,111,108,117,109,110,92,34,58,92,34,105,100,92,34,125,125,34,44,34,69,108,97,112,115,101,100,34,58,49,57,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,49,50,52,52,54,54,50,53,57,55,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,53,58,49,50,58,48,54,46,52,56,51,53,49,52,53,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,92,34,64,99,111,108,117,109,110,92,34,58,92,34,105,100,92,34,44,92,34,64,111,114,100,101,114,92,34,58,92,34,105,100,45,92,34,125,125,34,44,34,69,108,97,112,115,101,100,34,58,50,48,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,49,50,52,54,51,53,51,50,50,49,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,53,58,49,50,58,49,51,46,48,56,54,57,57,50,54,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,92,34,64,99,111,108,117,109,110,92,34,58,92,34,105,100,92,34,44,92,34,64,111,114,100,101,114,92,34,58,92,34,105,100,43,92,34,125,125,34,44,34,69,108,97,112,115,101,100,34,58,49,57,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,49,50,52,55,55,57,53,50,54,57,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,53,58,49,50,58,49,56,46,55,50,48,51,52,54,57,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,92,34,64,99,111,108,117,109,110,92,34,58,92,34,105,100,92,34,44,92,34,64,111,114,100,101,114,92,34,58,92,34,105,100,92,34,125,125,34,44,34,69,108,97,112,115,101,100,34,58,49,57,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,49,50,53,50,55,51,52,50,55,55,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,53,58,49,50,58,51,56,46,48,49,51,48,56,48,56,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,92,34,64,99,111,108,117,109,110,92,34,58,92,34,105,100,44,99,114,101,97,116,101,116,105,109,101,92,34,44,92,34,64,111,114,100,101,114,92,34,58,92,34,99,114,101,97,116,101,116,105,109,101,92,34,125,125,34,44,34,69,108,97,112,115,101,100,34,58,49,53,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,49,50,53,53,49,56,53,50,50,49,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,53,58,49,50,58,52,55,46,53,56,55,53,49,52,53,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,92,34,64,99,111,108,117,109,110,92,34,58,92,34,105,100,44,99,114,101,97,116,101,116,105,109,101,92,34,44,92,34,64,111,114,100,101,114,92,34,58,92,34,99,114,101,97,116,101,116,105,109,101,45,92,34,125,125,34,44,34,69,108,97,112,115,101,100,34,58,49,53,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,49,51,57,57,55,52,57,55,48,49,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,53,58,50,50,58,49,50,46,50,57,50,57,51,57,49,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,91,93,92,34,58,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,92,34,64,99,111,108,117,109,110,92,34,58,92,34,105,100,44,99,114,101,97,116,101,116,105,109,101,92,34,44,92,34,64,111,114,100,101,114,92,34,58,92,34,99,114,101,97,116,101,116,105,109,101,45,92,34,125,125,125,34,44,34,69,108,97,112,115,101,100,34,58,49,56,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,49,52,55,48,54,51,51,48,50,57,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,53,58,50,54,58,52,57,46,49,56,48,51,57,54,54,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,91,93,92,34,58,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,92,34,64,99,111,108,117,109,110,92,34,58,92,34,105,100,44,99,114,101,97,116,101,116,105,109,101,92,34,44,92,34,64,111,114,100,101,114,92,34,58,92,34,99,114,101,97,116,101,116,105,109,101,45,92,34,125,44,92,34,112,97,103,101,92,34,58,49,44,92,34,99,111,117,110,116,92,34,58,53,48,44,92,34,113,117,101,114,121,92,34,58,50,125,44,92,34,116,111,116,97,108,64,92,34,58,92,34,92,34,125,34,44,34,69,108,97,112,115,101,100,34,58,49,57,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,49,53,49,52,50,48,56,48,54,57,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,53,58,50,57,58,51,57,46,51,57,53,52,54,52,56,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,115,119,97,103,103,101,114,47,99,104,101,99,107,85,114,108,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,83,119,97,103,103,101,114,67,104,101,99,107,85,114,108,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,110,117,108,108,44,34,69,108,97,112,115,101,100,34,58,49,54,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,49,53,49,57,52,57,53,50,51,55,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,53,58,51,48,58,48,48,46,48,52,56,49,51,52,52,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,91,93,92,34,58,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,92,34,64,99,111,108,117,109,110,92,34,58,92,34,105,100,44,99,114,101,97,116,101,116,105,109,101,92,34,44,92,34,64,111,114,100,101,114,92,34,58,92,34,99,114,101,97,116,101,116,105,109,101,45,92,34,125,44,92,34,112,97,103,101,92,34,58,49,44,92,34,99,111,117,110,116,92,34,58,53,48,44,92,34,113,117,101,114,121,92,34,58,50,125,44,92,34,116,111,116,97,108,92,34,58,92,34,92,34,125,34,44,34,69,108,97,112,115,101,100,34,58,49,57,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,49,54,48,57,52,49,56,48,53,51,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,53,58,51,53,58,53,49,46,51,48,57,56,56,51,55,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,115,119,97,103,103,101,114,47,99,104,101,99,107,85,114,108,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,83,119,97,103,103,101,114,67,104,101,99,107,85,114,108,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,110,117,108,108,44,34,69,108,97,112,115,101,100,34,58,50,48,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,49,54,52,48,51,49,54,52,56,53,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,53,58,51,55,58,53,50,46,48,48,54,56,55,49,52,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,92,34,64,99,111,108,117,109,110,92,34,58,92,34,105,100,44,99,114,101,97,116,101,116,105,109,101,92,34,44,92,34,105,100,92,34,58,92,34,51,50,54,53,49,54,48,57,52,49,56,48,53,51,92,34,125,125,34,44,34,69,108,97,112,115,101,100,34,58,49,56,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,50,50,48,49,50,52,57,54,48,53,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,54,58,49,52,58,50,51,46,49,53,49,54,53,53,50,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,115,119,97,103,103,101,114,47,99,104,101,99,107,85,114,108,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,83,119,97,103,103,101,114,67,104,101,99,107,85,114,108,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,110,117,108,108,44,34,69,108,97,112,115,101,100,34,58,49,53,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,50,50,50,52,48,55,48,57,56,49,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,54,58,49,53,58,53,50,46,50,57,55,55,48,50,52,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,92,34,64,99,111,108,117,109,110,92,34,58,92,34,99,111,117,110,116,40,105,100,41,58,115,92,34,125,125,34,44,34,69,108,97,112,115,101,100,34,58,49,57,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,50,50,50,54,54,55,48,54,54,49,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,54,58,49,54,58,48,50,46,52,53,50,52,56,51,56,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,92,34,64,99,111,108,117,109,110,92,34,58,92,34,99,111,117,110,116,40,49,41,58,115,92,34,125,125,34,44,34,69,108,97,112,115,101,100,34,58,49,53,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,50,50,51,48,53,51,57,51,51,51,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,54,58,49,54,58,49,55,46,53,54,52,54,54,55,51,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,92,34,64,99,111,108,117,109,110,92,34,58,92,34,99,111,117,110,116,40,49,41,58,99,111,117,110,116,92,34,125,125,34,44,34,69,108,97,112,115,101,100,34,58,49,57,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,50,50,51,53,48,48,48,51,56,57,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,54,58,49,54,58,51,52,46,57,57,48,54,50,51,50,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,92,34,64,99,111,108,117,109,110,92,34,58,92,34,99,111,117,110,116,40,49,41,58,230,149,176,233,135,143,92,34,125,125,34,44,34,69,108,97,112,115,101,100,34,58,49,55,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,50,50,51,57,48,54,52,51,56,57,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,54,58,49,54,58,53,48,46,56,54,53,56,49,49,57,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,97,112,105,47,97,80,73,74,83,79,78,47,103,101,116,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,92,34,64,99,111,108,117,109,110,92,34,58,92,34,99,111,117,110,116,40,49,41,58,230,157,161,230,149,176,92,34,125,125,34,44,34,69,108,97,112,115,101,100,34,58,49,56,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,50,57,57,51,49,57,51,55,57,55,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,55,58,48,53,58,53,54,46,54,56,51,50,51,48,57,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,115,119,97,103,103,101,114,47,99,104,101,99,107,85,114,108,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,83,119,97,103,103,101,114,67,104,101,99,107,85,114,108,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,110,117,108,108,44,34,69,108,97,112,115,101,100,34,58,49,53,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,51,50,54,54,50,50,48,51,53,55,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,55,58,50,51,58,52,51,46,49,57,51,54,55,53,52,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,85,84,34,44,34,82,101,113,117,101,115,116,85,114,108,34,58,34,104,116,116,112,58,47,47,108,111,99,97,108,104,111,115,116,58,53,48,48,53,47,115,119,97,103,103,101,114,47,99,104,101,99,107,85,114,108,34,44,34,65,99,116,105,111,110,78,97,109,101,34,58,34,83,119,97,103,103,101,114,67,104,101,99,107,85,114,108,34,44,34,82,101,113,117,101,115,116,80,97,114,97,109,34,58,34,123,92,34,83,89,83,76,79,71,79,80,92,34,58,123,92,34,64,99,111,108,117,109,110,92,34,58,92,34,105,100,44,99,114,101,97,116,101,116,105,109,101,92,34,44,92,34,105,100,92,34,58,92,34,51,50,54,53,49,54,48,57,52,49,56,48,53,51,92,34,125,125,34,44,34,69,108,97,112,115,101,100,34,58,49,54,125,125,93,44,34,116,111,116,97,108,34,58,51,55,125,44,34,101,120,116,114,97,115,34,58,110,117,108,108,44,34,116,105,109,101,34,58,34,50,48,50,52,45,48,51,45,49,52,32,49,48,58,48,48,58,52,55,34,125]},\"cookie\":[],\"responseTime\":137,\"responseSize\":9324,\"type\":\"http\",\"tempFilePath\":\"\",\"timingPhases\":{\"prepare\":1.5074999928474426,\"wait\":0.32520002126693726,\"dns\":0,\"tcp\":0,\"firstByte\":135.28540003299713,\"download\":1.2343999743461609,\"process\":0.05519998073577881,\"total\":138.40770000219345}},\"request\":{\"url\":{\"protocol\":\"http\",\"port\":\"5005\",\"path\":[\"api\",\"aPIJSON\",\"get\"],\"host\":[\"localhost\"],\"query\":[],\"variable\":[]},\"header\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"system\":true},{\"key\":\"Accept\",\"value\":\"*/*\",\"system\":true},{\"key\":\"Host\",\"value\":\"localhost:5005\",\"system\":true},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\",\"system\":true},{\"key\":\"Connection\",\"value\":\"keep-alive\",\"system\":true},{\"key\":\"Content-Length\",\"value\":195,\"system\":true}],\"method\":\"POST\",\"baseUrl\":\"http://localhost:5005\",\"body\":{\"mode\":\"raw\",\"raw\":\"{\\n  \\\"[]\\\": {\\n    \\\"table1\\\": {\\n      \\\"@column\\\": \\\"id,createtime,httpmethod,RequestUrl,actionname,RequestParam,Elapsed\\\",\\n      \\\"createtime%\\\": \\\"2024-3-5,2024-3-14\\\",\\n      \\\"Elapsed%\\\": \\\"1,20\\\"\\n    }\\n  }\\n}\",\"generateMode\":\"normal\",\"type\":\"application/json\"},\"auth\":{\"type\":\"bearer\",\"bearer\":[{\"type\":\"any\",\"value\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"key\":\"token\"}]},\"type\":\"http\"},\"history\":{\"execution\":{\"verbose\":false,\"sessions\":{},\"data\":[{\"request\":{\"method\":\"POST\",\"href\":\"http://localhost:5005/api/aPIJSON/get\",\"headers\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\"},{\"key\":\"Accept\",\"value\":\"*/*\"},{\"key\":\"Host\",\"value\":\"localhost:5005\"},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\"},{\"key\":\"Connection\",\"value\":\"keep-alive\"},{\"key\":\"Content-Length\",\"value\":\"195\"}],\"httpVersion\":\"1.1\"},\"response\":{\"statusCode\":200,\"headers\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Thu, 14 Mar 2024 02:00:47 GMT\"},{\"key\":\"Server\",\"value\":\"Kestrel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"Admin.NET\",\"value\":\"Admin.NET\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86398\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-15T02:00:05.6689995Z\"}],\"httpVersion\":\"1.1\"},\"timings\":{\"start\":1710381647691,\"requestStart\":1710381647692,\"offset\":{\"request\":1.5074999928474426,\"socket\":1.8327000141143799,\"response\":137.1181000471115,\"end\":138.35250002145767,\"lookup\":1.8327000141143799,\"connect\":1.8327000141143799,\"done\":138.40770000219345}}}]}},\"responseValidation\":{},\"passed\":true,\"metaInfo\":{\"httpApiId\":151219333,\"httpApiCaseId\":143272865,\"httpApiName\":\"统一入口\",\"httpApiPath\":\"/api/aPIJSON/get\",\"httpApiMethod\":\"post\",\"httpApiCaseName\":\"列表查询\"}}", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}, {"id": 143191882, "type": "http", "path": null, "name": "分页查询", "responseId": 405647728, "parameters": {"query": [], "path": [], "cookie": [], "header": []}, "commonParameters": {"query": [], "body": [], "header": [], "cookie": []}, "requestBody": {"parameters": [], "data": "{\r\n    \"[]\": {//列表\r\n        \"SYSLOGOP\": {//表名\r\n            \"@column\": \"id,createtime\",//只查询两列\r\n            \"@order\":\"createtime-\"     //-倒序排序\r\n        },\r\n        \"page\": 1, //分页查询：第1页\r\n        \"count\": 3, //每页3条\r\n        //\"query\": 1 //查询内容:   0-对象，1-总数，2-数据、总数，默认为2\r\n    },\r\n    //\"total\": \"\", //总数   默认返回总数，不用传\r\n    \r\n}", "generateMode": "normal"}, "auth": {}, "advancedSettings": {"disabledSystemHeaders": {}, "isDefaultUrlEncoding": 2, "disableUrlEncoding": false}, "requestResult": "{\"id\":\"temp.bcfe838e-0335-42f2-ae40-05fb07285447\",\"cursor\":{\"position\":0,\"iteration\":0,\"length\":1,\"cycles\":1,\"empty\":false,\"eof\":false,\"bof\":true,\"cr\":false,\"ref\":\"bcfe838e-0335-42f2-ae40-05fb07285447\",\"requestIndex\":0,\"httpRequestId\":\"74d59ab7-76bd-4c77-9bcf-714ff5eb470e\"},\"type\":\"http\",\"response\":{\"id\":\"673bc01a-a4df-4514-b2e9-b13172c5eca8\",\"status\":\"OK\",\"code\":200,\"header\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Tue, 12 Mar 2024 08:29:32 GMT\"},{\"key\":\"Server\",\"value\":\"<PERSON><PERSON><PERSON>\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"Admin.NET\",\"value\":\"Admin.NET\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86398\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-13T08:27:50.0233019Z\"}],\"trailer\":[],\"stream\":{\"type\":\"Buffer\",\"data\":[123,34,99,111,100,101,34,58,50,48,48,44,34,116,121,112,101,34,58,34,115,117,99,99,101,115,115,34,44,34,109,101,115,115,97,103,101,34,58,34,34,44,34,114,101,115,117,108,116,34,58,123,34,91,93,34,58,91,123,34,83,89,83,76,79,71,79,80,34,58,123,34,73,100,34,58,51,50,55,56,53,49,52,51,50,52,49,53,52,49,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,49,50,32,49,54,58,50,57,58,50,55,46,56,48,55,57,53,49,52,34,125,125,44,123,34,83,89,83,76,79,71,79,80,34,58,123,34,73,100,34,58,51,50,55,56,53,49,49,56,51,48,53,56,54,49,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,49,50,32,49,54,58,50,55,58,53,48,46,52,48,50,49,56,34,125,125,44,123,34,83,89,83,76,79,71,79,80,34,58,123,34,73,100,34,58,51,50,55,56,53,49,49,53,55,51,54,51,56,57,44,34,67,114,101,97,116,101,84,105,109,101,34,58,34,50,48,50,52,45,48,51,45,49,50,32,49,54,58,50,55,58,52,48,46,51,54,53,57,51,48,54,34,125,125,93,44,34,112,97,103,101,34,58,49,44,34,99,111,117,110,116,34,58,51,44,34,109,97,120,34,58,49,50,49,44,34,116,111,116,97,108,34,58,51,54,49,125,44,34,101,120,116,114,97,115,34,58,110,117,108,108,44,34,116,105,109,101,34,58,34,50,48,50,52,45,48,51,45,49,50,32,49,54,58,50,57,58,51,51,34,125]},\"cookie\":[],\"responseTime\":72,\"responseSize\":376,\"type\":\"http\",\"tempFilePath\":\"\",\"timingPhases\":{\"prepare\":1.7229999899864197,\"wait\":0.46160000562667847,\"dns\":0,\"tcp\":0,\"firstByte\":69.22839999198914,\"download\":1.886900007724762,\"process\":0.04899996519088745,\"total\":73.34889996051788}},\"request\":{\"url\":{\"protocol\":\"http\",\"port\":\"5005\",\"path\":[\"api\",\"aPIJSON\",\"get\"],\"host\":[\"localhost\"],\"query\":[],\"variable\":[]},\"header\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"system\":true},{\"key\":\"Accept\",\"value\":\"*/*\",\"system\":true},{\"key\":\"Host\",\"value\":\"localhost:5005\",\"system\":true},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\",\"system\":true},{\"key\":\"Connection\",\"value\":\"keep-alive\",\"system\":true},{\"key\":\"Content-Length\",\"value\":136,\"system\":true}],\"method\":\"POST\",\"baseUrl\":\"http://localhost:5005\",\"body\":{\"mode\":\"raw\",\"raw\":\"{\\n  \\\"[]\\\": {\\n    \\\"SYSLOGOP\\\": {\\n      \\\"@column\\\": \\\"id,createtime\\\",\\n      \\\"@order\\\": \\\"createtime-\\\"\\n    },\\n    \\\"page\\\": 1,\\n    \\\"count\\\": 3\\n  }\\n}\",\"generateMode\":\"normal\",\"type\":\"application/json\"},\"auth\":{\"type\":\"bearer\",\"bearer\":[{\"type\":\"any\",\"value\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"key\":\"token\"}]},\"type\":\"http\"},\"history\":{\"execution\":{\"verbose\":false,\"sessions\":{},\"data\":[{\"request\":{\"method\":\"POST\",\"href\":\"http://localhost:5005/api/aPIJSON/get\",\"headers\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\"},{\"key\":\"Accept\",\"value\":\"*/*\"},{\"key\":\"Host\",\"value\":\"localhost:5005\"},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\"},{\"key\":\"Connection\",\"value\":\"keep-alive\"},{\"key\":\"Content-Length\",\"value\":\"136\"}],\"httpVersion\":\"1.1\"},\"response\":{\"statusCode\":200,\"headers\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Tue, 12 Mar 2024 08:29:32 GMT\"},{\"key\":\"Server\",\"value\":\"Kestrel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"Admin.NET\",\"value\":\"Admin.NET\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86398\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-13T08:27:50.0233019Z\"}],\"httpVersion\":\"1.1\"},\"timings\":{\"start\":1710232173277,\"requestStart\":1710232173279,\"offset\":{\"request\":1.7229999899864197,\"socket\":2.184599995613098,\"response\":71.41299998760223,\"end\":73.299899995327,\"lookup\":2.184599995613098,\"connect\":2.184599995613098,\"done\":73.34889996051788}}}]}},\"responseValidation\":{},\"passed\":true,\"metaInfo\":{\"httpApiId\":151219333,\"httpApiCaseId\":143191882,\"httpApiName\":\"统一入口\",\"httpApiPath\":\"/api/aPIJSON/get\",\"httpApiMethod\":\"post\",\"httpApiCaseName\":\"分页查询\"}}", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}, {"id": 143312444, "type": "http", "path": null, "name": "查询数量（聚合函数）", "responseId": 0, "parameters": {"query": [], "path": [], "cookie": [], "header": []}, "commonParameters": {"query": [], "body": [], "header": [], "cookie": []}, "requestBody": {"parameters": [], "data": "{\r\n    \"SYSLOGOP\": {\r\n        \"@column\": \"count(1):数量,sum(id):合计\",//支持聚合函数count、sum等，  ：后跟别名\r\n        \"httpmethod\":\"GET\"\r\n    }\r\n}", "generateMode": "normal"}, "auth": {}, "advancedSettings": {"disabledSystemHeaders": {}, "isDefaultUrlEncoding": 2, "disableUrlEncoding": false}, "requestResult": "{\"id\":\"temp.8cdec2eb-2a6e-4b14-b841-d45980eb6969\",\"cursor\":{\"position\":0,\"iteration\":0,\"length\":1,\"cycles\":1,\"empty\":false,\"eof\":false,\"bof\":true,\"cr\":false,\"ref\":\"8cdec2eb-2a6e-4b14-b841-d45980eb6969\",\"requestIndex\":0,\"httpRequestId\":\"b833e53d-ca56-464c-a0f3-bf293186f3c0\"},\"type\":\"http\",\"response\":{\"id\":\"c4a883ba-8b68-4bcf-b155-5300deb9b77e\",\"status\":\"OK\",\"code\":200,\"header\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"<PERSON><PERSON>, 12 Mar 2024 03:49:08 GMT\"},{\"key\":\"Server\",\"value\":\"Kestrel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"Admin.NET\",\"value\":\"Admin.NET\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86393\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-13T03:46:44.5267221Z\"}],\"trailer\":[],\"stream\":{\"type\":\"Buffer\",\"data\":[123,34,99,111,100,101,34,58,50,48,48,44,34,116,121,112,101,34,58,34,115,117,99,99,101,115,115,34,44,34,109,101,115,115,97,103,101,34,58,34,34,44,34,114,101,115,117,108,116,34,58,123,34,83,89,83,76,79,71,79,80,34,58,123,34,230,149,176,233,135,143,34,58,54,44,34,229,144,136,232,174,161,34,58,49,57,53,56,55,56,50,53,54,53,53,56,55,53,48,125,125,44,34,101,120,116,114,97,115,34,58,110,117,108,108,44,34,116,105,109,101,34,58,34,50,48,50,52,45,48,51,45,49,50,32,49,49,58,52,57,58,48,56,34,125]},\"cookie\":[],\"responseTime\":57,\"responseSize\":145,\"type\":\"http\",\"tempFilePath\":\"\",\"timingPhases\":{\"prepare\":2.178399980068207,\"wait\":0.46469998359680176,\"dns\":0,\"tcp\":0,\"firstByte\":54.569000005722046,\"download\":1.8371000289916992,\"process\":0.08829998970031738,\"total\":59.13749998807907}},\"request\":{\"url\":{\"protocol\":\"http\",\"port\":\"5005\",\"path\":[\"api\",\"aPIJSON\",\"get\"],\"host\":[\"localhost\"],\"query\":[],\"variable\":[]},\"header\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"system\":true},{\"key\":\"Accept\",\"value\":\"*/*\",\"system\":true},{\"key\":\"Host\",\"value\":\"localhost:5005\",\"system\":true},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\",\"system\":true},{\"key\":\"Connection\",\"value\":\"keep-alive\",\"system\":true},{\"key\":\"Content-Length\",\"value\":96,\"system\":true}],\"method\":\"POST\",\"baseUrl\":\"http://localhost:5005\",\"body\":{\"mode\":\"raw\",\"raw\":\"{\\n  \\\"SYSLOGOP\\\": {\\n    \\\"@column\\\": \\\"count(1):数量,sum(id):合计\\\",\\n    \\\"httpmethod\\\": \\\"GET\\\"\\n  }\\n}\",\"generateMode\":\"normal\",\"type\":\"application/json\"},\"auth\":{\"type\":\"bearer\",\"bearer\":[{\"type\":\"any\",\"value\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"key\":\"token\"}]},\"type\":\"http\"},\"history\":{\"execution\":{\"verbose\":false,\"sessions\":{},\"data\":[{\"request\":{\"method\":\"POST\",\"href\":\"http://localhost:5005/api/aPIJSON/get\",\"headers\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\"},{\"key\":\"Accept\",\"value\":\"*/*\"},{\"key\":\"Host\",\"value\":\"localhost:5005\"},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\"},{\"key\":\"Connection\",\"value\":\"keep-alive\"},{\"key\":\"Content-Length\",\"value\":\"96\"}],\"httpVersion\":\"1.1\"},\"response\":{\"statusCode\":200,\"headers\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Tue, 12 Mar 2024 03:49:08 GMT\"},{\"key\":\"Server\",\"value\":\"Kestrel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"Admin.NET\",\"value\":\"Admin.NET\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86393\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-13T03:46:44.5267221Z\"}],\"httpVersion\":\"1.1\"},\"timings\":{\"start\":1710215348896,\"requestStart\":1710215348898,\"offset\":{\"request\":2.178399980068207,\"socket\":2.6430999636650085,\"response\":57.212099969387054,\"end\":59.049199998378754,\"lookup\":2.6430999636650085,\"connect\":2.6430999636650085,\"done\":59.13749998807907}}}]}},\"responseValidation\":{},\"passed\":true,\"metaInfo\":{\"httpApiId\":151219333,\"httpApiCaseId\":143312444,\"httpApiName\":\"统一入口\",\"httpApiPath\":\"/api/aPIJSON/get\",\"httpApiMethod\":\"post\",\"httpApiCaseName\":\"查询数量（聚合函数）\"}}", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}, {"id": 143871046, "type": "http", "path": null, "name": "关联查询", "responseId": 0, "parameters": {"query": [], "path": [], "cookie": [], "header": []}, "commonParameters": {"query": [], "body": [], "header": [], "cookie": []}, "requestBody": {"parameters": [], "data": "//用于子表补充数据，没有实现join\r\n{\r\n    \"[]\": {\r\n        //必须先查子表，再循环补充主表数据\r\n        \"table3\": {\r\n            //\"@column\": \"id\",//必须在此返回后面的关联列\r\n            \"text\": \"update3\",\r\n            \"@order\": \"id-\", //只能在第一个表排序\r\n        },\r\n        \"table2\": {\r\n            \"id@\": \"/table3/table2id\",\r\n            //\"@order\": \"id-\", //这里排序无效\r\n        },\r\n        \"table1\": {\r\n            \"@column\": \"id,httpmethod\",\r\n            \"id@\": \"/table2/table1id\",\r\n        },\r\n        \"table4\": {\r\n            \"id@\": \"/table3/table4id\",\r\n        },\r\n        \"page\": 1,\r\n        \"count\": 3\r\n    }\r\n}", "generateMode": "normal"}, "auth": {}, "advancedSettings": {"disabledSystemHeaders": {}, "isDefaultUrlEncoding": 2, "disableUrlEncoding": false}, "requestResult": "{\"id\":\"temp.59b633e1-8099-48e6-9943-d0e27e50cae7\",\"cursor\":{\"position\":0,\"iteration\":0,\"length\":1,\"cycles\":1,\"empty\":false,\"eof\":false,\"bof\":true,\"cr\":false,\"ref\":\"59b633e1-8099-48e6-9943-d0e27e50cae7\",\"requestIndex\":0,\"httpRequestId\":\"a04d8d6c-aad7-4eaa-8fe4-11d3edaf05e7\"},\"type\":\"http\",\"response\":{\"id\":\"1a94a35f-3d89-44ef-8923-0f9c5d54e59d\",\"status\":\"OK\",\"code\":200,\"header\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Thu, 14 Mar 2024 03:53:26 GMT\"},{\"key\":\"Server\",\"value\":\"Kestrel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"Admin.NET\",\"value\":\"Admin.NET\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86399\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-15T03:53:27.0136472Z\"}],\"trailer\":[],\"stream\":{\"type\":\"Buffer\",\"data\":[123,34,99,111,100,101,34,58,50,48,48,44,34,116,121,112,101,34,58,34,115,117,99,99,101,115,115,34,44,34,109,101,115,115,97,103,101,34,58,34,34,44,34,114,101,115,117,108,116,34,58,123,34,91,93,34,58,91,123,34,116,97,98,108,101,51,34,58,123,34,105,100,34,58,51,51,51,44,34,116,97,98,108,101,50,105,100,34,58,50,51,44,34,116,101,120,116,34,58,34,117,112,100,97,116,101,51,34,44,34,116,97,98,108,101,52,105,100,34,58,52,52,125,44,34,116,97,98,108,101,50,34,58,123,34,105,100,34,58,50,51,44,34,116,101,120,116,34,58,34,110,101,119,103,101,116,34,44,34,116,97,98,108,101,49,105,100,34,58,51,50,54,53,51,50,51,54,56,53,48,55,53,55,44,34,105,115,68,101,108,101,116,101,34,58,48,125,44,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,51,50,51,54,56,53,48,55,53,55,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,68,69,76,69,84,69,34,125,44,34,116,97,98,108,101,52,34,58,123,34,105,100,34,58,52,52,44,34,116,101,120,116,34,58,34,52,52,34,125,125,44,123,34,116,97,98,108,101,51,34,58,123,34,105,100,34,58,51,51,44,34,116,97,98,108,101,50,105,100,34,58,50,51,44,34,116,101,120,116,34,58,34,117,112,100,97,116,101,51,34,44,34,116,97,98,108,101,52,105,100,34,58,52,51,125,44,34,116,97,98,108,101,50,34,58,123,34,105,100,34,58,50,51,44,34,116,101,120,116,34,58,34,110,101,119,103,101,116,34,44,34,116,97,98,108,101,49,105,100,34,58,51,50,54,53,51,50,51,54,56,53,48,55,53,55,44,34,105,115,68,101,108,101,116,101,34,58,48,125,44,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,51,50,51,54,56,53,48,55,53,55,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,68,69,76,69,84,69,34,125,125,44,123,34,116,97,98,108,101,51,34,58,123,34,105,100,34,58,51,50,44,34,116,97,98,108,101,50,105,100,34,58,50,50,44,34,116,101,120,116,34,58,34,117,112,100,97,116,101,51,34,44,34,116,97,98,108,101,52,105,100,34,58,52,50,125,44,34,116,97,98,108,101,50,34,58,123,34,105,100,34,58,50,50,44,34,116,101,120,116,34,58,34,110,101,119,103,101,116,34,44,34,116,97,98,108,101,49,105,100,34,58,51,50,54,53,51,50,51,56,56,48,48,57,54,53,44,34,105,115,68,101,108,101,116,101,34,58,48,125,44,34,116,97,98,108,101,49,34,58,123,34,73,100,34,58,51,50,54,53,51,50,51,56,56,48,48,57,54,53,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,71,69,84,34,125,44,34,116,97,98,108,101,52,34,58,123,34,105,100,34,58,52,50,44,34,116,101,120,116,34,58,34,52,50,34,125,125,93,44,34,112,97,103,101,34,58,49,44,34,99,111,117,110,116,34,58,51,44,34,109,97,120,34,58,50,44,34,116,111,116,97,108,34,58,52,125,44,34,101,120,116,114,97,115,34,58,110,117,108,108,44,34,116,105,109,101,34,58,34,50,48,50,52,45,48,51,45,49,52,32,49,49,58,53,51,58,50,55,34,125]},\"cookie\":[],\"responseTime\":590,\"responseSize\":779,\"type\":\"http\",\"tempFilePath\":\"\",\"timingPhases\":{\"prepare\":2.1902999877929688,\"wait\":1.2958000302314758,\"dns\":0.8892999887466431,\"tcp\":0.6733999848365784,\"firstByte\":584.8849999904633,\"download\":1.787600040435791,\"process\":0.033399999141693115,\"total\":591.7548000216484}},\"request\":{\"url\":{\"protocol\":\"http\",\"port\":\"5005\",\"path\":[\"api\",\"aPIJSON\",\"get\"],\"host\":[\"localhost\"],\"query\":[],\"variable\":[]},\"header\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"system\":true},{\"key\":\"Accept\",\"value\":\"*/*\",\"system\":true},{\"key\":\"Host\",\"value\":\"localhost:5005\",\"system\":true},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\",\"system\":true},{\"key\":\"Connection\",\"value\":\"keep-alive\",\"system\":true},{\"key\":\"Content-Length\",\"value\":316,\"system\":true}],\"method\":\"POST\",\"baseUrl\":\"http://localhost:5005\",\"body\":{\"mode\":\"raw\",\"raw\":\"{\\n  \\\"[]\\\": {\\n    \\\"table3\\\": {\\n      \\\"text\\\": \\\"update3\\\",\\n      \\\"@order\\\": \\\"id-\\\"\\n    },\\n    \\\"table2\\\": {\\n      \\\"id@\\\": \\\"/table3/table2id\\\"\\n    },\\n    \\\"table1\\\": {\\n      \\\"@column\\\": \\\"id,httpmethod\\\",\\n      \\\"id@\\\": \\\"/table2/table1id\\\"\\n    },\\n    \\\"table4\\\": {\\n      \\\"id@\\\": \\\"/table3/table4id\\\"\\n    },\\n    \\\"page\\\": 1,\\n    \\\"count\\\": 3\\n  }\\n}\",\"generateMode\":\"normal\",\"type\":\"application/json\"},\"auth\":{\"type\":\"bearer\",\"bearer\":[{\"type\":\"any\",\"value\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"key\":\"token\"}]},\"type\":\"http\"},\"history\":{\"execution\":{\"verbose\":false,\"sessions\":{},\"data\":[{\"request\":{\"method\":\"POST\",\"href\":\"http://localhost:5005/api/aPIJSON/get\",\"headers\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\"},{\"key\":\"Accept\",\"value\":\"*/*\"},{\"key\":\"Host\",\"value\":\"localhost:5005\"},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\"},{\"key\":\"Connection\",\"value\":\"keep-alive\"},{\"key\":\"Content-Length\",\"value\":\"316\"}],\"httpVersion\":\"1.1\"},\"response\":{\"statusCode\":200,\"headers\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Thu, 14 Mar 2024 03:53:26 GMT\"},{\"key\":\"Server\",\"value\":\"Kestrel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"Admin.NET\",\"value\":\"Admin.NET\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86399\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-15T03:53:27.0136472Z\"}],\"httpVersion\":\"1.1\"},\"timings\":{\"start\":1710388406828,\"requestStart\":1710388406830,\"offset\":{\"request\":2.1902999877929688,\"socket\":3.4861000180244446,\"lookup\":4.375400006771088,\"connect\":5.048799991607666,\"response\":589.9337999820709,\"end\":591.7214000225067,\"done\":591.7548000216484}}}]}},\"responseValidation\":{},\"passed\":true,\"metaInfo\":{\"httpApiId\":151219333,\"httpApiCaseId\":143871046,\"httpApiName\":\"统一查询入口\",\"httpApiPath\":\"/api/aPIJSON/get\",\"httpApiMethod\":\"post\",\"httpApiCaseName\":\"关联查询\"}}", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}, {"id": 143975340, "type": "http", "path": null, "name": "group by", "responseId": 0, "parameters": {"query": [], "path": [], "cookie": [], "header": []}, "commonParameters": {"query": [], "body": [], "header": [], "cookie": []}, "requestBody": {"parameters": [], "data": "{\r\n    \"[]\": {\r\n        \"table1\": {\r\n            \"@column\": \"actionname,httpmethod,max(id)\",\r\n            \"@group\": \"actionname,httpmethod\",\r\n        },\r\n    }\r\n}", "generateMode": "normal"}, "auth": {}, "advancedSettings": {"disabledSystemHeaders": {}, "isDefaultUrlEncoding": 2, "disableUrlEncoding": false}, "requestResult": "{\"id\":\"temp.00314312-a9fe-4ad2-859c-b7555f9a0d3e\",\"cursor\":{\"position\":0,\"iteration\":0,\"length\":1,\"cycles\":1,\"empty\":false,\"eof\":false,\"bof\":true,\"cr\":false,\"ref\":\"00314312-a9fe-4ad2-859c-b7555f9a0d3e\",\"requestIndex\":0,\"httpRequestId\":\"e6ba19a4-e4f9-4412-9b45-f48e81bb7277\"},\"type\":\"http\",\"response\":{\"id\":\"6134deb2-55f4-419b-9387-38ae669f137f\",\"status\":\"OK\",\"code\":200,\"header\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Fri, 08 Mar 2024 08:23:12 GMT\"},{\"key\":\"Server\",\"value\":\"<PERSON><PERSON><PERSON>\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86397\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-09T08:14:42.5201051Z\"}],\"trailer\":[],\"stream\":{\"type\":\"Buffer\",\"data\":[123,34,99,111,100,101,34,58,50,48,48,44,34,116,121,112,101,34,58,34,115,117,99,99,101,115,115,34,44,34,109,101,115,115,97,103,101,34,58,34,34,44,34,114,101,115,117,108,116,34,58,123,34,91,93,34,58,91,123,34,116,97,98,108,101,49,34,58,123,34,65,99,116,105,111,110,78,97,109,101,34,58,34,80,111,115,116,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,109,97,120,40,105,100,41,34,58,51,50,53,50,49,51,57,56,52,53,52,51,52,49,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,109,97,120,40,105,100,41,34,58,51,50,54,53,51,50,51,56,56,48,48,57,54,53,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,65,99,116,105,111,110,78,97,109,101,34,58,34,81,117,101,114,121,66,121,84,97,98,108,101,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,109,97,120,40,105,100,41,34,58,51,50,54,52,55,52,52,57,50,50,54,51,48,57,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,65,99,116,105,111,110,78,97,109,101,34,58,34,82,101,109,111,118,101,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,109,97,120,40,105,100,41,34,58,51,50,54,53,51,49,53,48,50,48,55,53,53,55,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,65,99,116,105,111,110,78,97,109,101,34,58,34,83,119,97,103,103,101,114,67,104,101,99,107,85,114,108,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,109,97,120,40,105,100,41,34,58,51,50,54,53,51,50,54,54,50,50,48,51,53,55,125,125,44,123,34,116,97,98,108,101,49,34,58,123,34,65,99,116,105,111,110,78,97,109,101,34,58,34,83,119,97,103,103,101,114,83,117,98,109,105,116,85,114,108,34,44,34,72,116,116,112,77,101,116,104,111,100,34,58,34,80,79,83,84,34,44,34,109,97,120,40,105,100,41,34,58,51,50,54,53,48,52,50,57,50,50,50,50,49,51,125,125,93,125,44,34,101,120,116,114,97,115,34,58,110,117,108,108,44,34,116,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,56,32,49,54,58,50,51,58,49,51,34,125]},\"cookie\":[],\"responseTime\":8399,\"responseSize\":605,\"type\":\"http\",\"tempFilePath\":\"\",\"timingPhases\":{\"prepare\":2.243699997663498,\"wait\":0.2954000011086464,\"dns\":0,\"tcp\":0,\"firstByte\":8396.081299997866,\"download\":1.9274000003933907,\"process\":0.052000001072883606,\"total\":8400.599799998105}},\"request\":{\"url\":{\"protocol\":\"http\",\"port\":\"5005\",\"path\":[\"api\",\"aPIJSON\",\"get\"],\"host\":[\"localhost\"],\"query\":[],\"variable\":[]},\"header\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"system\":true},{\"key\":\"Accept\",\"value\":\"*/*\",\"system\":true},{\"key\":\"Host\",\"value\":\"localhost:5005\",\"system\":true},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\",\"system\":true},{\"key\":\"Connection\",\"value\":\"keep-alive\",\"system\":true},{\"key\":\"Content-Length\",\"value\":162,\"system\":true}],\"method\":\"POST\",\"baseUrl\":\"http://localhost:5005\",\"body\":{\"mode\":\"raw\",\"raw\":\"{\\r\\n    \\\"[]\\\": {\\r\\n        \\\"table1\\\": {\\r\\n            \\\"@column\\\": \\\"actionname,httpmethod,max(id)\\\",\\r\\n            \\\"@group\\\": \\\"actionname,httpmethod\\\",\\r\\n        },\\r\\n    }\\r\\n}\",\"generateMode\":\"normal\",\"type\":\"application/json\"},\"auth\":{\"type\":\"bearer\",\"bearer\":[{\"type\":\"any\",\"value\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"key\":\"token\"}]},\"type\":\"http\"},\"history\":{\"execution\":{\"verbose\":false,\"sessions\":{},\"data\":[{\"request\":{\"method\":\"POST\",\"href\":\"http://localhost:5005/api/aPIJSON/get\",\"headers\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\"},{\"key\":\"Accept\",\"value\":\"*/*\"},{\"key\":\"Host\",\"value\":\"localhost:5005\"},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\"},{\"key\":\"Connection\",\"value\":\"keep-alive\"},{\"key\":\"Content-Length\",\"value\":\"162\"}],\"httpVersion\":\"1.1\"},\"response\":{\"statusCode\":200,\"headers\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Fri, 08 Mar 2024 08:23:12 GMT\"},{\"key\":\"Server\",\"value\":\"Kestrel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86397\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-09T08:14:42.5201051Z\"}],\"httpVersion\":\"1.1\"},\"timings\":{\"start\":1709886184664,\"requestStart\":1709886184667,\"offset\":{\"request\":2.243699997663498,\"socket\":2.5390999987721443,\"response\":8398.620399996638,\"end\":8400.547799997032,\"lookup\":2.5390999987721443,\"connect\":2.5390999987721443,\"done\":8400.599799998105}}}]}},\"responseValidation\":{},\"passed\":true,\"metaInfo\":{\"httpApiId\":151219333,\"httpApiCaseId\":143975340,\"httpApiName\":\"统一入口\",\"httpApiPath\":\"/api/aPIJSON/get\",\"httpApiMethod\":\"post\",\"httpApiCaseName\":\"group by\"}}", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.405647728"], "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "新增", "api": {"id": "152848473", "method": "post", "path": "/api/aPIJSON/post", "parameters": {"query": [], "path": [], "cookie": [], "header": []}, "auth": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "408753512", "name": "Success", "code": 200, "contentType": "json", "jsonSchema": {"$ref": "#/definitions/84275190"}}], "responseExamples": [], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"type": "object", "additionalProperties": {"$ref": "#/definitions/84275307"}, "x-apifox-orders": []}, "example": "{\r\n    \"table2\": \r\n        {\r\n            //\"id\": 236, //如果传id，就用前端id，如果没有，就后端生成\r\n            \"text\": \"newget\"\r\n        }\r\n\r\n}"}, "description": "", "tags": ["aPIJSON"], "status": "released", "serverId": "", "operationId": "api-aPIJSON-post-Post", "sourceUrl": "", "ordering": 12, "cases": [{"id": 144371019, "type": "http", "path": null, "name": "单条", "responseId": 0, "parameters": {"query": [], "header": [], "cookie": [], "path": []}, "commonParameters": {"query": [], "body": [], "header": [], "cookie": []}, "requestBody": {"parameters": [], "data": "{\r\n    \"table2\": \r\n        {\r\n            //\"id\": 236, //如果传id，就用前端id，如果没有，就后端生成\r\n            \"text\": \"newget\"\r\n        }\r\n\r\n}", "type": "application/json"}, "auth": {}, "advancedSettings": {"disabledSystemHeaders": {}, "isDefaultUrlEncoding": 2, "disableUrlEncoding": false}, "requestResult": "{\"id\":\"temp.04906a0f-2a35-491b-8335-13c224a6e496\",\"cursor\":{\"position\":0,\"iteration\":0,\"length\":1,\"cycles\":1,\"empty\":false,\"eof\":false,\"bof\":true,\"cr\":false,\"ref\":\"04906a0f-2a35-491b-8335-13c224a6e496\",\"requestIndex\":0,\"httpRequestId\":\"8b54192a-17b5-4a20-b7c0-115533d436f3\"},\"type\":\"http\",\"response\":{\"id\":\"000965cc-02df-4aae-8b23-18368ab61ecb\",\"status\":\"OK\",\"code\":200,\"header\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Mon, 11 Mar 2024 06:58:40 GMT\"},{\"key\":\"Server\",\"value\":\"<PERSON><PERSON>rel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86395\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-12T06:48:49.8348531Z\"}],\"trailer\":[],\"stream\":{\"type\":\"Buffer\",\"data\":[123,34,99,111,100,101,34,58,50,48,48,44,34,116,121,112,101,34,58,34,115,117,99,99,101,115,115,34,44,34,109,101,115,115,97,103,101,34,58,34,34,44,34,114,101,115,117,108,116,34,58,123,34,116,97,98,108,101,50,34,58,91,51,50,55,54,49,54,51,48,53,50,52,57,57,55,44,50,51,56,93,44,34,116,97,98,108,101,51,34,58,51,50,55,54,49,54,51,48,53,50,56,48,54,57,125,44,34,101,120,116,114,97,115,34,58,110,117,108,108,44,34,116,105,109,101,34,58,34,50,48,50,52,45,48,51,45,49,49,32,49,52,58,53,56,58,52,49,34,125]},\"cookie\":[],\"responseTime\":118,\"responseSize\":150,\"type\":\"http\",\"tempFilePath\":\"\",\"timingPhases\":{\"prepare\":2.154699981212616,\"wait\":0.394599974155426,\"dns\":0,\"tcp\":0,\"firstByte\":114.57359999418259,\"download\":2.3081000447273254,\"process\":0.08730000257492065,\"total\":119.51829999685287}},\"request\":{\"url\":{\"protocol\":\"http\",\"port\":\"5005\",\"path\":[\"api\",\"aPIJSON\",\"post\"],\"host\":[\"localhost\"],\"query\":[],\"variable\":[]},\"header\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"system\":true},{\"key\":\"Accept\",\"value\":\"*/*\",\"system\":true},{\"key\":\"Host\",\"value\":\"localhost:5005\",\"system\":true},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\",\"system\":true},{\"key\":\"Connection\",\"value\":\"keep-alive\",\"system\":true},{\"key\":\"Content-Length\",\"value\":171,\"system\":true}],\"method\":\"POST\",\"baseUrl\":\"http://localhost:5005\",\"body\":{\"mode\":\"raw\",\"raw\":\"{\\n  \\\"table2\\\": [\\n    {\\n      \\\"text\\\": \\\"newget\\\"\\n    },\\n    {\\n      \\\"id\\\": 238,\\n      \\\"text\\\": \\\"newget2\\\"\\n    }\\n  ],\\n  \\\"table3\\\": {\\n    \\\"table2id\\\": 238,\\n    \\\"text\\\": \\\"newget\\\"\\n  }\\n}\",\"type\":\"application/json\"},\"auth\":{\"type\":\"bearer\",\"bearer\":[{\"type\":\"any\",\"value\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"key\":\"token\"}]},\"type\":\"http\"},\"history\":{\"execution\":{\"verbose\":false,\"sessions\":{},\"data\":[{\"request\":{\"method\":\"POST\",\"href\":\"http://localhost:5005/api/aPIJSON/post\",\"headers\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\"},{\"key\":\"Accept\",\"value\":\"*/*\"},{\"key\":\"Host\",\"value\":\"localhost:5005\"},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\"},{\"key\":\"Connection\",\"value\":\"keep-alive\"},{\"key\":\"Content-Length\",\"value\":\"171\"}],\"httpVersion\":\"1.1\"},\"response\":{\"statusCode\":200,\"headers\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Mon, 11 Mar 2024 06:58:40 GMT\"},{\"key\":\"Server\",\"value\":\"Kestrel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86395\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-12T06:48:49.8348531Z\"}],\"httpVersion\":\"1.1\"},\"timings\":{\"start\":1710140321198,\"requestStart\":1710140321200,\"offset\":{\"request\":2.154699981212616,\"socket\":2.549299955368042,\"response\":117.12289994955063,\"end\":119.43099999427795,\"lookup\":2.549299955368042,\"connect\":2.549299955368042,\"done\":119.51829999685287}}}]}},\"responseValidation\":{},\"passed\":true,\"metaInfo\":{\"httpApiId\":152848473,\"httpApiName\":\"新增\",\"httpApiPath\":\"/api/aPIJSON/post\",\"httpApiMethod\":\"post\",\"httpApiCaseName\":\"成功\"}}", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}, {"id": 144370602, "type": "http", "path": null, "name": "多表批量", "responseId": 0, "parameters": {"query": [], "path": [], "cookie": [], "header": []}, "commonParameters": {"query": [], "body": [], "header": [], "cookie": []}, "requestBody": {"parameters": [], "data": "{\r\n    \"table2\": [\r\n        {\r\n            //\"id\": 236, //如果传id，就用前端id，如果没有，就后端生成\r\n            \"text\": \"newget\"\r\n        },\r\n        {\r\n            \"id\": 240,\r\n            \"text\": \"newget2\"\r\n        }\r\n    ],\r\n    \"table3\":{\r\n        \"table2id\":240,\r\n        \"text\":\"newget\"\r\n    }\r\n}", "generateMode": "normal"}, "auth": {}, "advancedSettings": {"disabledSystemHeaders": {}, "isDefaultUrlEncoding": 2, "disableUrlEncoding": false}, "requestResult": "{\"id\":\"temp.89a3b724-e244-47bd-8332-b18d13b87f39\",\"cursor\":{\"position\":0,\"iteration\":0,\"length\":1,\"cycles\":1,\"empty\":false,\"eof\":false,\"bof\":true,\"cr\":false,\"ref\":\"89a3b724-e244-47bd-8332-b18d13b87f39\",\"requestIndex\":0,\"httpRequestId\":\"c4ff5552-4d6b-4816-bac7-951f9acbd466\"},\"type\":\"http\",\"response\":{\"id\":\"a2e99ccb-e877-4151-9d9e-157c49c50e65\",\"status\":\"OK\",\"code\":200,\"header\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Mon, 11 Mar 2024 07:32:24 GMT\"},{\"key\":\"Server\",\"value\":\"<PERSON><PERSON><PERSON>\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86398\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-12T07:30:34.5282454Z\"}],\"trailer\":[],\"stream\":{\"type\":\"Buffer\",\"data\":[123,34,99,111,100,101,34,58,50,48,48,44,34,116,121,112,101,34,58,34,115,117,99,99,101,115,115,34,44,34,109,101,115,115,97,103,101,34,58,34,34,44,34,114,101,115,117,108,116,34,58,123,34,116,97,98,108,101,50,34,58,123,34,105,100,34,58,91,51,50,55,54,50,49,52,56,54,49,48,54,50,57,44,50,52,48,93,44,34,99,111,117,110,116,34,58,50,125,44,34,116,97,98,108,101,51,34,58,123,34,105,100,34,58,51,50,55,54,50,49,52,56,54,49,55,53,52,49,125,125,44,34,101,120,116,114,97,115,34,58,110,117,108,108,44,34,116,105,109,101,34,58,34,50,48,50,52,45,48,51,45,49,49,32,49,53,58,51,50,58,50,53,34,125]},\"cookie\":[],\"responseTime\":168,\"responseSize\":174,\"type\":\"http\",\"tempFilePath\":\"\",\"timingPhases\":{\"prepare\":1.51419997215271,\"wait\":0.2290000319480896,\"dns\":0,\"tcp\":0,\"firstByte\":165.6187999844551,\"download\":1.2360000014305115,\"process\":0.05760002136230469,\"total\":168.65560001134872}},\"request\":{\"url\":{\"protocol\":\"http\",\"port\":\"5005\",\"path\":[\"api\",\"aPIJSON\",\"post\"],\"host\":[\"localhost\"],\"query\":[],\"variable\":[]},\"header\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"system\":true},{\"key\":\"Accept\",\"value\":\"*/*\",\"system\":true},{\"key\":\"Host\",\"value\":\"localhost:5005\",\"system\":true},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\",\"system\":true},{\"key\":\"Connection\",\"value\":\"keep-alive\",\"system\":true},{\"key\":\"Content-Length\",\"value\":171,\"system\":true}],\"method\":\"POST\",\"baseUrl\":\"http://localhost:5005\",\"body\":{\"mode\":\"raw\",\"raw\":\"{\\n  \\\"table2\\\": [\\n    {\\n      \\\"text\\\": \\\"newget\\\"\\n    },\\n    {\\n      \\\"id\\\": 240,\\n      \\\"text\\\": \\\"newget2\\\"\\n    }\\n  ],\\n  \\\"table3\\\": {\\n    \\\"table2id\\\": 240,\\n    \\\"text\\\": \\\"newget\\\"\\n  }\\n}\",\"generateMode\":\"normal\",\"type\":\"application/json\"},\"auth\":{\"type\":\"bearer\",\"bearer\":[{\"type\":\"any\",\"value\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"key\":\"token\"}]},\"type\":\"http\"},\"history\":{\"execution\":{\"verbose\":false,\"sessions\":{},\"data\":[{\"request\":{\"method\":\"POST\",\"href\":\"http://localhost:5005/api/aPIJSON/post\",\"headers\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\"},{\"key\":\"Accept\",\"value\":\"*/*\"},{\"key\":\"Host\",\"value\":\"localhost:5005\"},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\"},{\"key\":\"Connection\",\"value\":\"keep-alive\"},{\"key\":\"Content-Length\",\"value\":\"171\"}],\"httpVersion\":\"1.1\"},\"response\":{\"statusCode\":200,\"headers\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Mon, 11 Mar 2024 07:32:24 GMT\"},{\"key\":\"Server\",\"value\":\"Kestrel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86398\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-12T07:30:34.5282454Z\"}],\"httpVersion\":\"1.1\"},\"timings\":{\"start\":1710142344933,\"requestStart\":1710142344934,\"offset\":{\"request\":1.51419997215271,\"socket\":1.7432000041007996,\"response\":167.3619999885559,\"end\":168.59799998998642,\"lookup\":1.7432000041007996,\"connect\":1.7432000041007996,\"done\":168.65560001134872}}}]}},\"responseValidation\":{},\"passed\":true,\"metaInfo\":{\"httpApiId\":152848473,\"httpApiCaseId\":144370602,\"httpApiName\":\"新增\",\"httpApiPath\":\"/api/aPIJSON/post\",\"httpApiMethod\":\"post\",\"httpApiCaseName\":\"多表批量\"}}", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.408753512"], "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "修改", "api": {"id": "152848474", "method": "post", "path": "/api/aPIJSON/put", "parameters": {"path": [], "query": [], "cookie": [], "header": []}, "auth": {}, "commonParameters": {}, "responses": [{"id": "408753513", "name": "Success", "code": 200, "contentType": "json", "jsonSchema": {"$ref": "#/definitions/84275190"}}], "responseExamples": [], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"type": "object", "additionalProperties": {"$ref": "#/definitions/84275307"}, "x-apifox-orders": []}, "example": ""}, "description": "", "tags": ["aPIJSON"], "status": "released", "serverId": "", "operationId": "api-aPIJSON-put-Post", "sourceUrl": "", "ordering": 18, "cases": [{"id": 144398604, "type": "http", "path": null, "name": "通过id单条更新", "responseId": 0, "parameters": {"query": [], "path": [], "cookie": [], "header": []}, "commonParameters": {"query": [], "body": [], "header": [], "cookie": []}, "requestBody": {"parameters": [], "data": "{\r\n    \"table3\": {\r\n        \"id\": 32762120351813,\r\n        \"text\": \"edit\",\r\n        \"table2id\":255\r\n    }\r\n}", "generateMode": "normal"}, "auth": {}, "advancedSettings": {"disabledSystemHeaders": {}, "isDefaultUrlEncoding": 2, "disableUrlEncoding": false}, "requestResult": "{\"id\":\"temp.3c2d26df-ff36-4408-84d1-ba86a03a15d9\",\"cursor\":{\"position\":0,\"iteration\":0,\"length\":1,\"cycles\":1,\"empty\":false,\"eof\":false,\"bof\":true,\"cr\":false,\"ref\":\"3c2d26df-ff36-4408-84d1-ba86a03a15d9\",\"requestIndex\":0,\"httpRequestId\":\"01efc573-4df9-4ee4-8f19-329b479d8f83\"},\"type\":\"http\",\"response\":{\"id\":\"c0132dd1-a39d-491a-a497-7a91230bfbc7\",\"status\":\"OK\",\"code\":200,\"header\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Mon, 11 Mar 2024 08:24:50 GMT\"},{\"key\":\"Server\",\"value\":\"Kestrel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86399\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-12T08:24:51.3365034Z\"}],\"trailer\":[],\"stream\":{\"type\":\"Buffer\",\"data\":[123,34,99,111,100,101,34,58,50,48,48,44,34,116,121,112,101,34,58,34,115,117,99,99,101,115,115,34,44,34,109,101,115,115,97,103,101,34,58,34,34,44,34,114,101,115,117,108,116,34,58,123,34,116,97,98,108,101,51,34,58,123,34,99,111,100,101,34,58,50,48,48,44,34,109,115,103,34,58,34,115,117,99,99,101,115,115,34,44,34,105,100,34,58,34,51,50,55,54,50,49,50,48,51,53,49,56,49,51,34,125,125,44,34,101,120,116,114,97,115,34,58,110,117,108,108,44,34,116,105,109,101,34,58,34,50,48,50,52,45,48,51,45,49,49,32,49,54,58,50,52,58,53,49,34,125]},\"cookie\":[],\"responseTime\":626,\"responseSize\":156,\"type\":\"http\",\"tempFilePath\":\"\",\"timingPhases\":{\"prepare\":2.3127999901771545,\"wait\":1.4280999898910522,\"dns\":0.35510003566741943,\"tcp\":0.7379999756813049,\"firstByte\":621.6582000255585,\"download\":1.3226999640464783,\"process\":0.029600024223327637,\"total\":627.8445000052452}},\"request\":{\"url\":{\"protocol\":\"http\",\"port\":\"5005\",\"path\":[\"api\",\"aPIJSON\",\"put\"],\"host\":[\"localhost\"],\"query\":[],\"variable\":[]},\"header\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"system\":true},{\"key\":\"Accept\",\"value\":\"*/*\",\"system\":true},{\"key\":\"Host\",\"value\":\"localhost:5005\",\"system\":true},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\",\"system\":true},{\"key\":\"Connection\",\"value\":\"keep-alive\",\"system\":true},{\"key\":\"Content-Length\",\"value\":108,\"system\":true}],\"method\":\"POST\",\"baseUrl\":\"http://localhost:5005\",\"body\":{\"mode\":\"raw\",\"raw\":\"{\\r\\n    \\\"table3\\\": {\\r\\n        \\\"id\\\": 32762120351813,\\r\\n        \\\"text\\\": \\\"edit\\\",\\r\\n        \\\"table2id\\\":255\\r\\n    }\\r\\n}\",\"generateMode\":\"normal\",\"type\":\"application/json\"},\"auth\":{\"type\":\"bearer\",\"bearer\":[{\"type\":\"any\",\"value\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"key\":\"token\"}]},\"type\":\"http\"},\"history\":{\"execution\":{\"verbose\":false,\"sessions\":{},\"data\":[{\"request\":{\"method\":\"POST\",\"href\":\"http://localhost:5005/api/aPIJSON/put\",\"headers\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\"},{\"key\":\"Accept\",\"value\":\"*/*\"},{\"key\":\"Host\",\"value\":\"localhost:5005\"},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\"},{\"key\":\"Connection\",\"value\":\"keep-alive\"},{\"key\":\"Content-Length\",\"value\":\"108\"}],\"httpVersion\":\"1.1\"},\"response\":{\"statusCode\":200,\"headers\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Mon, 11 Mar 2024 08:24:50 GMT\"},{\"key\":\"Server\",\"value\":\"Kestrel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86399\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-12T08:24:51.3365034Z\"}],\"httpVersion\":\"1.1\"},\"timings\":{\"start\":1710145491156,\"requestStart\":1710145491158,\"offset\":{\"request\":2.3127999901771545,\"socket\":3.740899980068207,\"lookup\":4.096000015735626,\"connect\":4.833999991416931,\"response\":626.4922000169754,\"end\":627.8148999810219,\"done\":627.8445000052452}}}]}},\"responseValidation\":{},\"passed\":true,\"metaInfo\":{\"httpApiId\":152848474,\"httpApiCaseId\":144398604,\"httpApiName\":\"修改\",\"httpApiPath\":\"/api/aPIJSON/put\",\"httpApiMethod\":\"post\",\"httpApiCaseName\":\"单条更新\"}}", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}, {"id": 144399593, "type": "http", "path": null, "name": "多表多id批量更新", "responseId": 0, "parameters": {"query": [], "path": [], "cookie": [], "header": []}, "commonParameters": {"query": [], "body": [], "header": [], "cookie": []}, "requestBody": {"parameters": [], "data": "{\r\n    \"table3\": [\r\n        {\r\n            \"id\": [31,32,33],\r\n            \"text\": \"update3\"\r\n        },\r\n        {\r\n            \"id\": 32762148617541,\r\n            \"text\": \"update2\"\r\n        }\r\n    ],\r\n    \"table2\": {\r\n        \"id\": 32761658134341,\r\n        \"text\": \"update\"\r\n    }\r\n}", "generateMode": "normal"}, "auth": {}, "advancedSettings": {"disabledSystemHeaders": {}, "isDefaultUrlEncoding": 2, "disableUrlEncoding": false}, "requestResult": "{\"id\":\"temp.a347f44c-82a5-437f-afef-bef75f5f55f4\",\"cursor\":{\"position\":0,\"iteration\":0,\"length\":1,\"cycles\":1,\"empty\":false,\"eof\":false,\"bof\":true,\"cr\":false,\"ref\":\"a347f44c-82a5-437f-afef-bef75f5f55f4\",\"requestIndex\":0,\"httpRequestId\":\"1ee88481-8f7b-480f-adf0-3a74acff8426\"},\"type\":\"http\",\"response\":{\"id\":\"6d9b1b3d-167c-4fe8-832e-f336630e6e64\",\"status\":\"OK\",\"code\":200,\"header\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Mon, 11 Mar 2024 09:38:05 GMT\"},{\"key\":\"Server\",\"value\":\"Ke<PERSON>rel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86397\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-12T09:36:21.4486506Z\"}],\"trailer\":[],\"stream\":{\"type\":\"Buffer\",\"data\":[123,34,99,111,100,101,34,58,50,48,48,44,34,116,121,112,101,34,58,34,115,117,99,99,101,115,115,34,44,34,109,101,115,115,97,103,101,34,58,34,34,44,34,114,101,115,117,108,116,34,58,123,34,116,97,98,108,101,51,34,58,123,34,99,111,117,110,116,34,58,52,125,44,34,116,97,98,108,101,50,34,58,123,34,99,111,117,110,116,34,58,49,125,125,44,34,101,120,116,114,97,115,34,58,110,117,108,108,44,34,116,105,109,101,34,58,34,50,48,50,52,45,48,51,45,49,49,32,49,55,58,51,56,58,48,53,34,125]},\"cookie\":[],\"responseTime\":135,\"responseSize\":138,\"type\":\"http\",\"tempFilePath\":\"\",\"timingPhases\":{\"prepare\":1.440500020980835,\"wait\":0.2959999442100525,\"dns\":0,\"tcp\":0,\"firstByte\":132.63910001516342,\"download\":1.2211000323295593,\"process\":0.04819995164871216,\"total\":135.64489996433258}},\"request\":{\"url\":{\"protocol\":\"http\",\"port\":\"5005\",\"path\":[\"api\",\"aPIJSON\",\"put\"],\"host\":[\"localhost\"],\"query\":[],\"variable\":[]},\"header\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"system\":true},{\"key\":\"Accept\",\"value\":\"*/*\",\"system\":true},{\"key\":\"Host\",\"value\":\"localhost:5005\",\"system\":true},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\",\"system\":true},{\"key\":\"Connection\",\"value\":\"keep-alive\",\"system\":true},{\"key\":\"Content-Length\",\"value\":283,\"system\":true}],\"method\":\"POST\",\"baseUrl\":\"http://localhost:5005\",\"body\":{\"mode\":\"raw\",\"raw\":\"{\\r\\n    \\\"table3\\\": [\\r\\n        {\\r\\n            \\\"id\\\": [31,32,33],\\r\\n            \\\"text\\\": \\\"update3\\\"\\r\\n        },\\r\\n        {\\r\\n            \\\"id\\\": 32762148617541,\\r\\n            \\\"text\\\": \\\"update2\\\"\\r\\n        }\\r\\n    ],\\r\\n    \\\"table2\\\": {\\r\\n        \\\"id\\\": 32761658134341,\\r\\n        \\\"text\\\": \\\"update\\\"\\r\\n    }\\r\\n}\",\"generateMode\":\"normal\",\"type\":\"application/json\"},\"auth\":{\"type\":\"bearer\",\"bearer\":[{\"type\":\"any\",\"value\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"key\":\"token\"}]},\"type\":\"http\"},\"history\":{\"execution\":{\"verbose\":false,\"sessions\":{},\"data\":[{\"request\":{\"method\":\"POST\",\"href\":\"http://localhost:5005/api/aPIJSON/put\",\"headers\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\"},{\"key\":\"Accept\",\"value\":\"*/*\"},{\"key\":\"Host\",\"value\":\"localhost:5005\"},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\"},{\"key\":\"Connection\",\"value\":\"keep-alive\"},{\"key\":\"Content-Length\",\"value\":\"283\"}],\"httpVersion\":\"1.1\"},\"response\":{\"statusCode\":200,\"headers\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Mon, 11 Mar 2024 09:38:05 GMT\"},{\"key\":\"Server\",\"value\":\"Kestrel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86397\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-12T09:36:21.4486506Z\"}],\"httpVersion\":\"1.1\"},\"timings\":{\"start\":1710149885351,\"requestStart\":1710149885352,\"offset\":{\"request\":1.440500020980835,\"socket\":1.7364999651908875,\"response\":134.3755999803543,\"end\":135.59670001268387,\"lookup\":1.7364999651908875,\"connect\":1.7364999651908875,\"done\":135.64489996433258}}}]}},\"responseValidation\":{},\"passed\":true,\"metaInfo\":{\"httpApiId\":152848474,\"httpApiCaseId\":144399593,\"httpApiName\":\"修改\",\"httpApiPath\":\"/api/aPIJSON/put\",\"httpApiMethod\":\"post\",\"httpApiCaseName\":\"多表更新\"}}", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "删除", "api": {"id": "152848475", "method": "post", "path": "/api/aPIJSON/delete", "parameters": {"query": [], "path": [], "cookie": [], "header": []}, "auth": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": []}, "responses": [{"id": "*********", "name": "Success", "code": 200, "contentType": "json", "jsonSchema": {"$ref": "#/definitions/84275190"}}], "responseExamples": [], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"type": "object", "additionalProperties": {"$ref": "#/definitions/84275307"}, "x-apifox-orders": []}, "example": "{\r\n    \"table1\": {\r\n        \"id\": [32520112744261,32520179315781,32520200693573],\r\n    }\r\n}"}, "description": "", "tags": ["aPIJSON"], "status": "released", "serverId": "", "operationId": "api-aPIJSON-delete-Post", "sourceUrl": "", "ordering": 24, "cases": [{"id": 143343716, "type": "http", "path": null, "name": "删除单条数据", "responseId": 0, "parameters": {"query": [], "header": [], "cookie": [], "path": []}, "commonParameters": {"query": [], "body": [], "header": [], "cookie": []}, "requestBody": {"parameters": [], "data": "{\r\n    \"table1\": {\r\n        \"id\": \"32520494044741\",\r\n    }\r\n}", "type": "application/json"}, "auth": {}, "advancedSettings": {"disabledSystemHeaders": {}, "isDefaultUrlEncoding": 2, "disableUrlEncoding": false}, "requestResult": "{\"id\":\"temp.26698a36-85d0-4cbb-b880-abbb4872dddf\",\"cursor\":{\"position\":0,\"iteration\":0,\"length\":1,\"cycles\":1,\"empty\":false,\"eof\":false,\"bof\":true,\"cr\":false,\"ref\":\"26698a36-85d0-4cbb-b880-abbb4872dddf\",\"requestIndex\":0,\"httpRequestId\":\"e91c2ef7-03a4-41fa-b114-138973559bec\"},\"type\":\"http\",\"response\":{\"id\":\"a6999ac7-bec5-4b65-a905-42dbe74794e7\",\"status\":\"OK\",\"code\":200,\"header\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Wed, 06 Mar 2024 09:16:09 GMT\"},{\"key\":\"Server\",\"value\":\"Ke<PERSON>rel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86398\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-07T09:15:13.4609976Z\"}],\"trailer\":[],\"stream\":{\"type\":\"Buffer\",\"data\":[123,34,99,111,100,101,34,58,50,48,48,44,34,116,121,112,101,34,58,34,115,117,99,99,101,115,115,34,44,34,109,101,115,115,97,103,101,34,58,34,34,44,34,114,101,115,117,108,116,34,58,123,34,116,97,98,108,101,49,34,58,123,34,99,111,100,101,34,58,50,48,48,44,34,109,115,103,34,58,34,115,117,99,99,101,115,115,34,44,34,105,100,34,58,34,51,50,53,50,48,52,57,52,48,52,52,55,52,49,34,125,125,44,34,101,120,116,114,97,115,34,58,110,117,108,108,44,34,116,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,54,32,49,55,58,49,54,58,48,57,34,125]},\"cookie\":[],\"responseTime\":111,\"responseSize\":156,\"type\":\"http\",\"tempFilePath\":\"\",\"timingPhases\":{\"prepare\":1.5057999789714813,\"wait\":0.2891000211238861,\"dns\":0,\"tcp\":0,\"firstByte\":109.1957999765873,\"download\":1.360700011253357,\"process\":0.034900009632110596,\"total\":112.38629999756813}},\"request\":{\"url\":{\"protocol\":\"http\",\"port\":\"5005\",\"path\":[\"api\",\"aPIJSON\",\"delete\"],\"host\":[\"localhost\"],\"query\":[],\"variable\":[]},\"header\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTE5NzE1MiwibmJmIjoxNzA5MTk3MTUyLCJleHAiOjE3MDk4MDE5NTIsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.96Y2rBegXqiYjuXcXeVyuXV75Cmzu8FpILylDcougt8\",\"system\":true},{\"key\":\"Accept\",\"value\":\"*/*\",\"system\":true},{\"key\":\"Host\",\"value\":\"localhost:5005\",\"system\":true},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\",\"system\":true},{\"key\":\"Connection\",\"value\":\"keep-alive\",\"system\":true},{\"key\":\"Content-Length\",\"value\":61,\"system\":true}],\"method\":\"POST\",\"baseUrl\":\"http://localhost:5005\",\"body\":{\"mode\":\"raw\",\"raw\":\"{\\r\\n    \\\"table1\\\": {\\r\\n        \\\"id\\\": \\\"32520494044741\\\",\\r\\n    }\\r\\n}\",\"type\":\"application/json\"},\"auth\":{\"type\":\"bearer\",\"bearer\":[{\"type\":\"any\",\"value\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTE5NzE1MiwibmJmIjoxNzA5MTk3MTUyLCJleHAiOjE3MDk4MDE5NTIsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.96Y2rBegXqiYjuXcXeVyuXV75Cmzu8FpILylDcougt8\",\"key\":\"token\"}]},\"type\":\"http\"},\"history\":{\"execution\":{\"verbose\":false,\"sessions\":{},\"data\":[{\"request\":{\"method\":\"POST\",\"href\":\"http://localhost:5005/api/aPIJSON/delete\",\"headers\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTE5NzE1MiwibmJmIjoxNzA5MTk3MTUyLCJleHAiOjE3MDk4MDE5NTIsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.96Y2rBegXqiYjuXcXeVyuXV75Cmzu8FpILylDcougt8\"},{\"key\":\"Accept\",\"value\":\"*/*\"},{\"key\":\"Host\",\"value\":\"localhost:5005\"},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\"},{\"key\":\"Connection\",\"value\":\"keep-alive\"},{\"key\":\"Content-Length\",\"value\":\"61\"}],\"httpVersion\":\"1.1\"},\"response\":{\"statusCode\":200,\"headers\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Wed, 06 Mar 2024 09:16:09 GMT\"},{\"key\":\"Server\",\"value\":\"Kestrel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86398\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-07T09:15:13.4609976Z\"}],\"httpVersion\":\"1.1\"},\"timings\":{\"start\":1709716569879,\"requestStart\":1709716569880,\"offset\":{\"request\":1.5057999789714813,\"socket\":1.7949000000953674,\"response\":110.99069997668266,\"end\":112.35139998793602,\"lookup\":1.7949000000953674,\"connect\":1.7949000000953674,\"done\":112.38629999756813}}}]}},\"responseValidation\":{},\"passed\":true,\"metaInfo\":{\"httpApiId\":152848475,\"httpApiName\":\"删除\",\"httpApiPath\":\"/api/aPIJSON/delete\",\"httpApiMethod\":\"post\",\"httpApiCaseName\":\"成功\"}}", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}, {"id": 143368262, "type": "http", "path": null, "name": "批量删除id", "responseId": 0, "parameters": {"query": [], "path": [], "cookie": [], "header": []}, "commonParameters": {"query": [], "body": [], "header": [], "cookie": []}, "requestBody": {"parameters": [], "data": "{\r\n    \"table3\": {\r\n        \"id\": [\r\n            32761630528069,\r\n            239\r\n        ]\r\n    }\r\n}", "generateMode": "normal"}, "auth": {}, "advancedSettings": {"disabledSystemHeaders": {}, "isDefaultUrlEncoding": 2, "disableUrlEncoding": false}, "requestResult": "{\"id\":\"temp.c39182fc-fc2f-4357-b3b7-32e41ef1fb57\",\"cursor\":{\"position\":0,\"iteration\":0,\"length\":1,\"cycles\":1,\"empty\":false,\"eof\":false,\"bof\":true,\"cr\":false,\"ref\":\"c39182fc-fc2f-4357-b3b7-32e41ef1fb57\",\"requestIndex\":0,\"httpRequestId\":\"7443b4a1-f762-4a24-8dd2-ad33ce1c8da7\"},\"type\":\"http\",\"response\":{\"id\":\"43956c21-adae-41c7-a722-c856c7c7c0aa\",\"status\":\"OK\",\"code\":200,\"header\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Mon, 11 Mar 2024 07:34:26 GMT\"},{\"key\":\"Server\",\"value\":\"Kestrel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86396\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-12T07:26:16.5966009Z\"}],\"trailer\":[],\"stream\":{\"type\":\"Buffer\",\"data\":[123,34,99,111,100,101,34,58,50,48,48,44,34,116,121,112,101,34,58,34,115,117,99,99,101,115,115,34,44,34,109,101,115,115,97,103,101,34,58,34,34,44,34,114,101,115,117,108,116,34,58,123,34,116,97,98,108,101,51,34,58,123,34,105,100,34,58,91,51,50,55,54,49,54,51,48,53,50,56,48,54,57,44,50,51,57,93,44,34,99,111,117,110,116,34,58,49,125,125,44,34,101,120,116,114,97,115,34,58,110,117,108,108,44,34,116,105,109,101,34,58,34,50,48,50,52,45,48,51,45,49,49,32,49,53,58,51,52,58,50,55,34,125]},\"cookie\":[],\"responseTime\":158,\"responseSize\":143,\"type\":\"http\",\"tempFilePath\":\"\",\"timingPhases\":{\"prepare\":2.1498000025749207,\"wait\":0.36399996280670166,\"dns\":0,\"tcp\":0,\"firstByte\":156.0023000240326,\"download\":1.451200008392334,\"process\":0.04890000820159912,\"total\":160.01620000600815}},\"request\":{\"url\":{\"protocol\":\"http\",\"port\":\"5005\",\"path\":[\"api\",\"aPIJSON\",\"delete\"],\"host\":[\"localhost\"],\"query\":[],\"variable\":[]},\"header\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"system\":true},{\"key\":\"Accept\",\"value\":\"*/*\",\"system\":true},{\"key\":\"Host\",\"value\":\"localhost:5005\",\"system\":true},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\",\"system\":true},{\"key\":\"Connection\",\"value\":\"keep-alive\",\"system\":true},{\"key\":\"Content-Length\",\"value\":55,\"system\":true}],\"method\":\"POST\",\"baseUrl\":\"http://localhost:5005\",\"body\":{\"mode\":\"raw\",\"raw\":\"{\\r\\n    \\\"table3\\\": {\\r\\n\\\"id\\\":[32761630528069,239]\\r\\n    }\\r\\n}\",\"generateMode\":\"normal\",\"type\":\"application/json\"},\"auth\":{\"type\":\"bearer\",\"bearer\":[{\"type\":\"any\",\"value\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"key\":\"token\"}]},\"type\":\"http\"},\"history\":{\"execution\":{\"verbose\":false,\"sessions\":{},\"data\":[{\"request\":{\"method\":\"POST\",\"href\":\"http://localhost:5005/api/aPIJSON/delete\",\"headers\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\"},{\"key\":\"Accept\",\"value\":\"*/*\"},{\"key\":\"Host\",\"value\":\"localhost:5005\"},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\"},{\"key\":\"Connection\",\"value\":\"keep-alive\"},{\"key\":\"Content-Length\",\"value\":\"55\"}],\"httpVersion\":\"1.1\"},\"response\":{\"statusCode\":200,\"headers\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Mon, 11 Mar 2024 07:34:26 GMT\"},{\"key\":\"Server\",\"value\":\"Kestrel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86396\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-12T07:26:16.5966009Z\"}],\"httpVersion\":\"1.1\"},\"timings\":{\"start\":1710142466906,\"requestStart\":1710142466908,\"offset\":{\"request\":2.1498000025749207,\"socket\":2.5137999653816223,\"response\":158.51609998941422,\"end\":159.96729999780655,\"lookup\":2.5137999653816223,\"connect\":2.5137999653816223,\"done\":160.01620000600815}}}]}},\"responseValidation\":{},\"passed\":true,\"metaInfo\":{\"httpApiId\":152848475,\"httpApiCaseId\":143368262,\"httpApiName\":\"删除\",\"httpApiPath\":\"/api/aPIJSON/delete\",\"httpApiMethod\":\"post\",\"httpApiCaseName\":\"批量删除id\"}}", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}, {"id": 143543753, "type": "http", "path": null, "name": "批量删除（匹配条件）", "responseId": 0, "parameters": {"query": [], "path": [], "cookie": [], "header": []}, "commonParameters": {"query": [], "body": [], "header": [], "cookie": []}, "requestBody": {"parameters": [], "data": "{\r\n    \"table1\": {\r\n        \"httpmethod\": [\"GET\",\"POST\"],\r\n        \"ThreadId\": [37,39],\"Actionname\":\"SwaggerCheckUrl\",\"loglevel\":2.5,\"isdelete\":false\r\n    }\r\n}", "generateMode": "normal"}, "auth": {}, "advancedSettings": {"disabledSystemHeaders": {}, "isDefaultUrlEncoding": 2, "disableUrlEncoding": false}, "requestResult": "{\"id\":\"temp.44a9ebb3-1e3d-4049-936a-a412fb468f00\",\"cursor\":{\"position\":0,\"iteration\":0,\"length\":1,\"cycles\":1,\"empty\":false,\"eof\":false,\"bof\":true,\"cr\":false,\"ref\":\"44a9ebb3-1e3d-4049-936a-a412fb468f00\",\"requestIndex\":0,\"httpRequestId\":\"2365b41d-b930-4f24-85d5-12b1198e2c4d\"},\"type\":\"http\",\"response\":{\"id\":\"097a32a0-1cbe-410f-979f-e1170b268276\",\"status\":\"OK\",\"code\":200,\"header\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Thu, 07 Mar 2024 10:10:46 GMT\"},{\"key\":\"Server\",\"value\":\"<PERSON><PERSON><PERSON>\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86399\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-08T10:10:47.0961001Z\"}],\"trailer\":[],\"stream\":{\"type\":\"Buffer\",\"data\":[123,34,99,111,100,101,34,58,50,48,48,44,34,116,121,112,101,34,58,34,115,117,99,99,101,115,115,34,44,34,109,101,115,115,97,103,101,34,58,34,34,44,34,114,101,115,117,108,116,34,58,123,34,116,97,98,108,101,49,34,58,123,34,104,116,116,112,109,101,116,104,111,100,34,58,91,34,71,69,84,34,44,34,80,79,83,84,34,93,44,34,84,104,114,101,97,100,73,100,34,58,91,51,55,44,51,57,93,44,34,65,99,116,105,111,110,110,97,109,101,34,58,34,83,119,97,103,103,101,114,67,104,101,99,107,85,114,108,34,44,34,108,111,103,108,101,118,101,108,34,58,50,46,53,44,34,105,115,100,101,108,101,116,101,34,58,102,97,108,115,101,44,34,99,111,117,110,116,34,58,48,125,125,44,34,101,120,116,114,97,115,34,58,110,117,108,108,44,34,116,105,109,101,34,58,34,50,48,50,52,45,48,51,45,48,55,32,49,56,58,49,48,58,52,55,34,125]},\"cookie\":[],\"responseTime\":602,\"responseSize\":227,\"type\":\"http\",\"tempFilePath\":\"\"},\"request\":{\"url\":{\"protocol\":\"http\",\"port\":\"5005\",\"path\":[\"api\",\"aPIJSON\",\"delete\"],\"host\":[\"localhost\"],\"query\":[],\"variable\":[]},\"header\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"system\":true},{\"key\":\"Accept\",\"value\":\"*/*\",\"system\":true},{\"key\":\"Host\",\"value\":\"localhost:5005\",\"system\":true},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\",\"system\":true},{\"key\":\"Connection\",\"value\":\"keep-alive\",\"system\":true},{\"key\":\"Content-Length\",\"value\":159,\"system\":true}],\"method\":\"POST\",\"baseUrl\":\"http://localhost:5005\",\"body\":{\"mode\":\"raw\",\"raw\":\"{\\r\\n    \\\"table1\\\": {\\r\\n        \\\"httpmethod\\\": [\\\"GET\\\",\\\"POST\\\"],\\r\\n        \\\"ThreadId\\\": [37,39],\\\"Actionname\\\":\\\"SwaggerCheckUrl\\\",\\\"loglevel\\\":2.5,\\\"isdelete\\\":false\\r\\n    }\\r\\n}\",\"generateMode\":\"normal\",\"type\":\"application/json\"},\"auth\":{\"type\":\"bearer\",\"bearer\":[{\"type\":\"any\",\"value\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\",\"key\":\"token\"}]},\"type\":\"http\"},\"history\":{\"execution\":{\"verbose\":false,\"sessions\":{},\"data\":[{\"request\":{\"method\":\"POST\",\"href\":\"http://localhost:5005/api/aPIJSON/delete\",\"headers\":[{\"key\":\"User-Agent\",\"value\":\"Apifox/1.0.0 (https://apifox.com)\"},{\"key\":\"Content-Type\",\"value\":\"application/json\"},{\"key\":\"Authorization\",\"value\":\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec\"},{\"key\":\"Accept\",\"value\":\"*/*\"},{\"key\":\"Host\",\"value\":\"localhost:5005\"},{\"key\":\"Accept-Encoding\",\"value\":\"gzip, deflate, br\"},{\"key\":\"Connection\",\"value\":\"keep-alive\"},{\"key\":\"Content-Length\",\"value\":\"159\"}],\"httpVersion\":\"1.1\"},\"response\":{\"statusCode\":200,\"headers\":[{\"key\":\"Content-Type\",\"value\":\"application/json; charset=utf-8\"},{\"key\":\"Date\",\"value\":\"Thu, 07 Mar 2024 10:10:46 GMT\"},{\"key\":\"Server\",\"value\":\"Kestrel\"},{\"key\":\"Content-Language\",\"value\":\"zh-CN\"},{\"key\":\"Transfer-Encoding\",\"value\":\"chunked\"},{\"key\":\"environment\",\"value\":\"Development\"},{\"key\":\"Furion\",\"value\":\"********\"},{\"key\":\"X-Rate-Limit-Limit\",\"value\":\"1d\"},{\"key\":\"X-Rate-Limit-Remaining\",\"value\":\"86399\"},{\"key\":\"X-Rate-Limit-Reset\",\"value\":\"2024-03-08T10:10:47.0961001Z\"}],\"httpVersion\":\"1.1\"},\"timings\":{\"start\":1709806246909,\"requestStart\":1709806246912,\"offset\":{\"request\":2.155799984931946,\"socket\":3.6095999479293823,\"lookup\":3.9983999729156494,\"connect\":4.8549999594688416,\"response\":602.0516999959946,\"end\":603.2913999557495,\"done\":603.3172999620438}}}]}},\"responseValidation\":{},\"passed\":true,\"metaInfo\":{\"httpApiId\":152848475,\"httpApiCaseId\":143543753,\"httpApiName\":\"删除\",\"httpApiPath\":\"/api/aPIJSON/delete\",\"httpApiMethod\":\"post\",\"httpApiCaseName\":\"批量删除2\"}}", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.*********"], "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}]}]}], "socketCollection": [], "docCollection": [], "schemaCollection": [{"name": "根目录", "items": [{"name": "<PERSON><PERSON><PERSON>", "items": [{"name": "AccountTypeEnum", "displayName": "", "id": "#/definitions/********", "description": "账号类型枚举<br />&nbsp;会员 Member = 666<br />&nbsp;普通账号 NormalUser = 777<br />&nbsp;系统管理员 SysAdmin = 888<br />&nbsp;超级管理员 SuperAdmin = 999<br />", "schema": {"jsonSchema": {"enum": [666, 777, 888, 999], "type": "integer", "description": "账号类型枚举<br />&nbsp;会员 Member = 666<br />&nbsp;普通账号 NormalUser = 777<br />&nbsp;系统管理员 SysAdmin = 888<br />&nbsp;超级管理员 SuperAdmin = 999<br />", "format": "int32"}}}, {"name": "AddCodeGenInput", "displayName": "", "id": "#/definitions/********", "description": "", "schema": {"jsonSchema": {"required": ["<PERSON><PERSON><PERSON>", "busName", "generateType", "menuPid", "nameSpace", "tableName"], "type": "object", "properties": {"page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": ["string", "null"], "description": "排序字段"}, "order": {"type": ["string", "null"], "description": "排序方向"}, "descStr": {"type": ["string", "null"], "description": "降序排序"}, "className": {"type": ["string", "null"], "description": "类名"}, "tablePrefix": {"type": ["string", "null"], "description": "是否移除表前缀"}, "configId": {"type": ["string", "null"], "description": "库定位器名"}, "dbName": {"type": ["string", "null"], "description": "数据库名(保留字段)"}, "dbType": {"type": ["string", "null"], "description": "数据库类型"}, "connectionString": {"type": ["string", "null"], "description": "数据库链接"}, "tableComment": {"type": ["string", "null"], "description": "功能名（数据库表名称）"}, "menuApplication": {"type": ["string", "null"], "description": "菜单应用分类（应用编码）"}, "printType": {"type": ["string", "null"], "description": "支持打印类型"}, "printName": {"type": ["string", "null"], "description": "打印模版名称"}, "tableName": {"minLength": 1, "type": "string", "description": "数据库表名"}, "busName": {"minLength": 1, "type": "string", "description": "业务名（业务代码包名称）"}, "nameSpace": {"minLength": 1, "type": "string", "description": "命名空间"}, "authorName": {"minLength": 1, "type": "string", "description": "作者姓名"}, "generateType": {"minLength": 1, "type": "string", "description": "生成方式"}, "menuPid": {"type": "integer", "description": "菜单父级", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["page", "pageSize", "field", "order", "descStr", "className", "tablePrefix", "configId", "dbN<PERSON>", "dbType", "connectionString", "tableComment", "menuApplication", "printType", "printName", "tableName", "busName", "nameSpace", "<PERSON><PERSON><PERSON>", "generateType", "menuPid"]}}}, {"name": "AddConfigInput", "displayName": "", "id": "#/definitions/84275169", "description": "", "schema": {"jsonSchema": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "name": {"maxLength": 64, "minLength": 1, "type": "string", "description": "名称"}, "code": {"maxLength": 64, "type": ["string", "null"], "description": "编码"}, "value": {"maxLength": 64, "type": ["string", "null"], "description": "属性值"}, "sysFlag": {"$ref": "#/definitions/84275444"}, "groupCode": {"maxLength": 64, "type": ["string", "null"], "description": "分组编码"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "remark": {"maxLength": 256, "type": ["string", "null"], "description": "备注"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "name", "code", "value", "sysFlag", "groupCode", "orderNo", "remark"]}}}, {"name": "AddDictDataInput", "displayName": "", "id": "#/definitions/84275170", "description": "", "schema": {"jsonSchema": {"required": ["code", "value"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "dictTypeId": {"type": "integer", "description": "字典类型Id", "format": "int64"}, "value": {"maxLength": 128, "minLength": 1, "type": "string", "description": "值"}, "code": {"maxLength": 64, "minLength": 1, "type": "string", "description": "编码"}, "tagType": {"maxLength": 16, "type": ["string", "null"], "description": "显示样式-标签颜色"}, "styleSetting": {"maxLength": 512, "type": ["string", "null"], "description": "显示样式-Style(控制显示样式)"}, "classSetting": {"maxLength": 512, "type": ["string", "null"], "description": "显示样式-Class(控制显示样式)"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "remark": {"maxLength": 2048, "type": ["string", "null"], "description": "备注"}, "extData": {"type": ["string", "null"], "description": "拓展数据(保存业务功能的配置项)"}, "status": {"$ref": "#/definitions/********"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "dictTypeId", "value", "code", "tagType", "styleSetting", "classSetting", "orderNo", "remark", "extData", "status"]}}}, {"name": "AddDictTypeInput", "displayName": "", "id": "#/definitions/84275171", "description": "", "schema": {"jsonSchema": {"required": ["code", "name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "name": {"maxLength": 64, "minLength": 1, "type": "string", "description": "名称"}, "code": {"maxLength": 64, "minLength": 1, "type": "string", "description": "编码"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "remark": {"maxLength": 256, "type": ["string", "null"], "description": "备注"}, "status": {"$ref": "#/definitions/********"}, "children": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275379"}, "description": "字典值集合"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "name", "code", "orderNo", "remark", "status", "children"]}}}, {"name": "AddJobDetailInput", "displayName": "", "id": "#/definitions/84275172", "description": "", "schema": {"jsonSchema": {"required": ["jobId"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "groupName": {"maxLength": 128, "type": ["string", "null"], "description": "组名称"}, "jobType": {"maxLength": 128, "type": ["string", "null"], "description": "作业类型FullName"}, "assemblyName": {"maxLength": 128, "type": ["string", "null"], "description": "程序集Name"}, "description": {"maxLength": 128, "type": ["string", "null"], "description": "描述信息"}, "concurrent": {"type": "boolean", "description": "是否并行执行"}, "includeAnnotations": {"type": "boolean", "description": "是否扫描特性触发器"}, "properties": {"type": ["string", "null"], "description": "额外数据"}, "updatedTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createType": {"$ref": "#/definitions/84275308"}, "scriptCode": {"type": ["string", "null"], "description": "脚本代码"}, "jobId": {"minLength": 2, "type": "string", "description": "作业Id"}}, "additionalProperties": false, "x-apifox-orders": ["id", "groupName", "jobType", "assemblyName", "description", "concurrent", "includeAnnotations", "properties", "updatedTime", "createType", "scriptCode", "jobId"]}}}, {"name": "AddJobTriggerInput", "displayName": "", "id": "#/definitions/84275173", "description": "", "schema": {"jsonSchema": {"required": ["jobId", "triggerId"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "triggerType": {"maxLength": 128, "type": ["string", "null"], "description": "触发器类型FullName"}, "assemblyName": {"maxLength": 128, "type": ["string", "null"], "description": "程序集Name"}, "args": {"maxLength": 128, "type": ["string", "null"], "description": "参数"}, "description": {"maxLength": 128, "type": ["string", "null"], "description": "描述信息"}, "status": {"$ref": "#/definitions/********"}, "startTime": {"type": ["string", "null"], "description": "起始时间", "format": "date-time"}, "endTime": {"type": ["string", "null"], "description": "结束时间", "format": "date-time"}, "lastRunTime": {"type": ["string", "null"], "description": "最近运行时间", "format": "date-time"}, "nextRunTime": {"type": ["string", "null"], "description": "下一次运行时间", "format": "date-time"}, "numberOfRuns": {"type": "integer", "description": "触发次数", "format": "int64"}, "maxNumberOfRuns": {"type": "integer", "description": "最大触发次数（0:不限制，n:N次）", "format": "int64"}, "numberOfErrors": {"type": "integer", "description": "出错次数", "format": "int64"}, "maxNumberOfErrors": {"type": "integer", "description": "最大出错次数（0:不限制，n:N次）", "format": "int64"}, "numRetries": {"type": "integer", "description": "重试次数", "format": "int32"}, "retryTimeout": {"type": "integer", "description": "重试间隔时间（ms）", "format": "int32"}, "startNow": {"type": "boolean", "description": "是否立即启动"}, "runOnStart": {"type": "boolean", "description": "是否启动时执行一次"}, "resetOnlyOnce": {"type": "boolean", "description": "是否在启动时重置最大触发次数等于一次的作业"}, "updatedTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "jobId": {"minLength": 2, "type": "string", "description": "作业Id"}, "triggerId": {"minLength": 2, "type": "string", "description": "触发器Id"}}, "additionalProperties": false, "x-apifox-orders": ["id", "triggerType", "assemblyName", "args", "description", "status", "startTime", "endTime", "lastRunTime", "nextRunTime", "numberOfRuns", "maxNumberOfRuns", "numberOfErrors", "maxNumberOfErrors", "numRetries", "retryTimeout", "startNow", "runOnStart", "resetOnlyOnce", "updatedTime", "jobId", "triggerId"]}}}, {"name": "AddMenuInput", "displayName": "", "id": "#/definitions/84275174", "description": "", "schema": {"jsonSchema": {"required": ["title"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "pid": {"type": "integer", "description": "父Id", "format": "int64"}, "type": {"$ref": "#/definitions/********"}, "name": {"maxLength": 64, "type": ["string", "null"], "description": "路由名称"}, "path": {"maxLength": 128, "type": ["string", "null"], "description": "路由地址"}, "component": {"maxLength": 128, "type": ["string", "null"], "description": "组件路径"}, "redirect": {"maxLength": 128, "type": ["string", "null"], "description": "重定向"}, "permission": {"maxLength": 128, "type": ["string", "null"], "description": "权限标识"}, "icon": {"maxLength": 128, "type": ["string", "null"], "description": "图标"}, "isIframe": {"type": "boolean", "description": "是否内嵌"}, "outLink": {"maxLength": 256, "type": ["string", "null"], "description": "外链链接"}, "isHide": {"type": "boolean", "description": "是否隐藏"}, "isKeepAlive": {"type": "boolean", "description": "是否缓存"}, "isAffix": {"type": "boolean", "description": "是否固定"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "status": {"$ref": "#/definitions/********"}, "remark": {"maxLength": 256, "type": ["string", "null"], "description": "备注"}, "children": {"type": ["array", "null"], "items": {"$ref": "#/definitions/********"}, "description": "菜单子项"}, "title": {"minLength": 1, "type": "string", "description": "名称"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "pid", "type", "name", "path", "component", "redirect", "permission", "icon", "isIframe", "outLink", "isHide", "isKeepAlive", "isAffix", "orderNo", "status", "remark", "children", "title"]}}}, {"name": "AddNoticeInput", "displayName": "", "id": "#/definitions/84275175", "description": "", "schema": {"jsonSchema": {"required": ["content", "title"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "title": {"maxLength": 32, "minLength": 1, "type": "string", "description": "标题"}, "content": {"minLength": 1, "type": "string", "description": "内容"}, "type": {"$ref": "#/definitions/84275325"}, "publicUserId": {"type": "integer", "description": "发布人Id", "format": "int64"}, "publicUserName": {"maxLength": 32, "type": ["string", "null"], "description": "发布人姓名"}, "publicOrgId": {"type": "integer", "description": "发布机构Id", "format": "int64"}, "publicOrgName": {"maxLength": 64, "type": ["string", "null"], "description": "发布机构名称"}, "publicTime": {"type": ["string", "null"], "description": "发布时间", "format": "date-time"}, "cancelTime": {"type": ["string", "null"], "description": "撤回时间", "format": "date-time"}, "status": {"$ref": "#/definitions/84275324"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "title", "content", "type", "publicUserId", "publicUserName", "publicOrgId", "publicOrgName", "publicTime", "cancelTime", "status"]}}}, {"name": "AddOpenAccessInput", "displayName": "", "id": "#/definitions/84275176", "description": "", "schema": {"jsonSchema": {"required": ["accessKey", "accessSecret", "bindUserId"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "bindTenantId": {"type": "integer", "description": "绑定租户Id", "format": "int64"}, "accessKey": {"minLength": 1, "type": "string", "description": "身份标识"}, "accessSecret": {"minLength": 1, "type": "string", "description": "密钥"}, "bindUserId": {"type": "integer", "description": "绑定用户Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "bindTenantId", "accessKey", "accessSecret", "bindUserId"]}}}, {"name": "AddOrgInput", "displayName": "", "id": "#/definitions/84275177", "description": "", "schema": {"jsonSchema": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "pid": {"type": "integer", "description": "父Id", "format": "int64"}, "code": {"maxLength": 64, "type": ["string", "null"], "description": "编码"}, "level": {"type": ["integer", "null"], "description": "级别", "format": "int32"}, "type": {"maxLength": 64, "type": ["string", "null"], "description": "机构类型-数据字典"}, "directorId": {"type": ["integer", "null"], "description": "负责人Id", "format": "int64"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "status": {"$ref": "#/definitions/********"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}, "children": {"type": ["array", "null"], "items": {"$ref": "#/definitions/********"}, "description": "机构子项"}, "disabled": {"type": "boolean", "description": "是否禁止选中"}, "name": {"minLength": 1, "type": "string", "description": "名称"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "pid", "code", "level", "type", "directorId", "orderNo", "status", "remark", "children", "disabled", "name"]}}}, {"name": "AddPluginInput", "displayName": "", "id": "#/definitions/84275178", "description": "", "schema": {"jsonSchema": {"required": ["csharpCode", "name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "csharpCode": {"minLength": 1, "type": "string", "description": "C#代码"}, "assemblyName": {"maxLength": 512, "type": ["string", "null"], "description": "程序集名称"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "status": {"$ref": "#/definitions/********"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}, "name": {"minLength": 1, "type": "string", "description": "名称"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "csharpCode", "assemblyName", "orderNo", "status", "remark", "name"]}}}, {"name": "AddPosInput", "displayName": "", "id": "#/definitions/84275179", "description": "", "schema": {"jsonSchema": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "code": {"maxLength": 64, "type": ["string", "null"], "description": "编码"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}, "status": {"$ref": "#/definitions/********"}, "name": {"minLength": 1, "type": "string", "description": "名称"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "code", "orderNo", "remark", "status", "name"]}}}, {"name": "AddPrintInput", "displayName": "", "id": "#/definitions/84275180", "description": "", "schema": {"jsonSchema": {"required": ["name", "template"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "template": {"minLength": 1, "type": "string", "description": "打印模板"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "status": {"$ref": "#/definitions/********"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}, "name": {"minLength": 1, "type": "string", "description": "名称"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "template", "orderNo", "status", "remark", "name"]}}}, {"name": "AddRegionInput", "displayName": "", "id": "#/definitions/84275181", "description": "", "schema": {"jsonSchema": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "pid": {"type": "integer", "description": "父Id", "format": "int64"}, "shortName": {"maxLength": 32, "type": ["string", "null"], "description": "简称"}, "mergerName": {"maxLength": 64, "type": ["string", "null"], "description": "组合名"}, "code": {"maxLength": 32, "type": ["string", "null"], "description": "行政代码"}, "zipCode": {"maxLength": 6, "type": ["string", "null"], "description": "邮政编码"}, "cityCode": {"maxLength": 6, "type": ["string", "null"], "description": "区号"}, "level": {"type": "integer", "description": "层级", "format": "int32"}, "pinYin": {"maxLength": 128, "type": ["string", "null"], "description": "拼音"}, "lng": {"type": "number", "description": "经度", "format": "float"}, "lat": {"type": "number", "description": "维度", "format": "float"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}, "children": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275399"}, "description": "机构子项"}, "name": {"minLength": 1, "type": "string", "description": "名称"}}, "additionalProperties": false, "x-apifox-orders": ["id", "pid", "shortName", "mergerName", "code", "zipCode", "cityCode", "level", "pinYin", "lng", "lat", "orderNo", "remark", "children", "name"]}}}, {"name": "AddRoleInput", "displayName": "", "id": "#/definitions/84275182", "description": "", "schema": {"jsonSchema": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "code": {"maxLength": 64, "type": ["string", "null"], "description": "编码"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "dataScope": {"$ref": "#/definitions/84275269"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}, "status": {"$ref": "#/definitions/********"}, "name": {"minLength": 1, "type": "string", "description": "名称"}, "menuIdList": {"type": ["array", "null"], "items": {"type": "integer", "format": "int64"}, "description": "菜单Id集合"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "code", "orderNo", "dataScope", "remark", "status", "name", "menuIdList"]}}}, {"name": "AddSubscribeMessageTemplateInput", "displayName": "", "id": "#/definitions/84275183", "description": "增加订阅消息模板", "schema": {"jsonSchema": {"required": ["keyworkIdList", "sceneDescription", "templateTitleId"], "type": "object", "properties": {"templateTitleId": {"minLength": 1, "type": "string", "description": "模板标题Id"}, "keyworkIdList": {"type": "array", "items": {"type": "integer", "format": "int32"}, "description": "模板关键词列表,例如 [3,5,4]"}, "sceneDescription": {"minLength": 1, "type": "string", "description": "服务场景描述，15个字以内"}}, "additionalProperties": false, "description": "增加订阅消息模板", "x-apifox-orders": ["templateTitleId", "keyworkIdList", "sceneDescription"]}}}, {"name": "AddTenantInput", "displayName": "", "id": "#/definitions/********", "description": "", "schema": {"jsonSchema": {"required": ["adminAccount", "name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "userId": {"type": "integer", "description": "用户Id", "format": "int64"}, "orgId": {"type": "integer", "description": "机构Id", "format": "int64"}, "host": {"maxLength": 128, "type": ["string", "null"], "description": "主机"}, "tenantType": {"$ref": "#/definitions/********"}, "dbType": {"$ref": "#/definitions/84275276"}, "connection": {"maxLength": 256, "type": ["string", "null"], "description": "数据库连接"}, "configId": {"maxLength": 64, "type": ["string", "null"], "description": "数据库标识"}, "slaveConnections": {"type": ["string", "null"], "description": "从库连接/读写分离"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}, "status": {"$ref": "#/definitions/********"}, "email": {"type": ["string", "null"], "description": "电子邮箱"}, "phone": {"type": ["string", "null"], "description": "电话"}, "name": {"minLength": 2, "type": "string", "description": "租户名称"}, "adminAccount": {"minLength": 3, "type": "string", "description": "租管账号"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "userId", "orgId", "host", "tenantType", "dbType", "connection", "configId", "slaveConnections", "orderNo", "remark", "status", "email", "phone", "name", "adminAccount"]}}}, {"name": "AddUserInput", "displayName": "", "id": "#/definitions/********", "description": "增加用户输入参数", "schema": {"jsonSchema": {"required": ["account", "realName"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "nickName": {"maxLength": 32, "type": ["string", "null"], "description": "昵称"}, "avatar": {"maxLength": 512, "type": ["string", "null"], "description": "头像"}, "sex": {"$ref": "#/definitions/********"}, "age": {"type": "integer", "description": "年龄", "format": "int32"}, "birthday": {"type": ["string", "null"], "description": "出生日期", "format": "date-time"}, "nation": {"maxLength": 32, "type": ["string", "null"], "description": "民族"}, "phone": {"maxLength": 16, "type": ["string", "null"], "description": "手机号码"}, "cardType": {"$ref": "#/definitions/********"}, "idCardNum": {"maxLength": 32, "type": ["string", "null"], "description": "身份证号"}, "email": {"maxLength": 64, "type": ["string", "null"], "description": "邮箱"}, "address": {"maxLength": 256, "type": ["string", "null"], "description": "地址"}, "cultureLevel": {"$ref": "#/definitions/84275267"}, "politicalOutlook": {"maxLength": 16, "type": ["string", "null"], "description": "政治面貌"}, "college": {"maxLength": 128, "type": ["string", "null"], "description": "毕业院校"}, "officePhone": {"maxLength": 16, "type": ["string", "null"], "description": "办公电话"}, "emergencyContact": {"maxLength": 32, "type": ["string", "null"], "description": "紧急联系人"}, "emergencyPhone": {"maxLength": 16, "type": ["string", "null"], "description": "紧急联系人电话"}, "emergencyAddress": {"maxLength": 256, "type": ["string", "null"], "description": "紧急联系人地址"}, "introduction": {"maxLength": 512, "type": ["string", "null"], "description": "个人简介"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "status": {"$ref": "#/definitions/********"}, "remark": {"maxLength": 256, "type": ["string", "null"], "description": "备注"}, "accountType": {"$ref": "#/definitions/********"}, "orgId": {"type": "integer", "description": "直属机构Id", "format": "int64"}, "sysOrg": {"$ref": "#/definitions/********"}, "managerUserId": {"type": ["integer", "null"], "description": "直属主管Id", "format": "int64"}, "posId": {"type": "integer", "description": "职位Id", "format": "int64"}, "jobNum": {"maxLength": 32, "type": ["string", "null"], "description": "工号"}, "posLevel": {"maxLength": 32, "type": ["string", "null"], "description": "职级"}, "posTitle": {"maxLength": 32, "type": ["string", "null"], "description": "职称"}, "expertise": {"maxLength": 32, "type": ["string", "null"], "description": "擅长领域"}, "officeZone": {"maxLength": 32, "type": ["string", "null"], "description": "办公区域"}, "office": {"maxLength": 32, "type": ["string", "null"], "description": "办公室"}, "joinDate": {"type": ["string", "null"], "description": "入职日期", "format": "date-time"}, "lastLoginIp": {"maxLength": 256, "type": ["string", "null"], "description": "最新登录Ip"}, "lastLoginAddress": {"maxLength": 128, "type": ["string", "null"], "description": "最新登录地点"}, "lastLoginTime": {"type": ["string", "null"], "description": "最新登录时间", "format": "date-time"}, "lastLoginDevice": {"maxLength": 128, "type": ["string", "null"], "description": "最新登录设备"}, "signature": {"maxLength": 512, "type": ["string", "null"], "description": "电子签名"}, "account": {"minLength": 1, "type": "string", "description": "账号"}, "realName": {"minLength": 1, "type": "string", "description": "真实姓名"}, "roleIdList": {"type": ["array", "null"], "items": {"type": "integer", "format": "int64"}, "description": "角色集合"}, "extOrgIdList": {"type": ["array", "null"], "items": {"$ref": "#/definitions/********"}, "description": "扩展机构集合"}}, "additionalProperties": false, "description": "增加用户输入参数", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "nick<PERSON><PERSON>", "avatar", "sex", "age", "birthday", "nation", "phone", "cardType", "idCardNum", "email", "address", "cultureLevel", "politicalOutlook", "college", "officePhone", "emergencyContact", "emergencyPhone", "emergencyAddress", "introduction", "orderNo", "status", "remark", "accountType", "orgId", "sysOrg", "managerUserId", "posId", "job<PERSON>um", "posLevel", "posTitle", "expertise", "officeZone", "office", "joinDate", "lastLoginIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lastLoginTime", "lastLoginDevice", "signature", "account", "realName", "roleIdList", "extOrgIdList"]}}}, {"name": "AdminResult_Boolean", "displayName": "", "id": "#/definitions/********", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": "boolean", "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_IActionResult", "displayName": "", "id": "#/definitions/********", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275306"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_Int32", "displayName": "", "id": "#/definitions/84275188", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": "integer", "description": "数据", "format": "int32"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_Int64", "displayName": "", "id": "#/definitions/84275189", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": "integer", "description": "数据", "format": "int64"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_JObject", "displayName": "", "id": "#/definitions/84275190", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["object", "null"], "additionalProperties": {"$ref": "#/definitions/84275307"}, "description": "数据", "x-apifox-orders": []}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_ApiOutput", "displayName": "", "id": "#/definitions/84275191", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275257"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_CodeGenConfig", "displayName": "", "id": "#/definitions/84275192", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275261"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_ColumnOuput", "displayName": "", "id": "#/definitions/84275193", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275263"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_ConstOutput", "displayName": "", "id": "#/definitions/84275194", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275264"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_DatabaseOutput", "displayName": "", "id": "#/definitions/84275195", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275270"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_DbColumnOutput", "displayName": "", "id": "#/definitions/84275196", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275272"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_DbTableInfo", "displayName": "", "id": "#/definitions/84275197", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275274"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_EnumEntity", "displayName": "", "id": "#/definitions/84275198", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275301"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_EnumTypeOutput", "displayName": "", "id": "#/definitions/84275199", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275302"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_Int64", "displayName": "", "id": "#/definitions/84275200", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"type": "integer", "format": "int64"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_MenuOutput", "displayName": "", "id": "#/definitions/84275201", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/********"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_RoleOutput", "displayName": "", "id": "#/definitions/84275202", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275349"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_String", "displayName": "", "id": "#/definitions/84275203", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"type": "string"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_SysConfig", "displayName": "", "id": "#/definitions/84275204", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275378"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_SysDictData", "displayName": "", "id": "#/definitions/84275205", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275379"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_SysDictType", "displayName": "", "id": "#/definitions/84275206", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275380"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_SysFile", "displayName": "", "id": "#/definitions/84275207", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275381"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_SysJobCluster", "displayName": "", "id": "#/definitions/84275208", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275382"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_SysJobTrigger", "displayName": "", "id": "#/definitions/84275209", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275384"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_SysMenu", "displayName": "", "id": "#/definitions/84275210", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/********"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_SysNotice", "displayName": "", "id": "#/definitions/84275211", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275392"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_SysOrg", "displayName": "", "id": "#/definitions/84275212", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/********"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_SysPos", "displayName": "", "id": "#/definitions/84275213", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275397"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_SysRegion", "displayName": "", "id": "#/definitions/84275214", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275399"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_SysUser", "displayName": "", "id": "#/definitions/84275215", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/********"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_SysUserExtOrg", "displayName": "", "id": "#/definitions/84275216", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/********"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_List_TableOutput", "displayName": "", "id": "#/definitions/84275217", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275405"}, "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_LoginOutput", "displayName": "", "id": "#/definitions/84275218", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/********"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_LoginUserOutput", "displayName": "", "id": "#/definitions/84275219", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/********"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_Object", "displayName": "", "id": "#/definitions/84275220", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"additionalProperties": false, "description": "数据", "type": "null"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SmKeyPairOutput", "displayName": "", "id": "#/definitions/84275221", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275352"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_JobDetailOutput", "displayName": "", "id": "#/definitions/84275222", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275353"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_OpenAccessOutput", "displayName": "", "id": "#/definitions/84275223", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275354"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_SysCodeGen", "displayName": "", "id": "#/definitions/84275224", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275355"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_SysConfig", "displayName": "", "id": "#/definitions/84275225", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275356"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_SysDictData", "displayName": "", "id": "#/definitions/84275226", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275357"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_SysDictType", "displayName": "", "id": "#/definitions/84275227", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275358"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_SysFile", "displayName": "", "id": "#/definitions/84275228", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275359"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_SysJobTriggerRecord", "displayName": "", "id": "#/definitions/84275229", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275360"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_SysLogDiff", "displayName": "", "id": "#/definitions/84275230", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275361"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_SysLogEx", "displayName": "", "id": "#/definitions/84275231", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275362"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_SysLogOp", "displayName": "", "id": "#/definitions/84275232", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275363"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_SysLogVis", "displayName": "", "id": "#/definitions/84275233", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275364"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_SysNotice", "displayName": "", "id": "#/definitions/84275234", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275365"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_SysNoticeUser", "displayName": "", "id": "#/definitions/84275235", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275366"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_SysOnlineUser", "displayName": "", "id": "#/definitions/84275236", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275367"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_SysPlugin", "displayName": "", "id": "#/definitions/84275237", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275368"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_SysPrint", "displayName": "", "id": "#/definitions/84275238", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275369"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_SysRegion", "displayName": "", "id": "#/definitions/84275239", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275370"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_SysRole", "displayName": "", "id": "#/definitions/84275240", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275371"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_SysWechatUser", "displayName": "", "id": "#/definitions/84275241", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275372"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_TenantOutput", "displayName": "", "id": "#/definitions/84275242", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275373"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SqlSugarPagedList_UserOutput", "displayName": "", "id": "#/definitions/84275243", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275374"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_String", "displayName": "", "id": "#/definitions/84275244", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"type": ["string", "null"], "description": "数据"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SysCodeGen", "displayName": "", "id": "#/definitions/84275245", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275376"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SysCodeGenConfig", "displayName": "", "id": "#/definitions/84275246", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275377"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SysConfig", "displayName": "", "id": "#/definitions/84275247", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275378"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SysDictData", "displayName": "", "id": "#/definitions/84275248", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275379"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SysDictType", "displayName": "", "id": "#/definitions/84275249", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275380"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SysFile", "displayName": "", "id": "#/definitions/84275250", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275381"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SysPrint", "displayName": "", "id": "#/definitions/84275251", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275398"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SysUser", "displayName": "", "id": "#/definitions/84275252", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/********"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_SysWechatPay", "displayName": "", "id": "#/definitions/84275253", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275403"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_WechatPayOutput", "displayName": "", "id": "#/definitions/84275254", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/********"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_WxOpenIdOutput", "displayName": "", "id": "#/definitions/84275255", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275442"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "AdminResult_WxPhoneOutput", "displayName": "", "id": "#/definitions/84275256", "description": "全局返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "type": {"type": ["string", "null"], "description": "类型success、warning、error"}, "message": {"type": ["string", "null"], "description": "错误信息"}, "result": {"$ref": "#/definitions/84275443"}, "extras": {"additionalProperties": false, "description": "附加数据", "type": "null"}, "time": {"type": "string", "description": "时间", "format": "date-time"}}, "additionalProperties": false, "description": "全局返回结果", "x-apifox-orders": ["code", "type", "message", "result", "extras", "time"]}}}, {"name": "ApiOutput", "displayName": "", "id": "#/definitions/84275257", "description": "接口/动态API输出", "schema": {"jsonSchema": {"type": "object", "properties": {"groupName": {"type": ["string", "null"], "description": "组名称"}, "displayName": {"type": ["string", "null"], "description": "接口名称"}, "routeName": {"type": ["string", "null"], "description": "路由名称"}}, "additionalProperties": false, "description": "接口/动态API输出", "x-apifox-orders": ["groupName", "displayName", "routeName"]}}}, {"name": "CardTypeEnum", "displayName": "", "id": "#/definitions/********", "description": "证件类型枚举<br />&nbsp;身份证 IdCard = 0<br />&nbsp;护照 PassportCard = 1<br />&nbsp;出生证 BirthCard = 2<br />&nbsp;港澳台通行证 GatCard = 3<br />&nbsp;外国人居留证 ForeignCard = 4<br />&nbsp;营业执照 License = 5<br />", "schema": {"jsonSchema": {"enum": [0, 1, 2, 3, 4, 5], "type": "integer", "description": "证件类型枚举<br />&nbsp;身份证 IdCard = 0<br />&nbsp;护照 PassportCard = 1<br />&nbsp;出生证 BirthCard = 2<br />&nbsp;港澳台通行证 GatCard = 3<br />&nbsp;外国人居留证 ForeignCard = 4<br />&nbsp;营业执照 License = 5<br />", "format": "int32"}}}, {"name": "ChangePwdInput", "displayName": "", "id": "#/definitions/84275259", "description": "修改用户密码输入参数", "schema": {"jsonSchema": {"required": ["passwordNew", "passwordOld"], "type": "object", "properties": {"passwordOld": {"minLength": 1, "type": "string", "description": "当前密码"}, "passwordNew": {"maxLength": 20, "minLength": 5, "type": "string", "description": "新密码"}}, "additionalProperties": false, "description": "修改用户密码输入参数", "x-apifox-orders": ["passwordOld", "passwordNew"]}}}, {"name": "ClusterStatus", "displayName": "", "id": "#/definitions/84275260", "description": "<br />&nbsp; Crashed = 0<br />&nbsp; Working = 1<br />&nbsp; Waiting = 2<br />", "schema": {"jsonSchema": {"enum": [0, 1, 2], "type": "integer", "description": "<br />&nbsp; Crashed = 0<br />&nbsp; Working = 1<br />&nbsp; Waiting = 2<br />", "format": "int32"}}}, {"name": "CodeGenConfig", "displayName": "", "id": "#/definitions/84275261", "description": "代码生成详细配置参数", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "codeGenId": {"type": "integer", "description": "代码生成主表ID", "format": "int64"}, "columnName": {"type": ["string", "null"], "description": "数据库字段名"}, "propertyName": {"type": ["string", "null"], "description": "实体属性名"}, "columnLength": {"type": "integer", "description": "字段数据长度", "format": "int32"}, "lowerPropertyName": {"type": ["string", "null"], "description": "数据库字段名(首字母小写)", "readOnly": true}, "columnComment": {"type": ["string", "null"], "description": "字段描述"}, "netType": {"type": ["string", "null"], "description": ".NET类型"}, "effectType": {"type": ["string", "null"], "description": "作用类型（字典）"}, "fkEntityName": {"type": ["string", "null"], "description": "外键实体名称"}, "fkTableName": {"type": ["string", "null"], "description": "外键表名称"}, "lowerFkEntityName": {"type": ["string", "null"], "description": "外键实体名称(首字母小写)", "readOnly": true}, "fkColumnName": {"type": ["string", "null"], "description": "外键显示字段"}, "lowerFkColumnName": {"type": ["string", "null"], "description": "外键显示字段(首字母小写)", "readOnly": true}, "fkColumnNetType": {"type": ["string", "null"], "description": "外键显示字段.NET类型"}, "dictTypeCode": {"type": ["string", "null"], "description": "字典code"}, "whetherRetract": {"type": ["string", "null"], "description": "列表是否缩进（字典）"}, "whetherRequired": {"type": ["string", "null"], "description": "是否必填（字典）"}, "whetherSortable": {"type": ["string", "null"], "description": "是否可排序（字典）"}, "queryWhether": {"type": ["string", "null"], "description": "是否是查询条件"}, "queryType": {"type": ["string", "null"], "description": "查询方式"}, "whetherTable": {"type": ["string", "null"], "description": "列表显示"}, "whetherAddUpdate": {"type": ["string", "null"], "description": "增改"}, "columnKey": {"type": ["string", "null"], "description": "主外键"}, "dataType": {"type": ["string", "null"], "description": "数据库中类型（物理类型）"}, "whetherCommon": {"type": ["string", "null"], "description": "是否是通用字段"}, "tableNickName": {"type": ["string", "null"], "description": "表的别名 Table as XXX", "readOnly": true}, "displayColumn": {"type": ["string", "null"], "description": "显示文本字段"}, "valueColumn": {"type": ["string", "null"], "description": "选中值字段"}, "pidColumn": {"type": ["string", "null"], "description": "父级字段"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}}, "additionalProperties": false, "description": "代码生成详细配置参数", "x-apifox-orders": ["id", "codeGenId", "columnName", "propertyName", "columnLength", "lowerPropertyName", "columnComment", "netType", "effectType", "fkEntityName", "fkTableName", "lowerFkEntityName", "fkColumnName", "lowerFkColumnName", "fkColumnNetType", "dictTypeCode", "whetherRetract", "whetherRequired", "whetherSortable", "query<PERSON><PERSON><PERSON>", "queryType", "whetherTable", "whetherAddUpdate", "column<PERSON>ey", "dataType", "<PERSON><PERSON><PERSON><PERSON>", "tableNickName", "displayColumn", "valueColumn", "pidColumn", "orderNo"]}}}, {"name": "CodeGenInput", "displayName": "", "id": "#/definitions/84275262", "description": "代码生成参数类", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": ["string", "null"], "description": "排序字段"}, "order": {"type": ["string", "null"], "description": "排序方向"}, "descStr": {"type": ["string", "null"], "description": "降序排序"}, "authorName": {"type": ["string", "null"], "description": "作者姓名"}, "className": {"type": ["string", "null"], "description": "类名"}, "tablePrefix": {"type": ["string", "null"], "description": "是否移除表前缀"}, "configId": {"type": ["string", "null"], "description": "库定位器名"}, "dbName": {"type": ["string", "null"], "description": "数据库名(保留字段)"}, "dbType": {"type": ["string", "null"], "description": "数据库类型"}, "connectionString": {"type": ["string", "null"], "description": "数据库链接"}, "generateType": {"type": ["string", "null"], "description": "生成方式"}, "tableName": {"type": ["string", "null"], "description": "数据库表名"}, "nameSpace": {"type": ["string", "null"], "description": "命名空间"}, "busName": {"type": ["string", "null"], "description": "业务名（业务代码包名称）"}, "tableComment": {"type": ["string", "null"], "description": "功能名（数据库表名称）"}, "menuApplication": {"type": ["string", "null"], "description": "菜单应用分类（应用编码）"}, "menuPid": {"type": "integer", "description": "菜单父级", "format": "int64"}, "printType": {"type": ["string", "null"], "description": "支持打印类型"}, "printName": {"type": ["string", "null"], "description": "打印模版名称"}}, "additionalProperties": false, "description": "代码生成参数类", "x-apifox-orders": ["page", "pageSize", "field", "order", "descStr", "<PERSON><PERSON><PERSON>", "className", "tablePrefix", "configId", "dbN<PERSON>", "dbType", "connectionString", "generateType", "tableName", "nameSpace", "busName", "tableComment", "menuApplication", "menuPid", "printType", "printName"]}}}, {"name": "ColumnOuput", "displayName": "", "id": "#/definitions/84275263", "description": "数据库表列", "schema": {"jsonSchema": {"type": "object", "properties": {"columnName": {"type": ["string", "null"], "description": "字段名"}, "propertyName": {"type": ["string", "null"], "description": "实体的Property名"}, "columnLength": {"type": "integer", "description": "字段数据长度", "format": "int32"}, "dataType": {"type": ["string", "null"], "description": "数据库中类型"}, "isPrimarykey": {"type": "boolean", "description": "是否为主键"}, "isNullable": {"type": "boolean", "description": "是否允许为空"}, "netType": {"type": ["string", "null"], "description": ".NET字段类型"}, "columnComment": {"type": ["string", "null"], "description": "字段描述"}, "columnKey": {"type": ["string", "null"], "description": "主外键"}}, "additionalProperties": false, "description": "数据库表列", "x-apifox-orders": ["columnName", "propertyName", "columnLength", "dataType", "isPrimarykey", "isNullable", "netType", "columnComment", "column<PERSON>ey"]}}}, {"name": "ConstOutput", "displayName": "", "id": "#/definitions/84275264", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"name": {"type": ["string", "null"], "description": "名称"}, "code": {"additionalProperties": false, "description": "编码", "type": "null"}, "data": {"additionalProperties": false, "description": "扩展字段", "type": "null"}}, "additionalProperties": false, "x-apifox-orders": ["name", "code", "data"]}}}, {"name": "CreateEntityInput", "displayName": "", "id": "#/definitions/84275265", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"tableName": {"type": ["string", "null"], "description": "表名", "examples": ["student"]}, "entityName": {"type": ["string", "null"], "description": "实体名", "examples": ["Student"]}, "baseClassName": {"type": ["string", "null"], "description": "基类名", "examples": ["AutoIncrementEntity"]}, "position": {"type": ["string", "null"], "description": "导出位置", "examples": ["Web.Application"]}, "configId": {"type": ["string", "null"], "description": "库标识"}}, "additionalProperties": false, "x-apifox-orders": ["tableName", "entityName", "baseClassName", "position", "configId"]}}}, {"name": "CreateSeedDataInput", "displayName": "", "id": "#/definitions/84275266", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"configId": {"type": ["string", "null"], "description": "库标识"}, "tableName": {"type": ["string", "null"], "description": "表名", "examples": ["student"]}, "entityName": {"type": ["string", "null"], "description": "实体名称", "examples": ["Student"]}, "seedDataName": {"type": ["string", "null"], "description": "种子名称", "examples": ["Student"]}, "position": {"type": ["string", "null"], "description": "导出位置", "examples": ["Web.Application"]}, "suffix": {"type": ["string", "null"], "description": "后缀", "examples": ["Web.Application"]}}, "additionalProperties": false, "x-apifox-orders": ["configId", "tableName", "entityName", "seedDataName", "position", "suffix"]}}}, {"name": "CultureLevelEnum", "displayName": "", "id": "#/definitions/84275267", "description": "文化程度枚举<br />&nbsp;其他 Level0 = 0<br />&nbsp;小学 Level1 = 1<br />&nbsp;初中 Level2 = 2<br />&nbsp;普通高中 Level3 = 3<br />&nbsp;技工学校 Level4 = 4<br />&nbsp;职业教育 Level5 = 5<br />&nbsp;职业高中 Level6 = 6<br />&nbsp;中等专科 Level7 = 7<br />&nbsp;大学专科 Level8 = 8<br />&nbsp;大学本科 Level9 = 9<br />&nbsp;硕士研究生 Level10 = 10<br />&nbsp;博士研究生 Level11 = 11<br />", "schema": {"jsonSchema": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], "type": "integer", "description": "文化程度枚举<br />&nbsp;其他 Level0 = 0<br />&nbsp;小学 Level1 = 1<br />&nbsp;初中 Level2 = 2<br />&nbsp;普通高中 Level3 = 3<br />&nbsp;技工学校 Level4 = 4<br />&nbsp;职业教育 Level5 = 5<br />&nbsp;职业高中 Level6 = 6<br />&nbsp;中等专科 Level7 = 7<br />&nbsp;大学专科 Level8 = 8<br />&nbsp;大学本科 Level9 = 9<br />&nbsp;硕士研究生 Level10 = 10<br />&nbsp;博士研究生 Level11 = 11<br />", "format": "int32"}}}, {"name": "DataItem", "displayName": "", "id": "#/definitions/84275268", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"value": {"type": ["string", "null"]}}, "additionalProperties": false, "x-apifox-orders": ["value"]}}}, {"name": "DataScopeEnum", "displayName": "", "id": "#/definitions/84275269", "description": "角色数据范围枚举<br />&nbsp;全部数据 All = 1<br />&nbsp;本部门及以下数据 DeptChild = 2<br />&nbsp;本部门数据 Dept = 3<br />&nbsp;仅本人数据 Self = 4<br />&nbsp;自定义数据 Define = 5<br />", "schema": {"jsonSchema": {"enum": [1, 2, 3, 4, 5], "type": "integer", "description": "角色数据范围枚举<br />&nbsp;全部数据 All = 1<br />&nbsp;本部门及以下数据 DeptChild = 2<br />&nbsp;本部门数据 Dept = 3<br />&nbsp;仅本人数据 Self = 4<br />&nbsp;自定义数据 Define = 5<br />", "format": "int32"}}}, {"name": "DatabaseOutput", "displayName": "", "id": "#/definitions/84275270", "description": "数据库", "schema": {"jsonSchema": {"type": "object", "properties": {"configId": {"type": ["string", "null"], "description": "库定位器名"}, "dbType": {"$ref": "#/definitions/84275276"}, "connectionString": {"type": ["string", "null"], "description": "数据库连接字符串"}}, "additionalProperties": false, "description": "数据库", "x-apifox-orders": ["configId", "dbType", "connectionString"]}}}, {"name": "DbColumnInput", "displayName": "", "id": "#/definitions/84275271", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"configId": {"type": ["string", "null"]}, "tableName": {"type": ["string", "null"]}, "dbColumnName": {"type": ["string", "null"]}, "dataType": {"type": ["string", "null"]}, "length": {"type": "integer", "format": "int32"}, "columnDescription": {"type": ["string", "null"]}, "isNullable": {"type": "integer", "format": "int32"}, "isIdentity": {"type": "integer", "format": "int32"}, "isPrimarykey": {"type": "integer", "format": "int32"}, "decimalDigits": {"type": "integer", "format": "int32"}}, "additionalProperties": false, "x-apifox-orders": ["configId", "tableName", "dbColumnName", "dataType", "length", "columnDescription", "isNullable", "isIdentity", "isPrimarykey", "decimalDigits"]}}}, {"name": "DbColumnOutput", "displayName": "", "id": "#/definitions/84275272", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"tableName": {"type": ["string", "null"]}, "tableId": {"type": "integer", "format": "int32"}, "dbColumnName": {"type": ["string", "null"]}, "propertyName": {"type": ["string", "null"]}, "dataType": {"type": ["string", "null"]}, "propertyType": {"additionalProperties": false, "type": "null"}, "length": {"type": "integer", "format": "int32"}, "columnDescription": {"type": ["string", "null"]}, "defaultValue": {"type": ["string", "null"]}, "isNullable": {"type": "boolean"}, "isIdentity": {"type": "boolean"}, "isPrimarykey": {"type": "boolean"}, "value": {"additionalProperties": false, "type": "null"}, "decimalDigits": {"type": "integer", "format": "int32"}, "scale": {"type": "integer", "format": "int32"}, "isArray": {"type": "boolean"}, "isJson": {"type": "boolean"}, "isUnsigned": {"type": ["boolean", "null"]}, "createTableFieldSort": {"type": "integer", "format": "int32"}}, "additionalProperties": false, "x-apifox-orders": ["tableName", "tableId", "dbColumnName", "propertyName", "dataType", "propertyType", "length", "columnDescription", "defaultValue", "isNullable", "isIdentity", "isPrimarykey", "value", "decimalDigits", "scale", "isArray", "isJson", "isUnsigned", "createTableFieldSort"]}}}, {"name": "DbObjectType", "displayName": "", "id": "#/definitions/84275273", "description": "", "schema": {"jsonSchema": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}}}, {"name": "DbTableInfo", "displayName": "", "id": "#/definitions/84275274", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"name": {"type": ["string", "null"]}, "description": {"type": ["string", "null"]}, "dbObjectType": {"$ref": "#/definitions/84275273"}}, "additionalProperties": false, "x-apifox-orders": ["name", "description", "dbObjectType"]}}}, {"name": "DbTableInput", "displayName": "", "id": "#/definitions/84275275", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"configId": {"type": ["string", "null"]}, "tableName": {"type": ["string", "null"]}, "description": {"type": ["string", "null"]}, "dbColumnInfoList": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275271"}}}, "additionalProperties": false, "x-apifox-orders": ["configId", "tableName", "description", "dbColumnInfoList"]}}}, {"name": "DbType", "displayName": "", "id": "#/definitions/84275276", "description": "", "schema": {"jsonSchema": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 900], "type": "integer", "format": "int32"}}}, {"name": "DeleteCodeGenInput", "displayName": "", "id": "#/definitions/84275277", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "代码生成器Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["id"]}}}, {"name": "DeleteConfigInput", "displayName": "", "id": "#/definitions/84275278", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["id"]}}}, {"name": "DeleteDbColumnInput", "displayName": "", "id": "#/definitions/84275279", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"configId": {"type": ["string", "null"]}, "tableName": {"type": ["string", "null"]}, "dbColumnName": {"type": ["string", "null"]}}, "additionalProperties": false, "x-apifox-orders": ["configId", "tableName", "dbColumnName"]}}}, {"name": "DeleteDbTableInput", "displayName": "", "id": "#/definitions/84275280", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"configId": {"type": ["string", "null"]}, "tableName": {"type": ["string", "null"]}}, "additionalProperties": false, "x-apifox-orders": ["configId", "tableName"]}}}, {"name": "DeleteDictDataInput", "displayName": "", "id": "#/definitions/84275281", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["id"]}}}, {"name": "DeleteDictTypeInput", "displayName": "", "id": "#/definitions/84275282", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["id"]}}}, {"name": "DeleteFileInput", "displayName": "", "id": "#/definitions/84275283", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["id"]}}}, {"name": "DeleteJobDetailInput", "displayName": "", "id": "#/definitions/84275284", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"jobId": {"type": ["string", "null"], "description": "作业Id"}}, "additionalProperties": false, "x-apifox-orders": ["jobId"]}}}, {"name": "DeleteJobTriggerInput", "displayName": "", "id": "#/definitions/84275285", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"jobId": {"type": ["string", "null"], "description": "作业Id"}, "triggerId": {"type": ["string", "null"], "description": "触发器Id"}}, "additionalProperties": false, "x-apifox-orders": ["jobId", "triggerId"]}}}, {"name": "DeleteMenuInput", "displayName": "", "id": "#/definitions/84275286", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["id"]}}}, {"name": "DeleteMessageTemplateInput", "displayName": "", "id": "#/definitions/84275287", "description": "删除消息模板", "schema": {"jsonSchema": {"required": ["templateId"], "type": "object", "properties": {"templateId": {"minLength": 1, "type": "string", "description": "订阅模板Id"}}, "additionalProperties": false, "description": "删除消息模板", "x-apifox-orders": ["templateId"]}}}, {"name": "DeleteNoticeInput", "displayName": "", "id": "#/definitions/84275288", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["id"]}}}, {"name": "DeleteOpenAccessInput", "displayName": "", "id": "#/definitions/84275289", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["id"]}}}, {"name": "DeleteOrgInput", "displayName": "", "id": "#/definitions/84275290", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["id"]}}}, {"name": "DeletePluginInput", "displayName": "", "id": "#/definitions/84275291", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["id"]}}}, {"name": "DeletePosInput", "displayName": "", "id": "#/definitions/84275292", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["id"]}}}, {"name": "DeletePrintInput", "displayName": "", "id": "#/definitions/84275293", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["id"]}}}, {"name": "DeleteRegionInput", "displayName": "", "id": "#/definitions/84275294", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["id"]}}}, {"name": "DeleteRoleInput", "displayName": "", "id": "#/definitions/84275295", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["id"]}}}, {"name": "DeleteTenantInput", "displayName": "", "id": "#/definitions/84275296", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["id"]}}}, {"name": "DeleteUserInput", "displayName": "", "id": "#/definitions/84275297", "description": "删除用户输入参数", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "orgId": {"type": "integer", "description": "机构Id", "format": "int64"}}, "additionalProperties": false, "description": "删除用户输入参数", "x-apifox-orders": ["id", "orgId"]}}}, {"name": "DeleteWechatUserInput", "displayName": "", "id": "#/definitions/84275298", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["id"]}}}, {"name": "DictDataInput", "displayName": "", "id": "#/definitions/84275299", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "status": {"$ref": "#/definitions/********"}}, "additionalProperties": false, "x-apifox-orders": ["id", "status"]}}}, {"name": "DictTypeInput", "displayName": "", "id": "#/definitions/84275300", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "status": {"$ref": "#/definitions/********"}}, "additionalProperties": false, "x-apifox-orders": ["id", "status"]}}}, {"name": "EnumEntity", "displayName": "", "id": "#/definitions/84275301", "description": "枚举实体", "schema": {"jsonSchema": {"type": "object", "properties": {"describe": {"type": ["string", "null"], "description": "枚举的描述"}, "name": {"type": ["string", "null"], "description": "枚举名称"}, "value": {"type": "integer", "description": "枚举对象的值", "format": "int32"}}, "additionalProperties": false, "description": "枚举实体", "x-apifox-orders": ["describe", "name", "value"]}}}, {"name": "EnumTypeOutput", "displayName": "", "id": "#/definitions/84275302", "description": "枚举类型输出参数", "schema": {"jsonSchema": {"type": "object", "properties": {"typeDescribe": {"type": ["string", "null"], "description": "枚举类型描述"}, "typeName": {"type": ["string", "null"], "description": "枚举类型名称"}, "typeRemark": {"type": ["string", "null"], "description": "枚举类型备注"}}, "additionalProperties": false, "description": "枚举类型输出参数", "x-apifox-orders": ["typeDescribe", "typeName", "typeRemark"]}}}, {"name": "FileInput", "displayName": "", "id": "#/definitions/84275303", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "fileName": {"type": ["string", "null"], "description": "文件名称"}, "url": {"type": ["string", "null"], "description": "文件Url"}}, "additionalProperties": false, "x-apifox-orders": ["id", "fileName", "url"]}}}, {"name": "GenAuthUrlInput", "displayName": "", "id": "#/definitions/84275304", "description": "生成网页授权Url", "schema": {"jsonSchema": {"type": "object", "properties": {"redirectUrl": {"type": ["string", "null"], "description": "RedirectUrl"}, "scope": {"type": ["string", "null"], "description": "<PERSON><PERSON>"}}, "additionalProperties": false, "description": "生成网页授权Url", "x-apifox-orders": ["redirectUrl", "scope"]}}}, {"name": "GenderEnum", "displayName": "", "id": "#/definitions/********", "description": "性别枚举<br />&nbsp;男 Male = 1<br />&nbsp;女 Female = 2<br />&nbsp;其他 Other = 3<br />", "schema": {"jsonSchema": {"enum": [1, 2, 3], "type": "integer", "description": "性别枚举<br />&nbsp;男 Male = 1<br />&nbsp;女 Female = 2<br />&nbsp;其他 Other = 3<br />", "format": "int32"}}}, {"name": "IActionResult", "displayName": "", "id": "#/definitions/84275306", "description": "", "schema": {"jsonSchema": {"type": "object", "additionalProperties": false, "x-apifox-orders": []}}}, {"name": "JToken", "displayName": "", "id": "#/definitions/84275307", "description": "", "schema": {"jsonSchema": {"type": "array", "items": {"$ref": "#/definitions/84275307"}}}}, {"name": "JobCreateTypeEnum", "displayName": "", "id": "#/definitions/84275308", "description": "作业创建类型枚举<br />&nbsp;内置 BuiltIn = 0<br />&nbsp;脚本 Script = 1<br />&nbsp;HTTP请求 Http = 2<br />", "schema": {"jsonSchema": {"enum": [0, 1, 2], "type": "integer", "description": "作业创建类型枚举<br />&nbsp;内置 BuiltIn = 0<br />&nbsp;脚本 Script = 1<br />&nbsp;HTTP请求 Http = 2<br />", "format": "int32"}}}, {"name": "JobDetailInput", "displayName": "", "id": "#/definitions/84275309", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"jobId": {"type": ["string", "null"], "description": "作业Id"}}, "additionalProperties": false, "x-apifox-orders": ["jobId"]}}}, {"name": "JobDetailOutput", "displayName": "", "id": "#/definitions/84275310", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"jobDetail": {"$ref": "#/definitions/84275383"}, "jobTriggers": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275384"}, "description": "触发器集合"}}, "additionalProperties": false, "x-apifox-orders": ["jobDetail", "jobTriggers"]}}}, {"name": "JobTriggerInput", "displayName": "", "id": "#/definitions/84275311", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"jobId": {"type": ["string", "null"], "description": "作业Id"}, "triggerId": {"type": ["string", "null"], "description": "触发器Id"}}, "additionalProperties": false, "x-apifox-orders": ["jobId", "triggerId"]}}}, {"name": "LogInput", "displayName": "", "id": "#/definitions/84275312", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"startTime": {"type": ["string", "null"], "description": "开始时间", "format": "date-time"}, "endTime": {"type": ["string", "null"], "description": "结束时间", "format": "date-time"}}, "additionalProperties": false, "x-apifox-orders": ["startTime", "endTime"]}}}, {"name": "LogLevel", "displayName": "", "id": "#/definitions/********", "description": "", "schema": {"jsonSchema": {"enum": [0, 1, 2, 3, 4, 5, 6], "type": "integer", "format": "int32"}}}, {"name": "LoginInput", "displayName": "", "id": "#/definitions/********", "description": "用户登录参数", "schema": {"jsonSchema": {"required": ["account", "password"], "type": "object", "properties": {"account": {"minLength": 2, "type": "string", "description": "账号", "examples": ["admin"]}, "password": {"minLength": 3, "type": "string", "description": "密码", "examples": ["123456"]}, "codeId": {"type": "integer", "description": "验证码Id", "format": "int64"}, "code": {"type": ["string", "null"], "description": "验证码"}}, "additionalProperties": false, "description": "用户登录参数", "x-apifox-orders": ["account", "password", "codeId", "code"]}}}, {"name": "LoginOutput", "displayName": "", "id": "#/definitions/********", "description": "用户登录结果", "schema": {"jsonSchema": {"type": "object", "properties": {"accessToken": {"type": ["string", "null"], "description": "令牌Token"}, "refreshToken": {"type": ["string", "null"], "description": "刷新Token"}}, "additionalProperties": false, "description": "用户登录结果", "x-apifox-orders": ["accessToken", "refreshToken"]}}}, {"name": "LoginPhoneInput", "displayName": "", "id": "#/definitions/********", "description": "", "schema": {"jsonSchema": {"required": ["code", "phone"], "type": "object", "properties": {"phone": {"minLength": 1, "type": "string", "description": "手机号码", "examples": ["admin"]}, "code": {"minLength": 4, "type": "string", "description": "验证码", "examples": ["123456"]}}, "additionalProperties": false, "x-apifox-orders": ["phone", "code"]}}}, {"name": "LoginUserOutput", "displayName": "", "id": "#/definitions/********", "description": "用户登录信息", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "用户id", "format": "int64"}, "account": {"type": ["string", "null"], "description": "账号名称"}, "realName": {"type": ["string", "null"], "description": "真实姓名"}, "phone": {"type": ["string", "null"], "description": "电话"}, "idCardNum": {"type": ["string", "null"], "description": "身份证"}, "email": {"type": ["string", "null"], "description": "邮箱"}, "accountType": {"$ref": "#/definitions/********"}, "avatar": {"type": ["string", "null"], "description": "头像"}, "introduction": {"type": ["string", "null"], "description": "个人简介"}, "address": {"type": ["string", "null"], "description": "地址"}, "signature": {"type": ["string", "null"], "description": "电子签名"}, "orgId": {"type": "integer", "description": "机构Id", "format": "int64"}, "orgName": {"type": ["string", "null"], "description": "机构名称"}, "orgType": {"type": ["string", "null"], "description": "机构类型"}, "posName": {"type": ["string", "null"], "description": "职位名称"}, "buttons": {"type": ["array", "null"], "items": {"type": "string"}, "description": "按钮权限集合"}, "roleIds": {"type": ["array", "null"], "items": {"type": "integer", "format": "int64"}, "description": "角色集合"}}, "additionalProperties": false, "description": "用户登录信息", "x-apifox-orders": ["id", "account", "realName", "phone", "idCardNum", "email", "accountType", "avatar", "introduction", "address", "signature", "orgId", "orgName", "orgType", "posName", "buttons", "roleIds"]}}}, {"name": "MenuOutput", "displayName": "", "id": "#/definitions/********", "description": "系统菜单返回结果", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "Id", "format": "int64"}, "pid": {"type": "integer", "description": "父Id", "format": "int64"}, "type": {"$ref": "#/definitions/********"}, "name": {"type": ["string", "null"], "description": "名称"}, "path": {"type": ["string", "null"], "description": "路由地址"}, "component": {"type": ["string", "null"], "description": "组件路径"}, "permission": {"type": ["string", "null"], "description": "权限标识"}, "redirect": {"type": ["string", "null"], "description": "重定向"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "status": {"$ref": "#/definitions/********"}, "remark": {"type": ["string", "null"], "description": "备注"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "meta": {"$ref": "#/definitions/84275391"}, "children": {"type": ["array", "null"], "items": {"$ref": "#/definitions/********"}, "description": "菜单子项"}}, "additionalProperties": false, "description": "系统菜单返回结果", "x-apifox-orders": ["id", "pid", "type", "name", "path", "component", "permission", "redirect", "orderNo", "status", "remark", "createTime", "updateTime", "createUserName", "updateUserName", "meta", "children"]}}}, {"name": "MenuTypeEnum", "displayName": "", "id": "#/definitions/********", "description": "系统菜单类型枚举<br />&nbsp;目录 Dir = 1<br />&nbsp;菜单 Menu = 2<br />&nbsp;按钮 Btn = 3<br />", "schema": {"jsonSchema": {"enum": [1, 2, 3], "type": "integer", "description": "系统菜单类型枚举<br />&nbsp;目录 Dir = 1<br />&nbsp;菜单 Menu = 2<br />&nbsp;按钮 Btn = 3<br />", "format": "int32"}}}, {"name": "MessageInput", "displayName": "", "id": "#/definitions/84275320", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"userId": {"type": "integer", "description": "用户ID", "format": "int64"}, "userIds": {"type": ["array", "null"], "items": {"type": "integer", "format": "int64"}, "description": "用户ID列表"}, "title": {"type": ["string", "null"], "description": "消息标题"}, "messageType": {"$ref": "#/definitions/84275322"}, "message": {"type": ["string", "null"], "description": "消息内容"}}, "additionalProperties": false, "x-apifox-orders": ["userId", "userIds", "title", "messageType", "message"]}}}, {"name": "MessageTemplateSendInput", "displayName": "", "id": "#/definitions/84275321", "description": "获取消息模板列表", "schema": {"jsonSchema": {"required": ["data", "templateId", "toUserOpenId"], "type": "object", "properties": {"templateId": {"minLength": 1, "type": "string", "description": "订阅模板Id"}, "toUserOpenId": {"minLength": 1, "type": "string", "description": "接收者的OpenId"}, "data": {"type": "object", "additionalProperties": {"$ref": "#/definitions/84275268"}, "description": "模板数据，格式形如 { \"key1\": { \"value\": any }, \"key2\": { \"value\": any } }", "x-apifox-orders": []}, "url": {"type": ["string", "null"], "description": "模板跳转链接"}, "miniProgramPagePath": {"type": ["string", "null"], "description": "所需跳转到小程序的具体页面路径，支持带参数,（示例index?foo=bar）"}}, "additionalProperties": false, "description": "获取消息模板列表", "x-apifox-orders": ["templateId", "toUserOpenId", "data", "url", "miniProgramPagePath"]}}}, {"name": "MessageTypeEnum", "displayName": "", "id": "#/definitions/84275322", "description": "消息类型枚举<br />&nbsp;消息 Info = 0<br />&nbsp;成功 Success = 1<br />&nbsp;警告 Warning = 2<br />&nbsp;错误 Error = 3<br />", "schema": {"jsonSchema": {"enum": [0, 1, 2, 3], "type": "integer", "description": "消息类型枚举<br />&nbsp;消息 Info = 0<br />&nbsp;成功 Success = 1<br />&nbsp;警告 Warning = 2<br />&nbsp;错误 Error = 3<br />", "format": "int32"}}}, {"name": "NoticeInput", "displayName": "", "id": "#/definitions/84275323", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["id"]}}}, {"name": "NoticeStatusEnum", "displayName": "", "id": "#/definitions/84275324", "description": "通知公告状态枚举<br />&nbsp;草稿 DRAFT = 0<br />&nbsp;发布 PUBLIC = 1<br />&nbsp;撤回 CANCEL = 2<br />&nbsp;删除 DELETED = 3<br />", "schema": {"jsonSchema": {"enum": [0, 1, 2, 3], "type": "integer", "description": "通知公告状态枚举<br />&nbsp;草稿 DRAFT = 0<br />&nbsp;发布 PUBLIC = 1<br />&nbsp;撤回 CANCEL = 2<br />&nbsp;删除 DELETED = 3<br />", "format": "int32"}}}, {"name": "NoticeTypeEnum", "displayName": "", "id": "#/definitions/84275325", "description": "通知公告状类型枚举<br />&nbsp;通知 NOTICE = 1<br />&nbsp;公告 ANNOUNCEMENT = 2<br />", "schema": {"jsonSchema": {"enum": [1, 2], "type": "integer", "description": "通知公告状类型枚举<br />&nbsp;通知 NOTICE = 1<br />&nbsp;公告 ANNOUNCEMENT = 2<br />", "format": "int32"}}}, {"name": "NoticeUserStatusEnum", "displayName": "", "id": "#/definitions/84275326", "description": "通知公告用户状态枚举<br />&nbsp;未读 UNREAD = 0<br />&nbsp;已读 READ = 1<br />", "schema": {"jsonSchema": {"enum": [0, 1], "type": "integer", "description": "通知公告用户状态枚举<br />&nbsp;未读 UNREAD = 0<br />&nbsp;已读 READ = 1<br />", "format": "int32"}}}, {"name": "OpenAccessInput", "displayName": "", "id": "#/definitions/84275327", "description": "开放接口身份输入参数", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": ["string", "null"], "description": "排序字段"}, "order": {"type": ["string", "null"], "description": "排序方向"}, "descStr": {"type": ["string", "null"], "description": "降序排序"}, "accessKey": {"type": ["string", "null"], "description": "身份标识"}}, "additionalProperties": false, "description": "开放接口身份输入参数", "x-apifox-orders": ["page", "pageSize", "field", "order", "descStr", "accessKey"]}}}, {"name": "OpenAccessOutput", "displayName": "", "id": "#/definitions/84275328", "description": "", "schema": {"jsonSchema": {"required": ["accessKey", "accessSecret"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "accessKey": {"maxLength": 128, "minLength": 1, "type": "string", "description": "身份标识"}, "accessSecret": {"maxLength": 256, "minLength": 1, "type": "string", "description": "密钥"}, "bindTenantId": {"type": "integer", "description": "绑定租户Id", "format": "int64"}, "bindUserId": {"type": "integer", "description": "绑定用户Id", "format": "int64"}, "bindUserAccount": {"type": ["string", "null"], "description": "绑定用户账号"}, "bindTenantName": {"type": ["string", "null"], "description": "绑定租户名称"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "accessKey", "accessSecret", "bindTenantId", "bindUserId", "bindUserAccount", "bindTenantName"]}}}, {"name": "PageConfigInput", "displayName": "", "id": "#/definitions/********", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": ["string", "null"], "description": "排序字段"}, "order": {"type": ["string", "null"], "description": "排序方向"}, "descStr": {"type": ["string", "null"], "description": "降序排序"}, "name": {"type": ["string", "null"], "description": "名称"}, "code": {"type": ["string", "null"], "description": "编码"}, "groupCode": {"type": ["string", "null"], "description": "分组编码"}}, "additionalProperties": false, "x-apifox-orders": ["page", "pageSize", "field", "order", "descStr", "name", "code", "groupCode"]}}}, {"name": "PageDictDataInput", "displayName": "", "id": "#/definitions/********", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": ["string", "null"], "description": "排序字段"}, "order": {"type": ["string", "null"], "description": "排序方向"}, "descStr": {"type": ["string", "null"], "description": "降序排序"}, "dictTypeId": {"type": "integer", "description": "字典类型Id", "format": "int64"}, "value": {"type": ["string", "null"], "description": "值"}, "code": {"type": ["string", "null"], "description": "编码"}}, "additionalProperties": false, "x-apifox-orders": ["page", "pageSize", "field", "order", "descStr", "dictTypeId", "value", "code"]}}}, {"name": "PageDictTypeInput", "displayName": "", "id": "#/definitions/84275331", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": ["string", "null"], "description": "排序字段"}, "order": {"type": ["string", "null"], "description": "排序方向"}, "descStr": {"type": ["string", "null"], "description": "降序排序"}, "name": {"type": ["string", "null"], "description": "名称"}, "code": {"type": ["string", "null"], "description": "编码"}}, "additionalProperties": false, "x-apifox-orders": ["page", "pageSize", "field", "order", "descStr", "name", "code"]}}}, {"name": "PageFileInput", "displayName": "", "id": "#/definitions/84275332", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": ["string", "null"], "description": "排序字段"}, "order": {"type": ["string", "null"], "description": "排序方向"}, "descStr": {"type": ["string", "null"], "description": "降序排序"}, "fileName": {"type": ["string", "null"], "description": "文件名称"}, "startTime": {"type": ["string", "null"], "description": "开始时间", "format": "date-time"}, "endTime": {"type": ["string", "null"], "description": "结束时间", "format": "date-time"}}, "additionalProperties": false, "x-apifox-orders": ["page", "pageSize", "field", "order", "descStr", "fileName", "startTime", "endTime"]}}}, {"name": "PageJobDetailInput", "displayName": "", "id": "#/definitions/84275333", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": ["string", "null"], "description": "排序字段"}, "order": {"type": ["string", "null"], "description": "排序方向"}, "descStr": {"type": ["string", "null"], "description": "降序排序"}, "jobId": {"type": ["string", "null"], "description": "作业Id"}, "description": {"type": ["string", "null"], "description": "描述信息"}}, "additionalProperties": false, "x-apifox-orders": ["page", "pageSize", "field", "order", "descStr", "jobId", "description"]}}}, {"name": "PageJobTriggerRecordInput", "displayName": "", "id": "#/definitions/84275334", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": ["string", "null"], "description": "排序字段"}, "order": {"type": ["string", "null"], "description": "排序方向"}, "descStr": {"type": ["string", "null"], "description": "降序排序"}, "jobId": {"type": ["string", "null"], "description": "作业Id"}, "triggerId": {"type": ["string", "null"], "description": "触发器Id"}}, "additionalProperties": false, "x-apifox-orders": ["page", "pageSize", "field", "order", "descStr", "jobId", "triggerId"]}}}, {"name": "PageLogInput", "displayName": "", "id": "#/definitions/84275335", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": ["string", "null"], "description": "排序字段"}, "order": {"type": ["string", "null"], "description": "排序方向"}, "descStr": {"type": ["string", "null"], "description": "降序排序"}, "startTime": {"type": ["string", "null"], "description": "开始时间", "format": "date-time"}, "endTime": {"type": ["string", "null"], "description": "结束时间", "format": "date-time"}}, "additionalProperties": false, "x-apifox-orders": ["page", "pageSize", "field", "order", "descStr", "startTime", "endTime"]}}}, {"name": "PageNoticeInput", "displayName": "", "id": "#/definitions/84275336", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": ["string", "null"], "description": "排序字段"}, "order": {"type": ["string", "null"], "description": "排序方向"}, "descStr": {"type": ["string", "null"], "description": "降序排序"}, "title": {"type": ["string", "null"], "description": "标题"}, "type": {"$ref": "#/definitions/84275325"}}, "additionalProperties": false, "x-apifox-orders": ["page", "pageSize", "field", "order", "descStr", "title", "type"]}}}, {"name": "PageOnlineUserInput", "displayName": "", "id": "#/definitions/84275337", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": ["string", "null"], "description": "排序字段"}, "order": {"type": ["string", "null"], "description": "排序方向"}, "descStr": {"type": ["string", "null"], "description": "降序排序"}, "userName": {"type": ["string", "null"], "description": "账号名称"}, "realName": {"type": ["string", "null"], "description": "真实姓名"}}, "additionalProperties": false, "x-apifox-orders": ["page", "pageSize", "field", "order", "descStr", "userName", "realName"]}}}, {"name": "PagePluginInput", "displayName": "", "id": "#/definitions/84275338", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": ["string", "null"], "description": "排序字段"}, "order": {"type": ["string", "null"], "description": "排序方向"}, "descStr": {"type": ["string", "null"], "description": "降序排序"}, "name": {"type": ["string", "null"], "description": "名称"}, "code": {"type": ["string", "null"], "description": "编码"}}, "additionalProperties": false, "x-apifox-orders": ["page", "pageSize", "field", "order", "descStr", "name", "code"]}}}, {"name": "PagePrintInput", "displayName": "", "id": "#/definitions/84275339", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": ["string", "null"], "description": "排序字段"}, "order": {"type": ["string", "null"], "description": "排序方向"}, "descStr": {"type": ["string", "null"], "description": "降序排序"}, "name": {"type": ["string", "null"], "description": "名称"}, "code": {"type": ["string", "null"], "description": "编码"}}, "additionalProperties": false, "x-apifox-orders": ["page", "pageSize", "field", "order", "descStr", "name", "code"]}}}, {"name": "PageRegionInput", "displayName": "", "id": "#/definitions/84275340", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": ["string", "null"], "description": "排序字段"}, "order": {"type": ["string", "null"], "description": "排序方向"}, "descStr": {"type": ["string", "null"], "description": "降序排序"}, "pid": {"type": "integer", "description": "父节点Id", "format": "int64"}, "name": {"type": ["string", "null"], "description": "名称"}, "code": {"type": ["string", "null"], "description": "编码"}}, "additionalProperties": false, "x-apifox-orders": ["page", "pageSize", "field", "order", "descStr", "pid", "name", "code"]}}}, {"name": "PageRoleInput", "displayName": "", "id": "#/definitions/84275341", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": ["string", "null"], "description": "排序字段"}, "order": {"type": ["string", "null"], "description": "排序方向"}, "descStr": {"type": ["string", "null"], "description": "降序排序"}, "name": {"type": ["string", "null"], "description": "名称"}, "code": {"type": ["string", "null"], "description": "编码"}}, "additionalProperties": false, "x-apifox-orders": ["page", "pageSize", "field", "order", "descStr", "name", "code"]}}}, {"name": "PageTenantInput", "displayName": "", "id": "#/definitions/84275342", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": ["string", "null"], "description": "排序字段"}, "order": {"type": ["string", "null"], "description": "排序方向"}, "descStr": {"type": ["string", "null"], "description": "降序排序"}, "name": {"type": ["string", "null"], "description": "名称"}, "phone": {"type": ["string", "null"], "description": "电话"}}, "additionalProperties": false, "x-apifox-orders": ["page", "pageSize", "field", "order", "descStr", "name", "phone"]}}}, {"name": "PageUserInput", "displayName": "", "id": "#/definitions/********", "description": "获取用户分页列表输入参数", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": ["string", "null"], "description": "排序字段"}, "order": {"type": ["string", "null"], "description": "排序方向"}, "descStr": {"type": ["string", "null"], "description": "降序排序"}, "account": {"type": ["string", "null"], "description": "账号"}, "realName": {"type": ["string", "null"], "description": "姓名"}, "phone": {"type": ["string", "null"], "description": "手机号"}, "orgId": {"type": "integer", "description": "查询时所选机构Id", "format": "int64"}}, "additionalProperties": false, "description": "获取用户分页列表输入参数", "x-apifox-orders": ["page", "pageSize", "field", "order", "descStr", "account", "realName", "phone", "orgId"]}}}, {"name": "PlatformTypeEnum", "displayName": "", "id": "#/definitions/********", "description": "平台类型枚举<br />&nbsp;微信公众号 微信公众号 = 1<br />&nbsp;微信小程序 微信小程序 = 2<br />&nbsp;QQ QQ = 3<br />&nbsp;支付宝 Alipay = 4<br />&nbsp;Gitee Gitee = 5<br />", "schema": {"jsonSchema": {"enum": [1, 2, 3, 4, 5], "type": "integer", "description": "平台类型枚举<br />&nbsp;微信公众号 微信公众号 = 1<br />&nbsp;微信小程序 微信小程序 = 2<br />&nbsp;QQ QQ = 3<br />&nbsp;支付宝 Alipay = 4<br />&nbsp;Gitee Gitee = 5<br />", "format": "int32"}}}, {"name": "ResetPwdUserInput", "displayName": "", "id": "#/definitions/********", "description": "重置用户密码输入参数", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "description": "重置用户密码输入参数", "x-apifox-orders": ["id"]}}}, {"name": "RoleInput", "displayName": "", "id": "#/definitions/84275346", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "status": {"$ref": "#/definitions/********"}}, "additionalProperties": false, "x-apifox-orders": ["id", "status"]}}}, {"name": "RoleMenuInput", "displayName": "", "id": "#/definitions/84275347", "description": "授权角色菜单", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "menuIdList": {"type": ["array", "null"], "items": {"type": "integer", "format": "int64"}, "description": "菜单Id集合"}}, "additionalProperties": false, "description": "授权角色菜单", "x-apifox-orders": ["id", "menuIdList"]}}}, {"name": "RoleOrgInput", "displayName": "", "id": "#/definitions/84275348", "description": "授权角色机构", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "dataScope": {"type": "integer", "description": "数据范围", "format": "int32"}, "orgIdList": {"type": ["array", "null"], "items": {"type": "integer", "format": "int64"}, "description": "机构Id集合"}}, "additionalProperties": false, "description": "授权角色机构", "x-apifox-orders": ["id", "dataScope", "orgIdList"]}}}, {"name": "RoleOutput", "displayName": "", "id": "#/definitions/84275349", "description": "角色列表输出参数", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "Id", "format": "int64"}, "name": {"type": ["string", "null"], "description": "名称"}, "code": {"type": ["string", "null"], "description": "编码"}}, "additionalProperties": false, "description": "角色列表输出参数", "x-apifox-orders": ["id", "name", "code"]}}}, {"name": "SendSubscribeMessageInput", "displayName": "", "id": "#/definitions/84275350", "description": "发送订阅消息", "schema": {"jsonSchema": {"required": ["data", "templateId", "toUserOpenId"], "type": "object", "properties": {"templateId": {"minLength": 1, "type": "string", "description": "订阅模板Id"}, "toUserOpenId": {"minLength": 1, "type": "string", "description": "接收者的OpenId"}, "data": {"type": "object", "additionalProperties": {"$ref": "#/definitions/84275268"}, "description": "模板内容，格式形如 { \"key1\": { \"value\": any }, \"key2\": { \"value\": any } }", "x-apifox-orders": []}, "miniprogramState": {"type": ["string", "null"], "description": "跳转小程序类型"}, "language": {"type": ["string", "null"], "description": "语言类型"}, "miniProgramPagePath": {"type": ["string", "null"], "description": "点击模板卡片后的跳转页面（仅限本小程序内的页面），支持带参数（示例pages/app/index?foo=bar）"}}, "additionalProperties": false, "description": "发送订阅消息", "x-apifox-orders": ["templateId", "toUserOpenId", "data", "miniprogramState", "language", "miniProgramPagePath"]}}}, {"name": "SignatureInput", "displayName": "", "id": "#/definitions/84275351", "description": "获取配置签名", "schema": {"jsonSchema": {"type": "object", "properties": {"url": {"type": ["string", "null"], "description": "Url"}}, "additionalProperties": false, "description": "获取配置签名", "x-apifox-orders": ["url"]}}}, {"name": "SmKeyPairOutput", "displayName": "", "id": "#/definitions/84275352", "description": "国密公钥私钥对输出", "schema": {"jsonSchema": {"type": "object", "properties": {"privateKey": {"type": ["string", "null"], "description": "私匙"}, "publicKey": {"type": ["string", "null"], "description": "公匙"}}, "additionalProperties": false, "description": "国密公钥私钥对输出", "x-apifox-orders": ["privateKey", "public<PERSON>ey"]}}}, {"name": "SqlSugarPagedList_JobDetailOutput", "displayName": "", "id": "#/definitions/84275353", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275310"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_OpenAccessOutput", "displayName": "", "id": "#/definitions/84275354", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275328"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_SysCodeGen", "displayName": "", "id": "#/definitions/84275355", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275376"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_SysConfig", "displayName": "", "id": "#/definitions/84275356", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275378"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_SysDictData", "displayName": "", "id": "#/definitions/84275357", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275379"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_SysDictType", "displayName": "", "id": "#/definitions/84275358", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275380"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_SysFile", "displayName": "", "id": "#/definitions/84275359", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275381"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_SysJobTriggerRecord", "displayName": "", "id": "#/definitions/84275360", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275385"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_SysLogDiff", "displayName": "", "id": "#/definitions/84275361", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275386"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_SysLogEx", "displayName": "", "id": "#/definitions/84275362", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275387"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_SysLogOp", "displayName": "", "id": "#/definitions/84275363", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/********"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_SysLogVis", "displayName": "", "id": "#/definitions/84275364", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/********"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_SysNotice", "displayName": "", "id": "#/definitions/84275365", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275392"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_SysNoticeUser", "displayName": "", "id": "#/definitions/84275366", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275393"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_SysOnlineUser", "displayName": "", "id": "#/definitions/84275367", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275394"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_SysPlugin", "displayName": "", "id": "#/definitions/84275368", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275396"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_SysPrint", "displayName": "", "id": "#/definitions/84275369", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275398"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_SysRegion", "displayName": "", "id": "#/definitions/84275370", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275399"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_SysRole", "displayName": "", "id": "#/definitions/84275371", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275400"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_SysWechatUser", "displayName": "", "id": "#/definitions/84275372", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/********"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_TenantOutput", "displayName": "", "id": "#/definitions/84275373", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275408"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "SqlSugarPagedList_UserOutput", "displayName": "", "id": "#/definitions/84275374", "description": "分页泛型集合", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页容量", "format": "int32"}, "total": {"type": "integer", "description": "总条数", "format": "int32"}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32"}, "items": {"type": ["array", "null"], "items": {"$ref": "#/definitions/********"}, "description": "当前页集合"}, "hasPrevPage": {"type": "boolean", "description": "是否有上一页"}, "hasNextPage": {"type": "boolean", "description": "是否有下一页"}}, "additionalProperties": false, "description": "分页泛型集合", "x-apifox-orders": ["page", "pageSize", "total", "totalPages", "items", "hasPrevPage", "hasNextPage"]}}}, {"name": "StatusEnum", "displayName": "", "id": "#/definitions/********", "description": "通用状态枚举<br />&nbsp;启用 Enable = 1<br />&nbsp;停用 Disable = 2<br />", "schema": {"jsonSchema": {"enum": [1, 2], "type": "integer", "description": "通用状态枚举<br />&nbsp;启用 Enable = 1<br />&nbsp;停用 Disable = 2<br />", "format": "int32"}}}, {"name": "SysCodeGen", "displayName": "", "id": "#/definitions/84275376", "description": "代码生成表", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "authorName": {"maxLength": 32, "type": ["string", "null"], "description": "作者姓名"}, "tablePrefix": {"maxLength": 8, "type": ["string", "null"], "description": "是否移除表前缀"}, "generateType": {"maxLength": 32, "type": ["string", "null"], "description": "生成方式"}, "configId": {"maxLength": 64, "type": ["string", "null"], "description": "库定位器名"}, "dbName": {"maxLength": 64, "type": ["string", "null"], "description": "数据库名(保留字段)"}, "dbType": {"maxLength": 64, "type": ["string", "null"], "description": "数据库类型"}, "connectionString": {"maxLength": 256, "type": ["string", "null"], "description": "数据库链接"}, "tableName": {"maxLength": 128, "type": ["string", "null"], "description": "数据库表名"}, "nameSpace": {"maxLength": 128, "type": ["string", "null"], "description": "命名空间"}, "busName": {"maxLength": 128, "type": ["string", "null"], "description": "业务名"}, "menuPid": {"type": "integer", "description": "菜单编码", "format": "int64"}, "printType": {"maxLength": 32, "type": ["string", "null"], "description": "支持打印类型"}, "printName": {"maxLength": 32, "type": ["string", "null"], "description": "打印模版名称"}}, "additionalProperties": false, "description": "代码生成表", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "<PERSON><PERSON><PERSON>", "tablePrefix", "generateType", "configId", "dbN<PERSON>", "dbType", "connectionString", "tableName", "nameSpace", "busName", "menuPid", "printType", "printName"]}}}, {"name": "SysCodeGenConfig", "displayName": "", "id": "#/definitions/84275377", "description": "代码生成字段配置表", "schema": {"jsonSchema": {"required": ["columnName", "propertyName"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "codeGenId": {"type": "integer", "description": "代码生成主表Id", "format": "int64"}, "columnName": {"maxLength": 128, "minLength": 1, "type": "string", "description": "数据库字段名"}, "propertyName": {"maxLength": 128, "minLength": 1, "type": "string", "description": "实体属性名"}, "columnLength": {"type": "integer", "description": "字段数据长度", "format": "int32"}, "columnComment": {"maxLength": 128, "type": ["string", "null"], "description": "字段描述"}, "netType": {"maxLength": 64, "type": ["string", "null"], "description": ".NET数据类型"}, "effectType": {"maxLength": 64, "type": ["string", "null"], "description": "作用类型（字典）"}, "fkEntityName": {"maxLength": 64, "type": ["string", "null"], "description": "外键实体名称"}, "fkTableName": {"maxLength": 128, "type": ["string", "null"], "description": "外键表名称"}, "fkColumnName": {"maxLength": 64, "type": ["string", "null"], "description": "外键显示字段"}, "fkColumnNetType": {"maxLength": 64, "type": ["string", "null"], "description": "外键显示字段.NET类型"}, "dictTypeCode": {"maxLength": 64, "type": ["string", "null"], "description": "字典编码"}, "whetherRetract": {"maxLength": 8, "type": ["string", "null"], "description": "列表是否缩进（字典）"}, "whetherRequired": {"maxLength": 8, "type": ["string", "null"], "description": "是否必填（字典）"}, "whetherSortable": {"maxLength": 8, "type": ["string", "null"], "description": "是否可排序（字典）"}, "queryWhether": {"maxLength": 8, "type": ["string", "null"], "description": "是否是查询条件"}, "queryType": {"maxLength": 16, "type": ["string", "null"], "description": "查询方式"}, "whetherTable": {"maxLength": 8, "type": ["string", "null"], "description": "列表显示"}, "whetherAddUpdate": {"maxLength": 8, "type": ["string", "null"], "description": "增改"}, "columnKey": {"maxLength": 8, "type": ["string", "null"], "description": "主键"}, "dataType": {"maxLength": 64, "type": ["string", "null"], "description": "数据库中类型（物理类型）"}, "whetherCommon": {"maxLength": 8, "type": ["string", "null"], "description": "是否通用字段"}, "displayColumn": {"type": ["string", "null"], "description": "显示文本字段"}, "valueColumn": {"maxLength": 128, "type": ["string", "null"], "description": "选中值字段"}, "pidColumn": {"maxLength": 128, "type": ["string", "null"], "description": "父级字段"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}}, "additionalProperties": false, "description": "代码生成字段配置表", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "codeGenId", "columnName", "propertyName", "columnLength", "columnComment", "netType", "effectType", "fkEntityName", "fkTableName", "fkColumnName", "fkColumnNetType", "dictTypeCode", "whetherRetract", "whetherRequired", "whetherSortable", "query<PERSON><PERSON><PERSON>", "queryType", "whetherTable", "whetherAddUpdate", "column<PERSON>ey", "dataType", "<PERSON><PERSON><PERSON><PERSON>", "displayColumn", "valueColumn", "pidColumn", "orderNo"]}}}, {"name": "SysConfig", "displayName": "", "id": "#/definitions/84275378", "description": "系统参数配置表", "schema": {"jsonSchema": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "name": {"maxLength": 64, "minLength": 1, "type": "string", "description": "名称"}, "code": {"maxLength": 64, "type": ["string", "null"], "description": "编码"}, "value": {"maxLength": 64, "type": ["string", "null"], "description": "属性值"}, "sysFlag": {"$ref": "#/definitions/84275444"}, "groupCode": {"maxLength": 64, "type": ["string", "null"], "description": "分组编码"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "remark": {"maxLength": 256, "type": ["string", "null"], "description": "备注"}}, "additionalProperties": false, "description": "系统参数配置表", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "name", "code", "value", "sysFlag", "groupCode", "orderNo", "remark"]}}}, {"name": "SysDictData", "displayName": "", "id": "#/definitions/84275379", "description": "系统字典值表", "schema": {"jsonSchema": {"required": ["code", "value"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "dictTypeId": {"type": "integer", "description": "字典类型Id", "format": "int64"}, "value": {"maxLength": 128, "minLength": 1, "type": "string", "description": "值"}, "code": {"maxLength": 64, "minLength": 1, "type": "string", "description": "编码"}, "tagType": {"maxLength": 16, "type": ["string", "null"], "description": "显示样式-标签颜色"}, "styleSetting": {"maxLength": 512, "type": ["string", "null"], "description": "显示样式-Style(控制显示样式)"}, "classSetting": {"maxLength": 512, "type": ["string", "null"], "description": "显示样式-Class(控制显示样式)"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "remark": {"maxLength": 2048, "type": ["string", "null"], "description": "备注"}, "extData": {"type": ["string", "null"], "description": "拓展数据(保存业务功能的配置项)"}, "status": {"$ref": "#/definitions/********"}}, "additionalProperties": false, "description": "系统字典值表", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "dictTypeId", "value", "code", "tagType", "styleSetting", "classSetting", "orderNo", "remark", "extData", "status"]}}}, {"name": "SysDictType", "displayName": "", "id": "#/definitions/84275380", "description": "系统字典类型表", "schema": {"jsonSchema": {"required": ["code", "name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "name": {"maxLength": 64, "minLength": 1, "type": "string", "description": "名称"}, "code": {"maxLength": 64, "minLength": 1, "type": "string", "description": "编码"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "remark": {"maxLength": 256, "type": ["string", "null"], "description": "备注"}, "status": {"$ref": "#/definitions/********"}, "children": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275379"}, "description": "字典值集合"}}, "additionalProperties": false, "description": "系统字典类型表", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "name", "code", "orderNo", "remark", "status", "children"]}}}, {"name": "SysFile", "displayName": "", "id": "#/definitions/84275381", "description": "系统文件表", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "provider": {"maxLength": 128, "type": ["string", "null"], "description": "提供者"}, "bucketName": {"maxLength": 128, "type": ["string", "null"], "description": "仓储名称"}, "fileName": {"maxLength": 128, "type": ["string", "null"], "description": "文件名称（源文件名）"}, "suffix": {"maxLength": 16, "type": ["string", "null"], "description": "文件后缀"}, "filePath": {"maxLength": 128, "type": ["string", "null"], "description": "存储路径"}, "sizeKb": {"maxLength": 16, "type": ["string", "null"], "description": "文件大小KB"}, "sizeInfo": {"maxLength": 64, "type": ["string", "null"], "description": "文件大小信息-计算后的"}, "url": {"maxLength": 512, "type": ["string", "null"], "description": "外链地址-OSS上传后生成外链地址方便前端预览"}, "fileMd5": {"maxLength": 128, "type": ["string", "null"], "description": "文件MD5"}}, "additionalProperties": false, "description": "系统文件表", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "provider", "bucketName", "fileName", "suffix", "filePath", "sizeKb", "sizeInfo", "url", "fileMd5"]}}}, {"name": "SysJobCluster", "displayName": "", "id": "#/definitions/84275382", "description": "系统作业集群表", "schema": {"jsonSchema": {"required": ["clusterId"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "clusterId": {"maxLength": 64, "minLength": 1, "type": "string", "description": "作业集群Id"}, "description": {"maxLength": 128, "type": ["string", "null"], "description": "描述信息"}, "status": {"$ref": "#/definitions/84275260"}, "updatedTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}}, "additionalProperties": false, "description": "系统作业集群表", "x-apifox-orders": ["id", "clusterId", "description", "status", "updatedTime"]}}}, {"name": "SysJobDetail", "displayName": "", "id": "#/definitions/84275383", "description": "系统作业信息表", "schema": {"jsonSchema": {"required": ["jobId"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "jobId": {"maxLength": 64, "minLength": 1, "type": "string", "description": "作业Id"}, "groupName": {"maxLength": 128, "type": ["string", "null"], "description": "组名称"}, "jobType": {"maxLength": 128, "type": ["string", "null"], "description": "作业类型FullName"}, "assemblyName": {"maxLength": 128, "type": ["string", "null"], "description": "程序集Name"}, "description": {"maxLength": 128, "type": ["string", "null"], "description": "描述信息"}, "concurrent": {"type": "boolean", "description": "是否并行执行"}, "includeAnnotations": {"type": "boolean", "description": "是否扫描特性触发器"}, "properties": {"type": ["string", "null"], "description": "额外数据"}, "updatedTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createType": {"$ref": "#/definitions/84275308"}, "scriptCode": {"type": ["string", "null"], "description": "脚本代码"}}, "additionalProperties": false, "description": "系统作业信息表", "x-apifox-orders": ["id", "jobId", "groupName", "jobType", "assemblyName", "description", "concurrent", "includeAnnotations", "properties", "updatedTime", "createType", "scriptCode"]}}}, {"name": "SysJobTrigger", "displayName": "", "id": "#/definitions/84275384", "description": "系统作业触发器表", "schema": {"jsonSchema": {"required": ["jobId", "triggerId"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "triggerId": {"maxLength": 64, "minLength": 1, "type": "string", "description": "触发器Id"}, "jobId": {"maxLength": 64, "minLength": 1, "type": "string", "description": "作业Id"}, "triggerType": {"maxLength": 128, "type": ["string", "null"], "description": "触发器类型FullName"}, "assemblyName": {"maxLength": 128, "type": ["string", "null"], "description": "程序集Name"}, "args": {"maxLength": 128, "type": ["string", "null"], "description": "参数"}, "description": {"maxLength": 128, "type": ["string", "null"], "description": "描述信息"}, "status": {"$ref": "#/definitions/********"}, "startTime": {"type": ["string", "null"], "description": "起始时间", "format": "date-time"}, "endTime": {"type": ["string", "null"], "description": "结束时间", "format": "date-time"}, "lastRunTime": {"type": ["string", "null"], "description": "最近运行时间", "format": "date-time"}, "nextRunTime": {"type": ["string", "null"], "description": "下一次运行时间", "format": "date-time"}, "numberOfRuns": {"type": "integer", "description": "触发次数", "format": "int64"}, "maxNumberOfRuns": {"type": "integer", "description": "最大触发次数（0:不限制，n:N次）", "format": "int64"}, "numberOfErrors": {"type": "integer", "description": "出错次数", "format": "int64"}, "maxNumberOfErrors": {"type": "integer", "description": "最大出错次数（0:不限制，n:N次）", "format": "int64"}, "numRetries": {"type": "integer", "description": "重试次数", "format": "int32"}, "retryTimeout": {"type": "integer", "description": "重试间隔时间（ms）", "format": "int32"}, "startNow": {"type": "boolean", "description": "是否立即启动"}, "runOnStart": {"type": "boolean", "description": "是否启动时执行一次"}, "resetOnlyOnce": {"type": "boolean", "description": "是否在启动时重置最大触发次数等于一次的作业"}, "updatedTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}}, "additionalProperties": false, "description": "系统作业触发器表", "x-apifox-orders": ["id", "triggerId", "jobId", "triggerType", "assemblyName", "args", "description", "status", "startTime", "endTime", "lastRunTime", "nextRunTime", "numberOfRuns", "maxNumberOfRuns", "numberOfErrors", "maxNumberOfErrors", "numRetries", "retryTimeout", "startNow", "runOnStart", "resetOnlyOnce", "updatedTime"]}}}, {"name": "SysJobTriggerRecord", "displayName": "", "id": "#/definitions/84275385", "description": "系统作业触发器运行记录表", "schema": {"jsonSchema": {"required": ["jobId", "triggerId"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "jobId": {"maxLength": 64, "minLength": 1, "type": "string", "description": "作业Id"}, "triggerId": {"maxLength": 64, "minLength": 1, "type": "string", "description": "触发器Id"}, "numberOfRuns": {"type": "integer", "description": "当前运行次数", "format": "int64"}, "lastRunTime": {"type": ["string", "null"], "description": "最近运行时间", "format": "date-time"}, "nextRunTime": {"type": ["string", "null"], "description": "下一次运行时间", "format": "date-time"}, "status": {"$ref": "#/definitions/********"}, "result": {"maxLength": 128, "type": ["string", "null"], "description": "本次执行结果"}, "elapsedTime": {"type": "integer", "description": "本次执行耗时", "format": "int64"}, "createdTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}}, "additionalProperties": false, "description": "系统作业触发器运行记录表", "x-apifox-orders": ["id", "jobId", "triggerId", "numberOfRuns", "lastRunTime", "nextRunTime", "status", "result", "elapsedTime", "createdTime"]}}}, {"name": "SysLogDiff", "displayName": "", "id": "#/definitions/84275386", "description": "系统差异日志表", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "beforeData": {"type": ["string", "null"], "description": "操作前记录"}, "afterData": {"type": ["string", "null"], "description": "操作后记录"}, "sql": {"type": ["string", "null"], "description": "Sql"}, "parameters": {"type": ["string", "null"], "description": "参数  手动传入的参数"}, "businessData": {"type": ["string", "null"], "description": "业务对象"}, "diffType": {"type": ["string", "null"], "description": "差异操作"}, "elapsed": {"type": ["integer", "null"], "description": "耗时", "format": "int64"}}, "additionalProperties": false, "description": "系统差异日志表", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "beforeData", "afterData", "sql", "parameters", "businessData", "diffType", "elapsed"]}}}, {"name": "SysLogEx", "displayName": "", "id": "#/definitions/84275387", "description": "系统异常日志表", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "controllerName": {"maxLength": 256, "type": ["string", "null"], "description": "模块名称"}, "actionName": {"maxLength": 256, "type": ["string", "null"], "description": "方法名称"}, "displayTitle": {"maxLength": 256, "type": ["string", "null"], "description": "显示名称"}, "status": {"maxLength": 32, "type": ["string", "null"], "description": "执行状态"}, "remoteIp": {"maxLength": 256, "type": ["string", "null"], "description": "IP地址"}, "location": {"maxLength": 128, "type": ["string", "null"], "description": "登录地点"}, "longitude": {"type": ["number", "null"], "description": "经度", "format": "double"}, "latitude": {"type": ["number", "null"], "description": "维度", "format": "double"}, "browser": {"maxLength": 1024, "type": ["string", "null"], "description": "浏览器"}, "os": {"maxLength": 256, "type": ["string", "null"], "description": "操作系统"}, "elapsed": {"type": ["integer", "null"], "description": "操作用时", "format": "int64"}, "logDateTime": {"type": ["string", "null"], "description": "日志时间", "format": "date-time"}, "logLevel": {"$ref": "#/definitions/********"}, "account": {"maxLength": 32, "type": ["string", "null"], "description": "账号"}, "realName": {"maxLength": 32, "type": ["string", "null"], "description": "真实姓名"}, "httpMethod": {"maxLength": 32, "type": ["string", "null"], "description": "请求方式"}, "requestUrl": {"type": ["string", "null"], "description": "请求地址"}, "requestParam": {"type": ["string", "null"], "description": "请求参数"}, "returnResult": {"type": ["string", "null"], "description": "返回结果"}, "eventId": {"type": ["integer", "null"], "description": "事件Id", "format": "int32"}, "threadId": {"type": ["integer", "null"], "description": "线程Id", "format": "int32"}, "traceId": {"maxLength": 128, "type": ["string", "null"], "description": "请求跟踪Id"}, "exception": {"type": ["string", "null"], "description": "异常信息"}, "message": {"type": ["string", "null"], "description": "日志消息Json"}}, "additionalProperties": false, "description": "系统异常日志表", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "controllerName", "actionName", "displayTitle", "status", "remoteIp", "location", "longitude", "latitude", "browser", "os", "elapsed", "logDateTime", "logLevel", "account", "realName", "httpMethod", "requestUrl", "requestParam", "returnResult", "eventId", "threadId", "traceId", "exception", "message"]}}}, {"name": "SysLogOp", "displayName": "", "id": "#/definitions/********", "description": "系统操作日志表", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "controllerName": {"maxLength": 256, "type": ["string", "null"], "description": "模块名称"}, "actionName": {"maxLength": 256, "type": ["string", "null"], "description": "方法名称"}, "displayTitle": {"maxLength": 256, "type": ["string", "null"], "description": "显示名称"}, "status": {"maxLength": 32, "type": ["string", "null"], "description": "执行状态"}, "remoteIp": {"maxLength": 256, "type": ["string", "null"], "description": "IP地址"}, "location": {"maxLength": 128, "type": ["string", "null"], "description": "登录地点"}, "longitude": {"type": ["number", "null"], "description": "经度", "format": "double"}, "latitude": {"type": ["number", "null"], "description": "维度", "format": "double"}, "browser": {"maxLength": 1024, "type": ["string", "null"], "description": "浏览器"}, "os": {"maxLength": 256, "type": ["string", "null"], "description": "操作系统"}, "elapsed": {"type": ["integer", "null"], "description": "操作用时", "format": "int64"}, "logDateTime": {"type": ["string", "null"], "description": "日志时间", "format": "date-time"}, "logLevel": {"$ref": "#/definitions/********"}, "account": {"maxLength": 32, "type": ["string", "null"], "description": "账号"}, "realName": {"maxLength": 32, "type": ["string", "null"], "description": "真实姓名"}, "httpMethod": {"maxLength": 32, "type": ["string", "null"], "description": "请求方式"}, "requestUrl": {"type": ["string", "null"], "description": "请求地址"}, "requestParam": {"type": ["string", "null"], "description": "请求参数"}, "returnResult": {"type": ["string", "null"], "description": "返回结果"}, "eventId": {"type": ["integer", "null"], "description": "事件Id", "format": "int32"}, "threadId": {"type": ["integer", "null"], "description": "线程Id", "format": "int32"}, "traceId": {"maxLength": 128, "type": ["string", "null"], "description": "请求跟踪Id"}, "exception": {"type": ["string", "null"], "description": "异常信息"}, "message": {"type": ["string", "null"], "description": "日志消息Json"}}, "additionalProperties": false, "description": "系统操作日志表", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "controllerName", "actionName", "displayTitle", "status", "remoteIp", "location", "longitude", "latitude", "browser", "os", "elapsed", "logDateTime", "logLevel", "account", "realName", "httpMethod", "requestUrl", "requestParam", "returnResult", "eventId", "threadId", "traceId", "exception", "message"]}}}, {"name": "SysLogVis", "displayName": "", "id": "#/definitions/********", "description": "系统访问日志表", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "controllerName": {"maxLength": 256, "type": ["string", "null"], "description": "模块名称"}, "actionName": {"maxLength": 256, "type": ["string", "null"], "description": "方法名称"}, "displayTitle": {"maxLength": 256, "type": ["string", "null"], "description": "显示名称"}, "status": {"maxLength": 32, "type": ["string", "null"], "description": "执行状态"}, "remoteIp": {"maxLength": 256, "type": ["string", "null"], "description": "IP地址"}, "location": {"maxLength": 128, "type": ["string", "null"], "description": "登录地点"}, "longitude": {"type": ["number", "null"], "description": "经度", "format": "double"}, "latitude": {"type": ["number", "null"], "description": "维度", "format": "double"}, "browser": {"maxLength": 1024, "type": ["string", "null"], "description": "浏览器"}, "os": {"maxLength": 256, "type": ["string", "null"], "description": "操作系统"}, "elapsed": {"type": ["integer", "null"], "description": "操作用时", "format": "int64"}, "logDateTime": {"type": ["string", "null"], "description": "日志时间", "format": "date-time"}, "logLevel": {"$ref": "#/definitions/********"}, "account": {"maxLength": 32, "type": ["string", "null"], "description": "账号"}, "realName": {"maxLength": 32, "type": ["string", "null"], "description": "真实姓名"}}, "additionalProperties": false, "description": "系统访问日志表", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "controllerName", "actionName", "displayTitle", "status", "remoteIp", "location", "longitude", "latitude", "browser", "os", "elapsed", "logDateTime", "logLevel", "account", "realName"]}}}, {"name": "SysMenu", "displayName": "", "id": "#/definitions/********", "description": "系统菜单表", "schema": {"jsonSchema": {"required": ["title"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "pid": {"type": "integer", "description": "父Id", "format": "int64"}, "type": {"$ref": "#/definitions/********"}, "name": {"maxLength": 64, "type": ["string", "null"], "description": "路由名称"}, "path": {"maxLength": 128, "type": ["string", "null"], "description": "路由地址"}, "component": {"maxLength": 128, "type": ["string", "null"], "description": "组件路径"}, "redirect": {"maxLength": 128, "type": ["string", "null"], "description": "重定向"}, "permission": {"maxLength": 128, "type": ["string", "null"], "description": "权限标识"}, "title": {"maxLength": 64, "minLength": 1, "type": "string", "description": "菜单名称"}, "icon": {"maxLength": 128, "type": ["string", "null"], "description": "图标"}, "isIframe": {"type": "boolean", "description": "是否内嵌"}, "outLink": {"maxLength": 256, "type": ["string", "null"], "description": "外链链接"}, "isHide": {"type": "boolean", "description": "是否隐藏"}, "isKeepAlive": {"type": "boolean", "description": "是否缓存"}, "isAffix": {"type": "boolean", "description": "是否固定"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "status": {"$ref": "#/definitions/********"}, "remark": {"maxLength": 256, "type": ["string", "null"], "description": "备注"}, "children": {"type": ["array", "null"], "items": {"$ref": "#/definitions/********"}, "description": "菜单子项"}}, "additionalProperties": false, "description": "系统菜单表", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "pid", "type", "name", "path", "component", "redirect", "permission", "title", "icon", "isIframe", "outLink", "isHide", "isKeepAlive", "isAffix", "orderNo", "status", "remark", "children"]}}}, {"name": "SysMenuMeta", "displayName": "", "id": "#/definitions/84275391", "description": "菜单Meta配置", "schema": {"jsonSchema": {"type": "object", "properties": {"title": {"type": ["string", "null"], "description": "标题"}, "icon": {"type": ["string", "null"], "description": "图标"}, "isIframe": {"type": "boolean", "description": "是否内嵌"}, "isLink": {"type": ["string", "null"], "description": "外链链接"}, "isHide": {"type": "boolean", "description": "是否隐藏"}, "isKeepAlive": {"type": "boolean", "description": "是否缓存"}, "isAffix": {"type": "boolean", "description": "是否固定"}}, "additionalProperties": false, "description": "菜单Meta配置", "x-apifox-orders": ["title", "icon", "isIframe", "isLink", "isHide", "isKeepAlive", "isAffix"]}}}, {"name": "SysNotice", "displayName": "", "id": "#/definitions/84275392", "description": "系统通知公告表", "schema": {"jsonSchema": {"required": ["content", "title"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "title": {"maxLength": 32, "minLength": 1, "type": "string", "description": "标题"}, "content": {"minLength": 1, "type": "string", "description": "内容"}, "type": {"$ref": "#/definitions/84275325"}, "publicUserId": {"type": "integer", "description": "发布人Id", "format": "int64"}, "publicUserName": {"maxLength": 32, "type": ["string", "null"], "description": "发布人姓名"}, "publicOrgId": {"type": "integer", "description": "发布机构Id", "format": "int64"}, "publicOrgName": {"maxLength": 64, "type": ["string", "null"], "description": "发布机构名称"}, "publicTime": {"type": ["string", "null"], "description": "发布时间", "format": "date-time"}, "cancelTime": {"type": ["string", "null"], "description": "撤回时间", "format": "date-time"}, "status": {"$ref": "#/definitions/84275324"}}, "additionalProperties": false, "description": "系统通知公告表", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "title", "content", "type", "publicUserId", "publicUserName", "publicOrgId", "publicOrgName", "publicTime", "cancelTime", "status"]}}}, {"name": "SysNoticeUser", "displayName": "", "id": "#/definitions/84275393", "description": "系统通知公告用户表", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "noticeId": {"type": "integer", "description": "通知公告Id", "format": "int64"}, "sysNotice": {"$ref": "#/definitions/84275392"}, "userId": {"type": "integer", "description": "用户Id", "format": "int64"}, "readTime": {"type": ["string", "null"], "description": "阅读时间", "format": "date-time"}, "readStatus": {"$ref": "#/definitions/84275326"}}, "additionalProperties": false, "description": "系统通知公告用户表", "x-apifox-orders": ["id", "noticeId", "sysNotice", "userId", "readTime", "readStatus"]}}}, {"name": "SysOnlineUser", "displayName": "", "id": "#/definitions/84275394", "description": "系统在线用户表", "schema": {"jsonSchema": {"required": ["userName"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "connectionId": {"type": ["string", "null"], "description": "连接Id"}, "userId": {"type": "integer", "description": "用户Id", "format": "int64"}, "userName": {"maxLength": 32, "minLength": 1, "type": "string", "description": "账号"}, "realName": {"maxLength": 32, "type": ["string", "null"], "description": "真实姓名"}, "time": {"type": ["string", "null"], "description": "连接时间", "format": "date-time"}, "ip": {"maxLength": 256, "type": ["string", "null"], "description": "连接IP"}, "browser": {"maxLength": 128, "type": ["string", "null"], "description": "浏览器"}, "os": {"maxLength": 128, "type": ["string", "null"], "description": "操作系统"}}, "additionalProperties": false, "description": "系统在线用户表", "x-apifox-orders": ["id", "tenantId", "connectionId", "userId", "userName", "realName", "time", "ip", "browser", "os"]}}}, {"name": "SysOrg", "displayName": "", "id": "#/definitions/********", "description": "系统机构表", "schema": {"jsonSchema": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "pid": {"type": "integer", "description": "父Id", "format": "int64"}, "name": {"maxLength": 64, "minLength": 1, "type": "string", "description": "名称"}, "code": {"maxLength": 64, "type": ["string", "null"], "description": "编码"}, "level": {"type": ["integer", "null"], "description": "级别", "format": "int32"}, "type": {"maxLength": 64, "type": ["string", "null"], "description": "机构类型-数据字典"}, "directorId": {"type": ["integer", "null"], "description": "负责人Id", "format": "int64"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "status": {"$ref": "#/definitions/********"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}, "children": {"type": ["array", "null"], "items": {"$ref": "#/definitions/********"}, "description": "机构子项"}, "disabled": {"type": "boolean", "description": "是否禁止选中"}}, "additionalProperties": false, "description": "系统机构表", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "pid", "name", "code", "level", "type", "directorId", "orderNo", "status", "remark", "children", "disabled"]}}}, {"name": "SysPlugin", "displayName": "", "id": "#/definitions/84275396", "description": "系统动态插件表", "schema": {"jsonSchema": {"required": ["csharpCode", "name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "name": {"maxLength": 64, "minLength": 1, "type": "string", "description": "名称"}, "csharpCode": {"minLength": 1, "type": "string", "description": "C#代码"}, "assemblyName": {"maxLength": 512, "type": ["string", "null"], "description": "程序集名称"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "status": {"$ref": "#/definitions/********"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}}, "additionalProperties": false, "description": "系统动态插件表", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "name", "csharpCode", "assemblyName", "orderNo", "status", "remark"]}}}, {"name": "SysPos", "displayName": "", "id": "#/definitions/84275397", "description": "系统职位表", "schema": {"jsonSchema": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "name": {"maxLength": 64, "minLength": 1, "type": "string", "description": "名称"}, "code": {"maxLength": 64, "type": ["string", "null"], "description": "编码"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}, "status": {"$ref": "#/definitions/********"}}, "additionalProperties": false, "description": "系统职位表", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "name", "code", "orderNo", "remark", "status"]}}}, {"name": "SysPrint", "displayName": "", "id": "#/definitions/84275398", "description": "系统打印模板表", "schema": {"jsonSchema": {"required": ["name", "template"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "name": {"maxLength": 64, "minLength": 1, "type": "string", "description": "名称"}, "template": {"minLength": 1, "type": "string", "description": "打印模板"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "status": {"$ref": "#/definitions/********"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}}, "additionalProperties": false, "description": "系统打印模板表", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "name", "template", "orderNo", "status", "remark"]}}}, {"name": "SysRegion", "displayName": "", "id": "#/definitions/84275399", "description": "系统行政地区表", "schema": {"jsonSchema": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "pid": {"type": "integer", "description": "父Id", "format": "int64"}, "name": {"maxLength": 128, "minLength": 1, "type": "string", "description": "名称"}, "shortName": {"maxLength": 32, "type": ["string", "null"], "description": "简称"}, "mergerName": {"maxLength": 64, "type": ["string", "null"], "description": "组合名"}, "code": {"maxLength": 32, "type": ["string", "null"], "description": "行政代码"}, "zipCode": {"maxLength": 6, "type": ["string", "null"], "description": "邮政编码"}, "cityCode": {"maxLength": 6, "type": ["string", "null"], "description": "区号"}, "level": {"type": "integer", "description": "层级", "format": "int32"}, "pinYin": {"maxLength": 128, "type": ["string", "null"], "description": "拼音"}, "lng": {"type": "number", "description": "经度", "format": "float"}, "lat": {"type": "number", "description": "维度", "format": "float"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}, "children": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275399"}, "description": "机构子项"}}, "additionalProperties": false, "description": "系统行政地区表", "x-apifox-orders": ["id", "pid", "name", "shortName", "mergerName", "code", "zipCode", "cityCode", "level", "pinYin", "lng", "lat", "orderNo", "remark", "children"]}}}, {"name": "SysRole", "displayName": "", "id": "#/definitions/84275400", "description": "系统角色表", "schema": {"jsonSchema": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "name": {"maxLength": 64, "minLength": 1, "type": "string", "description": "名称"}, "code": {"maxLength": 64, "type": ["string", "null"], "description": "编码"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "dataScope": {"$ref": "#/definitions/84275269"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}, "status": {"$ref": "#/definitions/********"}}, "additionalProperties": false, "description": "系统角色表", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "name", "code", "orderNo", "dataScope", "remark", "status"]}}}, {"name": "SysUser", "displayName": "", "id": "#/definitions/********", "description": "系统用户表", "schema": {"jsonSchema": {"required": ["account"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "account": {"maxLength": 32, "minLength": 1, "type": "string", "description": "账号"}, "realName": {"maxLength": 32, "type": ["string", "null"], "description": "真实姓名"}, "nickName": {"maxLength": 32, "type": ["string", "null"], "description": "昵称"}, "avatar": {"maxLength": 512, "type": ["string", "null"], "description": "头像"}, "sex": {"$ref": "#/definitions/********"}, "age": {"type": "integer", "description": "年龄", "format": "int32"}, "birthday": {"type": ["string", "null"], "description": "出生日期", "format": "date-time"}, "nation": {"maxLength": 32, "type": ["string", "null"], "description": "民族"}, "phone": {"maxLength": 16, "type": ["string", "null"], "description": "手机号码"}, "cardType": {"$ref": "#/definitions/********"}, "idCardNum": {"maxLength": 32, "type": ["string", "null"], "description": "身份证号"}, "email": {"maxLength": 64, "type": ["string", "null"], "description": "邮箱"}, "address": {"maxLength": 256, "type": ["string", "null"], "description": "地址"}, "cultureLevel": {"$ref": "#/definitions/84275267"}, "politicalOutlook": {"maxLength": 16, "type": ["string", "null"], "description": "政治面貌"}, "college": {"maxLength": 128, "type": ["string", "null"], "description": "毕业院校"}, "officePhone": {"maxLength": 16, "type": ["string", "null"], "description": "办公电话"}, "emergencyContact": {"maxLength": 32, "type": ["string", "null"], "description": "紧急联系人"}, "emergencyPhone": {"maxLength": 16, "type": ["string", "null"], "description": "紧急联系人电话"}, "emergencyAddress": {"maxLength": 256, "type": ["string", "null"], "description": "紧急联系人地址"}, "introduction": {"maxLength": 512, "type": ["string", "null"], "description": "个人简介"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "status": {"$ref": "#/definitions/********"}, "remark": {"maxLength": 256, "type": ["string", "null"], "description": "备注"}, "accountType": {"$ref": "#/definitions/********"}, "orgId": {"type": "integer", "description": "直属机构Id", "format": "int64"}, "sysOrg": {"$ref": "#/definitions/********"}, "managerUserId": {"type": ["integer", "null"], "description": "直属主管Id", "format": "int64"}, "posId": {"type": "integer", "description": "职位Id", "format": "int64"}, "jobNum": {"maxLength": 32, "type": ["string", "null"], "description": "工号"}, "posLevel": {"maxLength": 32, "type": ["string", "null"], "description": "职级"}, "posTitle": {"maxLength": 32, "type": ["string", "null"], "description": "职称"}, "expertise": {"maxLength": 32, "type": ["string", "null"], "description": "擅长领域"}, "officeZone": {"maxLength": 32, "type": ["string", "null"], "description": "办公区域"}, "office": {"maxLength": 32, "type": ["string", "null"], "description": "办公室"}, "joinDate": {"type": ["string", "null"], "description": "入职日期", "format": "date-time"}, "lastLoginIp": {"maxLength": 256, "type": ["string", "null"], "description": "最新登录Ip"}, "lastLoginAddress": {"maxLength": 128, "type": ["string", "null"], "description": "最新登录地点"}, "lastLoginTime": {"type": ["string", "null"], "description": "最新登录时间", "format": "date-time"}, "lastLoginDevice": {"maxLength": 128, "type": ["string", "null"], "description": "最新登录设备"}, "signature": {"maxLength": 512, "type": ["string", "null"], "description": "电子签名"}}, "additionalProperties": false, "description": "系统用户表", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "account", "realName", "nick<PERSON><PERSON>", "avatar", "sex", "age", "birthday", "nation", "phone", "cardType", "idCardNum", "email", "address", "cultureLevel", "politicalOutlook", "college", "officePhone", "emergencyContact", "emergencyPhone", "emergencyAddress", "introduction", "orderNo", "status", "remark", "accountType", "orgId", "sysOrg", "managerUserId", "posId", "job<PERSON>um", "posLevel", "posTitle", "expertise", "officeZone", "office", "joinDate", "lastLoginIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lastLoginTime", "lastLoginDevice", "signature"]}}}, {"name": "SysUserExtOrg", "displayName": "", "id": "#/definitions/********", "description": "系统用户扩展机构表", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "userId": {"type": "integer", "description": "用户Id", "format": "int64"}, "orgId": {"type": "integer", "description": "机构Id", "format": "int64"}, "posId": {"type": "integer", "description": "职位Id", "format": "int64"}, "jobNum": {"maxLength": 32, "type": ["string", "null"], "description": "工号"}, "posLevel": {"maxLength": 32, "type": ["string", "null"], "description": "职级"}, "joinDate": {"type": ["string", "null"], "description": "入职日期", "format": "date-time"}}, "additionalProperties": false, "description": "系统用户扩展机构表", "x-apifox-orders": ["id", "userId", "orgId", "posId", "job<PERSON>um", "posLevel", "joinDate"]}}}, {"name": "SysWechatPay", "displayName": "", "id": "#/definitions/84275403", "description": "系统微信支付表", "schema": {"jsonSchema": {"required": ["appId", "merchantId", "outTradeNumber", "transactionId"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "merchantId": {"minLength": 1, "type": "string", "description": "微信商户号"}, "appId": {"minLength": 1, "type": "string", "description": "服务商AppId"}, "outTradeNumber": {"minLength": 1, "type": "string", "description": "商户订单号"}, "transactionId": {"minLength": 1, "type": "string", "description": "支付订单号"}, "tradeType": {"type": ["string", "null"], "description": "交易类型"}, "tradeState": {"type": ["string", "null"], "description": "交易状态"}, "tradeStateDescription": {"type": ["string", "null"], "description": "交易状态描述"}, "bankType": {"type": ["string", "null"], "description": "付款银行类型"}, "total": {"type": "integer", "description": "订单总金额", "format": "int32"}, "payerTotal": {"type": ["integer", "null"], "description": "用户支付金额", "format": "int32"}, "successTime": {"type": ["string", "null"], "description": "支付完成时间", "format": "date-time"}, "expireTime": {"type": ["string", "null"], "description": "交易结束时间", "format": "date-time"}, "description": {"type": ["string", "null"], "description": "商品描述"}, "scene": {"type": ["string", "null"], "description": "场景信息"}, "attachment": {"type": ["string", "null"], "description": "附加数据"}, "goodsTag": {"type": ["string", "null"], "description": "优惠标记"}, "settlement": {"type": ["string", "null"], "description": "结算信息"}, "notifyUrl": {"type": ["string", "null"], "description": "回调通知地址"}, "remark": {"type": ["string", "null"], "description": "备注"}, "openId": {"type": ["string", "null"], "description": "微信OpenId标识"}, "subMerchantId": {"type": ["string", "null"], "description": "子商户号"}, "subAppId": {"type": ["string", "null"], "description": "子商户AppId"}, "subOpenId": {"type": ["string", "null"], "description": "子商户唯一标识"}}, "additionalProperties": false, "description": "系统微信支付表", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "merchantId", "appId", "outTradeNumber", "transactionId", "tradeType", "tradeState", "tradeStateDescription", "bankType", "total", "payerTotal", "successTime", "expireTime", "description", "scene", "attachment", "goodsTag", "settlement", "notifyUrl", "remark", "openId", "subMerchantId", "subAppId", "subOpenId"]}}}, {"name": "SysWechatUser", "displayName": "", "id": "#/definitions/********", "description": "系统微信用户表", "schema": {"jsonSchema": {"required": ["openId"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "userId": {"type": "integer", "description": "系统用户Id", "format": "int64"}, "platformType": {"$ref": "#/definitions/********"}, "openId": {"maxLength": 64, "minLength": 1, "type": "string", "description": "OpenId"}, "sessionKey": {"maxLength": 256, "type": ["string", "null"], "description": "会话密钥"}, "unionId": {"maxLength": 64, "type": ["string", "null"], "description": "UnionId"}, "nickName": {"maxLength": 64, "type": ["string", "null"], "description": "昵称"}, "avatar": {"maxLength": 256, "type": ["string", "null"], "description": "头像"}, "mobile": {"maxLength": 16, "type": ["string", "null"], "description": "手机号码"}, "sex": {"type": ["integer", "null"], "description": "性别", "format": "int32"}, "language": {"maxLength": 64, "type": ["string", "null"], "description": "语言"}, "city": {"maxLength": 64, "type": ["string", "null"], "description": "城市"}, "province": {"maxLength": 64, "type": ["string", "null"], "description": "省"}, "country": {"maxLength": 64, "type": ["string", "null"], "description": "国家"}, "accessToken": {"type": ["string", "null"], "description": "AccessToken"}, "refreshToken": {"type": ["string", "null"], "description": "RefreshToken"}, "expiresIn": {"type": ["integer", "null"], "description": "过期时间", "format": "int32"}, "scope": {"maxLength": 64, "type": ["string", "null"], "description": "用户授权的作用域，使用逗号分隔"}}, "additionalProperties": false, "description": "系统微信用户表", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "userId", "platformType", "openId", "<PERSON><PERSON><PERSON>", "unionId", "nick<PERSON><PERSON>", "avatar", "mobile", "sex", "language", "city", "province", "country", "accessToken", "refreshToken", "expiresIn", "scope"]}}}, {"name": "TableOutput", "displayName": "", "id": "#/definitions/84275405", "description": "数据库表", "schema": {"jsonSchema": {"type": "object", "properties": {"configId": {"type": ["string", "null"], "description": "库定位器名"}, "tableName": {"type": ["string", "null"], "description": "表名（字母形式的）"}, "entityName": {"type": ["string", "null"], "description": "实体名称"}, "createTime": {"type": ["string", "null"], "description": "创建时间"}, "updateTime": {"type": ["string", "null"], "description": "更新时间"}, "tableComment": {"type": ["string", "null"], "description": "表名称描述（功能名）"}}, "additionalProperties": false, "description": "数据库表", "x-apifox-orders": ["configId", "tableName", "entityName", "createTime", "updateTime", "tableComment"]}}}, {"name": "TenantIdInput", "displayName": "", "id": "#/definitions/84275406", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"tenantId": {"type": "integer", "description": "租户Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["tenantId"]}}}, {"name": "TenantInput", "displayName": "", "id": "#/definitions/84275407", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "status": {"$ref": "#/definitions/********"}}, "additionalProperties": false, "x-apifox-orders": ["id", "status"]}}}, {"name": "TenantOutput", "displayName": "", "id": "#/definitions/84275408", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "userId": {"type": "integer", "description": "用户Id", "format": "int64"}, "orgId": {"type": "integer", "description": "机构Id", "format": "int64"}, "host": {"maxLength": 128, "type": ["string", "null"], "description": "主机"}, "tenantType": {"$ref": "#/definitions/********"}, "dbType": {"$ref": "#/definitions/84275276"}, "connection": {"maxLength": 256, "type": ["string", "null"], "description": "数据库连接"}, "configId": {"maxLength": 64, "type": ["string", "null"], "description": "数据库标识"}, "slaveConnections": {"type": ["string", "null"], "description": "从库连接/读写分离"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}, "status": {"$ref": "#/definitions/********"}, "name": {"type": ["string", "null"], "description": "租户名称"}, "adminAccount": {"type": ["string", "null"], "description": "管理员账号"}, "email": {"type": ["string", "null"], "description": "电子邮箱"}, "phone": {"type": ["string", "null"], "description": "电话"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "userId", "orgId", "host", "tenantType", "dbType", "connection", "configId", "slaveConnections", "orderNo", "remark", "status", "name", "adminAccount", "email", "phone"]}}}, {"name": "TenantTypeEnum", "displayName": "", "id": "#/definitions/********", "description": "租户类型枚举<br />&nbsp;Id隔离 Id = 0<br />&nbsp;库隔离 Db = 1<br />", "schema": {"jsonSchema": {"enum": [0, 1], "type": "integer", "description": "租户类型枚举<br />&nbsp;Id隔离 Id = 0<br />&nbsp;库隔离 Db = 1<br />", "format": "int32"}}}, {"name": "TenantUserInput", "displayName": "", "id": "#/definitions/********", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"userId": {"type": "integer", "description": "用户Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["userId"]}}}, {"name": "TriggerStatus", "displayName": "", "id": "#/definitions/********", "description": "<br />&nbsp; Backlog = 0<br />&nbsp; Ready = 1<br />&nbsp; Running = 2<br />&nbsp; Pause = 3<br />&nbsp; Blocked = 4<br />&nbsp; ErrorToReady = 5<br />&nbsp; Archived = 6<br />&nbsp; Panic = 7<br />&nbsp; Overrun = 8<br />&nbsp; Unoccupied = 9<br />&nbsp; NotStart = 10<br />&nbsp; Unknown = 11<br />&nbsp; Unhandled = 12<br />", "schema": {"jsonSchema": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], "type": "integer", "description": "<br />&nbsp; Backlog = 0<br />&nbsp; Ready = 1<br />&nbsp; Running = 2<br />&nbsp; Pause = 3<br />&nbsp; Blocked = 4<br />&nbsp; ErrorToReady = 5<br />&nbsp; Archived = 6<br />&nbsp; Panic = 7<br />&nbsp; Overrun = 8<br />&nbsp; Unoccupied = 9<br />&nbsp; NotStart = 10<br />&nbsp; Unknown = 11<br />&nbsp; Unhandled = 12<br />", "format": "int32"}}}, {"name": "UnlockLoginInput", "displayName": "", "id": "#/definitions/84275412", "description": "解除登录锁定输入参数", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}}, "additionalProperties": false, "description": "解除登录锁定输入参数", "x-apifox-orders": ["id"]}}}, {"name": "UpdateCodeGenInput", "displayName": "", "id": "#/definitions/84275413", "description": "", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": ["string", "null"], "description": "排序字段"}, "order": {"type": ["string", "null"], "description": "排序方向"}, "descStr": {"type": ["string", "null"], "description": "降序排序"}, "authorName": {"type": ["string", "null"], "description": "作者姓名"}, "className": {"type": ["string", "null"], "description": "类名"}, "tablePrefix": {"type": ["string", "null"], "description": "是否移除表前缀"}, "configId": {"type": ["string", "null"], "description": "库定位器名"}, "dbName": {"type": ["string", "null"], "description": "数据库名(保留字段)"}, "dbType": {"type": ["string", "null"], "description": "数据库类型"}, "connectionString": {"type": ["string", "null"], "description": "数据库链接"}, "generateType": {"type": ["string", "null"], "description": "生成方式"}, "tableName": {"type": ["string", "null"], "description": "数据库表名"}, "nameSpace": {"type": ["string", "null"], "description": "命名空间"}, "busName": {"type": ["string", "null"], "description": "业务名（业务代码包名称）"}, "tableComment": {"type": ["string", "null"], "description": "功能名（数据库表名称）"}, "menuApplication": {"type": ["string", "null"], "description": "菜单应用分类（应用编码）"}, "menuPid": {"type": "integer", "description": "菜单父级", "format": "int64"}, "printType": {"type": ["string", "null"], "description": "支持打印类型"}, "printName": {"type": ["string", "null"], "description": "打印模版名称"}, "id": {"type": "integer", "description": "代码生成器Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["page", "pageSize", "field", "order", "descStr", "<PERSON><PERSON><PERSON>", "className", "tablePrefix", "configId", "dbN<PERSON>", "dbType", "connectionString", "generateType", "tableName", "nameSpace", "busName", "tableComment", "menuApplication", "menuPid", "printType", "printName", "id"]}}}, {"name": "UpdateConfigInput", "displayName": "", "id": "#/definitions/84275414", "description": "", "schema": {"jsonSchema": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "name": {"maxLength": 64, "minLength": 1, "type": "string", "description": "名称"}, "code": {"maxLength": 64, "type": ["string", "null"], "description": "编码"}, "value": {"maxLength": 64, "type": ["string", "null"], "description": "属性值"}, "sysFlag": {"$ref": "#/definitions/84275444"}, "groupCode": {"maxLength": 64, "type": ["string", "null"], "description": "分组编码"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "remark": {"maxLength": 256, "type": ["string", "null"], "description": "备注"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "name", "code", "value", "sysFlag", "groupCode", "orderNo", "remark"]}}}, {"name": "UpdateDbColumnInput", "displayName": "", "id": "#/definitions/84275415", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"configId": {"type": ["string", "null"]}, "tableName": {"type": ["string", "null"]}, "columnName": {"type": ["string", "null"]}, "oldColumnName": {"type": ["string", "null"]}, "description": {"type": ["string", "null"]}}, "additionalProperties": false, "x-apifox-orders": ["configId", "tableName", "columnName", "oldColumnName", "description"]}}}, {"name": "UpdateDbTableInput", "displayName": "", "id": "#/definitions/84275416", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"configId": {"type": ["string", "null"]}, "tableName": {"type": ["string", "null"]}, "oldTableName": {"type": ["string", "null"]}, "description": {"type": ["string", "null"]}}, "additionalProperties": false, "x-apifox-orders": ["configId", "tableName", "oldTableName", "description"]}}}, {"name": "UpdateDictDataInput", "displayName": "", "id": "#/definitions/84275417", "description": "", "schema": {"jsonSchema": {"required": ["code", "value"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "dictTypeId": {"type": "integer", "description": "字典类型Id", "format": "int64"}, "value": {"maxLength": 128, "minLength": 1, "type": "string", "description": "值"}, "code": {"maxLength": 64, "minLength": 1, "type": "string", "description": "编码"}, "tagType": {"maxLength": 16, "type": ["string", "null"], "description": "显示样式-标签颜色"}, "styleSetting": {"maxLength": 512, "type": ["string", "null"], "description": "显示样式-Style(控制显示样式)"}, "classSetting": {"maxLength": 512, "type": ["string", "null"], "description": "显示样式-Class(控制显示样式)"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "remark": {"maxLength": 2048, "type": ["string", "null"], "description": "备注"}, "extData": {"type": ["string", "null"], "description": "拓展数据(保存业务功能的配置项)"}, "status": {"$ref": "#/definitions/********"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "dictTypeId", "value", "code", "tagType", "styleSetting", "classSetting", "orderNo", "remark", "extData", "status"]}}}, {"name": "UpdateDictTypeInput", "displayName": "", "id": "#/definitions/84275418", "description": "", "schema": {"jsonSchema": {"required": ["code", "name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "name": {"maxLength": 64, "minLength": 1, "type": "string", "description": "名称"}, "code": {"maxLength": 64, "minLength": 1, "type": "string", "description": "编码"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "remark": {"maxLength": 256, "type": ["string", "null"], "description": "备注"}, "status": {"$ref": "#/definitions/********"}, "children": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275379"}, "description": "字典值集合"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "name", "code", "orderNo", "remark", "status", "children"]}}}, {"name": "UpdateJobDetailInput", "displayName": "", "id": "#/definitions/84275419", "description": "", "schema": {"jsonSchema": {"required": ["jobId"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "groupName": {"maxLength": 128, "type": ["string", "null"], "description": "组名称"}, "jobType": {"maxLength": 128, "type": ["string", "null"], "description": "作业类型FullName"}, "assemblyName": {"maxLength": 128, "type": ["string", "null"], "description": "程序集Name"}, "description": {"maxLength": 128, "type": ["string", "null"], "description": "描述信息"}, "concurrent": {"type": "boolean", "description": "是否并行执行"}, "includeAnnotations": {"type": "boolean", "description": "是否扫描特性触发器"}, "properties": {"type": ["string", "null"], "description": "额外数据"}, "updatedTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createType": {"$ref": "#/definitions/84275308"}, "scriptCode": {"type": ["string", "null"], "description": "脚本代码"}, "jobId": {"minLength": 2, "type": "string", "description": "作业Id"}}, "additionalProperties": false, "x-apifox-orders": ["id", "groupName", "jobType", "assemblyName", "description", "concurrent", "includeAnnotations", "properties", "updatedTime", "createType", "scriptCode", "jobId"]}}}, {"name": "UpdateJobTriggerInput", "displayName": "", "id": "#/definitions/84275420", "description": "", "schema": {"jsonSchema": {"required": ["jobId", "triggerId"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "triggerType": {"maxLength": 128, "type": ["string", "null"], "description": "触发器类型FullName"}, "assemblyName": {"maxLength": 128, "type": ["string", "null"], "description": "程序集Name"}, "args": {"maxLength": 128, "type": ["string", "null"], "description": "参数"}, "description": {"maxLength": 128, "type": ["string", "null"], "description": "描述信息"}, "status": {"$ref": "#/definitions/********"}, "startTime": {"type": ["string", "null"], "description": "起始时间", "format": "date-time"}, "endTime": {"type": ["string", "null"], "description": "结束时间", "format": "date-time"}, "lastRunTime": {"type": ["string", "null"], "description": "最近运行时间", "format": "date-time"}, "nextRunTime": {"type": ["string", "null"], "description": "下一次运行时间", "format": "date-time"}, "numberOfRuns": {"type": "integer", "description": "触发次数", "format": "int64"}, "maxNumberOfRuns": {"type": "integer", "description": "最大触发次数（0:不限制，n:N次）", "format": "int64"}, "numberOfErrors": {"type": "integer", "description": "出错次数", "format": "int64"}, "maxNumberOfErrors": {"type": "integer", "description": "最大出错次数（0:不限制，n:N次）", "format": "int64"}, "numRetries": {"type": "integer", "description": "重试次数", "format": "int32"}, "retryTimeout": {"type": "integer", "description": "重试间隔时间（ms）", "format": "int32"}, "startNow": {"type": "boolean", "description": "是否立即启动"}, "runOnStart": {"type": "boolean", "description": "是否启动时执行一次"}, "resetOnlyOnce": {"type": "boolean", "description": "是否在启动时重置最大触发次数等于一次的作业"}, "updatedTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "jobId": {"minLength": 2, "type": "string", "description": "作业Id"}, "triggerId": {"minLength": 2, "type": "string", "description": "触发器Id"}}, "additionalProperties": false, "x-apifox-orders": ["id", "triggerType", "assemblyName", "args", "description", "status", "startTime", "endTime", "lastRunTime", "nextRunTime", "numberOfRuns", "maxNumberOfRuns", "numberOfErrors", "maxNumberOfErrors", "numRetries", "retryTimeout", "startNow", "runOnStart", "resetOnlyOnce", "updatedTime", "jobId", "triggerId"]}}}, {"name": "UpdateMenuInput", "displayName": "", "id": "#/definitions/84275421", "description": "", "schema": {"jsonSchema": {"required": ["title"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "pid": {"type": "integer", "description": "父Id", "format": "int64"}, "type": {"$ref": "#/definitions/********"}, "name": {"maxLength": 64, "type": ["string", "null"], "description": "路由名称"}, "path": {"maxLength": 128, "type": ["string", "null"], "description": "路由地址"}, "component": {"maxLength": 128, "type": ["string", "null"], "description": "组件路径"}, "redirect": {"maxLength": 128, "type": ["string", "null"], "description": "重定向"}, "permission": {"maxLength": 128, "type": ["string", "null"], "description": "权限标识"}, "icon": {"maxLength": 128, "type": ["string", "null"], "description": "图标"}, "isIframe": {"type": "boolean", "description": "是否内嵌"}, "outLink": {"maxLength": 256, "type": ["string", "null"], "description": "外链链接"}, "isHide": {"type": "boolean", "description": "是否隐藏"}, "isKeepAlive": {"type": "boolean", "description": "是否缓存"}, "isAffix": {"type": "boolean", "description": "是否固定"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "status": {"$ref": "#/definitions/********"}, "remark": {"maxLength": 256, "type": ["string", "null"], "description": "备注"}, "children": {"type": ["array", "null"], "items": {"$ref": "#/definitions/********"}, "description": "菜单子项"}, "title": {"minLength": 1, "type": "string", "description": "名称"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "pid", "type", "name", "path", "component", "redirect", "permission", "icon", "isIframe", "outLink", "isHide", "isKeepAlive", "isAffix", "orderNo", "status", "remark", "children", "title"]}}}, {"name": "UpdateNoticeInput", "displayName": "", "id": "#/definitions/84275422", "description": "", "schema": {"jsonSchema": {"required": ["content", "title"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "title": {"maxLength": 32, "minLength": 1, "type": "string", "description": "标题"}, "content": {"minLength": 1, "type": "string", "description": "内容"}, "type": {"$ref": "#/definitions/84275325"}, "publicUserId": {"type": "integer", "description": "发布人Id", "format": "int64"}, "publicUserName": {"maxLength": 32, "type": ["string", "null"], "description": "发布人姓名"}, "publicOrgId": {"type": "integer", "description": "发布机构Id", "format": "int64"}, "publicOrgName": {"maxLength": 64, "type": ["string", "null"], "description": "发布机构名称"}, "publicTime": {"type": ["string", "null"], "description": "发布时间", "format": "date-time"}, "cancelTime": {"type": ["string", "null"], "description": "撤回时间", "format": "date-time"}, "status": {"$ref": "#/definitions/84275324"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "title", "content", "type", "publicUserId", "publicUserName", "publicOrgId", "publicOrgName", "publicTime", "cancelTime", "status"]}}}, {"name": "UpdateOpenAccessInput", "displayName": "", "id": "#/definitions/84275423", "description": "", "schema": {"jsonSchema": {"required": ["accessKey", "accessSecret", "bindUserId"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "bindTenantId": {"type": "integer", "description": "绑定租户Id", "format": "int64"}, "accessKey": {"minLength": 1, "type": "string", "description": "身份标识"}, "accessSecret": {"minLength": 1, "type": "string", "description": "密钥"}, "bindUserId": {"type": "integer", "description": "绑定用户Id", "format": "int64"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "bindTenantId", "accessKey", "accessSecret", "bindUserId"]}}}, {"name": "UpdateOrgInput", "displayName": "", "id": "#/definitions/84275424", "description": "", "schema": {"jsonSchema": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "pid": {"type": "integer", "description": "父Id", "format": "int64"}, "code": {"maxLength": 64, "type": ["string", "null"], "description": "编码"}, "level": {"type": ["integer", "null"], "description": "级别", "format": "int32"}, "type": {"maxLength": 64, "type": ["string", "null"], "description": "机构类型-数据字典"}, "directorId": {"type": ["integer", "null"], "description": "负责人Id", "format": "int64"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "status": {"$ref": "#/definitions/********"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}, "children": {"type": ["array", "null"], "items": {"$ref": "#/definitions/********"}, "description": "机构子项"}, "disabled": {"type": "boolean", "description": "是否禁止选中"}, "name": {"minLength": 1, "type": "string", "description": "名称"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "pid", "code", "level", "type", "directorId", "orderNo", "status", "remark", "children", "disabled", "name"]}}}, {"name": "UpdatePluginInput", "displayName": "", "id": "#/definitions/84275425", "description": "", "schema": {"jsonSchema": {"required": ["csharpCode", "name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "csharpCode": {"minLength": 1, "type": "string", "description": "C#代码"}, "assemblyName": {"maxLength": 512, "type": ["string", "null"], "description": "程序集名称"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "status": {"$ref": "#/definitions/********"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}, "name": {"minLength": 1, "type": "string", "description": "名称"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "csharpCode", "assemblyName", "orderNo", "status", "remark", "name"]}}}, {"name": "UpdatePosInput", "displayName": "", "id": "#/definitions/84275426", "description": "", "schema": {"jsonSchema": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "code": {"maxLength": 64, "type": ["string", "null"], "description": "编码"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}, "status": {"$ref": "#/definitions/********"}, "name": {"minLength": 1, "type": "string", "description": "名称"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "code", "orderNo", "remark", "status", "name"]}}}, {"name": "UpdatePrintInput", "displayName": "", "id": "#/definitions/84275427", "description": "", "schema": {"jsonSchema": {"required": ["name", "template"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "template": {"minLength": 1, "type": "string", "description": "打印模板"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "status": {"$ref": "#/definitions/********"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}, "name": {"minLength": 1, "type": "string", "description": "名称"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "template", "orderNo", "status", "remark", "name"]}}}, {"name": "UpdateRegionInput", "displayName": "", "id": "#/definitions/84275428", "description": "", "schema": {"jsonSchema": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "pid": {"type": "integer", "description": "父Id", "format": "int64"}, "shortName": {"maxLength": 32, "type": ["string", "null"], "description": "简称"}, "mergerName": {"maxLength": 64, "type": ["string", "null"], "description": "组合名"}, "code": {"maxLength": 32, "type": ["string", "null"], "description": "行政代码"}, "zipCode": {"maxLength": 6, "type": ["string", "null"], "description": "邮政编码"}, "cityCode": {"maxLength": 6, "type": ["string", "null"], "description": "区号"}, "level": {"type": "integer", "description": "层级", "format": "int32"}, "pinYin": {"maxLength": 128, "type": ["string", "null"], "description": "拼音"}, "lng": {"type": "number", "description": "经度", "format": "float"}, "lat": {"type": "number", "description": "维度", "format": "float"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}, "children": {"type": ["array", "null"], "items": {"$ref": "#/definitions/84275399"}, "description": "机构子项"}, "name": {"minLength": 1, "type": "string", "description": "名称"}}, "additionalProperties": false, "x-apifox-orders": ["id", "pid", "shortName", "mergerName", "code", "zipCode", "cityCode", "level", "pinYin", "lng", "lat", "orderNo", "remark", "children", "name"]}}}, {"name": "UpdateRoleInput", "displayName": "", "id": "#/definitions/84275429", "description": "", "schema": {"jsonSchema": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "code": {"maxLength": 64, "type": ["string", "null"], "description": "编码"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "dataScope": {"$ref": "#/definitions/84275269"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}, "status": {"$ref": "#/definitions/********"}, "name": {"minLength": 1, "type": "string", "description": "名称"}, "menuIdList": {"type": ["array", "null"], "items": {"type": "integer", "format": "int64"}, "description": "菜单Id集合"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "code", "orderNo", "dataScope", "remark", "status", "name", "menuIdList"]}}}, {"name": "UpdateTenantInput", "displayName": "", "id": "#/definitions/********", "description": "", "schema": {"jsonSchema": {"required": ["adminAccount", "name"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "userId": {"type": "integer", "description": "用户Id", "format": "int64"}, "orgId": {"type": "integer", "description": "机构Id", "format": "int64"}, "host": {"maxLength": 128, "type": ["string", "null"], "description": "主机"}, "tenantType": {"$ref": "#/definitions/********"}, "dbType": {"$ref": "#/definitions/84275276"}, "connection": {"maxLength": 256, "type": ["string", "null"], "description": "数据库连接"}, "configId": {"maxLength": 64, "type": ["string", "null"], "description": "数据库标识"}, "slaveConnections": {"type": ["string", "null"], "description": "从库连接/读写分离"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "remark": {"maxLength": 128, "type": ["string", "null"], "description": "备注"}, "status": {"$ref": "#/definitions/********"}, "email": {"type": ["string", "null"], "description": "电子邮箱"}, "phone": {"type": ["string", "null"], "description": "电话"}, "name": {"minLength": 2, "type": "string", "description": "租户名称"}, "adminAccount": {"minLength": 3, "type": "string", "description": "租管账号"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "userId", "orgId", "host", "tenantType", "dbType", "connection", "configId", "slaveConnections", "orderNo", "remark", "status", "email", "phone", "name", "adminAccount"]}}}, {"name": "UpdateUserInput", "displayName": "", "id": "#/definitions/********", "description": "更新用户输入参数", "schema": {"jsonSchema": {"required": ["account", "realName"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "nickName": {"maxLength": 32, "type": ["string", "null"], "description": "昵称"}, "avatar": {"maxLength": 512, "type": ["string", "null"], "description": "头像"}, "sex": {"$ref": "#/definitions/********"}, "age": {"type": "integer", "description": "年龄", "format": "int32"}, "birthday": {"type": ["string", "null"], "description": "出生日期", "format": "date-time"}, "nation": {"maxLength": 32, "type": ["string", "null"], "description": "民族"}, "phone": {"maxLength": 16, "type": ["string", "null"], "description": "手机号码"}, "cardType": {"$ref": "#/definitions/********"}, "idCardNum": {"maxLength": 32, "type": ["string", "null"], "description": "身份证号"}, "email": {"maxLength": 64, "type": ["string", "null"], "description": "邮箱"}, "address": {"maxLength": 256, "type": ["string", "null"], "description": "地址"}, "cultureLevel": {"$ref": "#/definitions/84275267"}, "politicalOutlook": {"maxLength": 16, "type": ["string", "null"], "description": "政治面貌"}, "college": {"maxLength": 128, "type": ["string", "null"], "description": "毕业院校"}, "officePhone": {"maxLength": 16, "type": ["string", "null"], "description": "办公电话"}, "emergencyContact": {"maxLength": 32, "type": ["string", "null"], "description": "紧急联系人"}, "emergencyPhone": {"maxLength": 16, "type": ["string", "null"], "description": "紧急联系人电话"}, "emergencyAddress": {"maxLength": 256, "type": ["string", "null"], "description": "紧急联系人地址"}, "introduction": {"maxLength": 512, "type": ["string", "null"], "description": "个人简介"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "status": {"$ref": "#/definitions/********"}, "remark": {"maxLength": 256, "type": ["string", "null"], "description": "备注"}, "accountType": {"$ref": "#/definitions/********"}, "orgId": {"type": "integer", "description": "直属机构Id", "format": "int64"}, "sysOrg": {"$ref": "#/definitions/********"}, "managerUserId": {"type": ["integer", "null"], "description": "直属主管Id", "format": "int64"}, "posId": {"type": "integer", "description": "职位Id", "format": "int64"}, "jobNum": {"maxLength": 32, "type": ["string", "null"], "description": "工号"}, "posLevel": {"maxLength": 32, "type": ["string", "null"], "description": "职级"}, "posTitle": {"maxLength": 32, "type": ["string", "null"], "description": "职称"}, "expertise": {"maxLength": 32, "type": ["string", "null"], "description": "擅长领域"}, "officeZone": {"maxLength": 32, "type": ["string", "null"], "description": "办公区域"}, "office": {"maxLength": 32, "type": ["string", "null"], "description": "办公室"}, "joinDate": {"type": ["string", "null"], "description": "入职日期", "format": "date-time"}, "lastLoginIp": {"maxLength": 256, "type": ["string", "null"], "description": "最新登录Ip"}, "lastLoginAddress": {"maxLength": 128, "type": ["string", "null"], "description": "最新登录地点"}, "lastLoginTime": {"type": ["string", "null"], "description": "最新登录时间", "format": "date-time"}, "lastLoginDevice": {"maxLength": 128, "type": ["string", "null"], "description": "最新登录设备"}, "signature": {"maxLength": 512, "type": ["string", "null"], "description": "电子签名"}, "account": {"minLength": 1, "type": "string", "description": "账号"}, "realName": {"minLength": 1, "type": "string", "description": "真实姓名"}, "roleIdList": {"type": ["array", "null"], "items": {"type": "integer", "format": "int64"}, "description": "角色集合"}, "extOrgIdList": {"type": ["array", "null"], "items": {"$ref": "#/definitions/********"}, "description": "扩展机构集合"}}, "additionalProperties": false, "description": "更新用户输入参数", "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "nick<PERSON><PERSON>", "avatar", "sex", "age", "birthday", "nation", "phone", "cardType", "idCardNum", "email", "address", "cultureLevel", "politicalOutlook", "college", "officePhone", "emergencyContact", "emergencyPhone", "emergencyAddress", "introduction", "orderNo", "status", "remark", "accountType", "orgId", "sysOrg", "managerUserId", "posId", "job<PERSON>um", "posLevel", "posTitle", "expertise", "officeZone", "office", "joinDate", "lastLoginIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lastLoginTime", "lastLoginDevice", "signature", "account", "realName", "roleIdList", "extOrgIdList"]}}}, {"name": "UploadFileFromBase64Input", "displayName": "", "id": "#/definitions/********", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"fileDataBase64": {"type": ["string", "null"], "description": "文件内容"}, "contentType": {"type": ["string", "null"], "description": "文件类型( \"image/jpeg\",)"}, "fileName": {"type": ["string", "null"], "description": "文件名称"}, "path": {"type": ["string", "null"], "description": "保存路径"}}, "additionalProperties": false, "x-apifox-orders": ["fileDataBase64", "contentType", "fileName", "path"]}}}, {"name": "UserInput", "displayName": "", "id": "#/definitions/********", "description": "设置用户状态输入参数", "schema": {"jsonSchema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "status": {"$ref": "#/definitions/********"}}, "additionalProperties": false, "description": "设置用户状态输入参数", "x-apifox-orders": ["id", "status"]}}}, {"name": "UserOutput", "displayName": "", "id": "#/definitions/********", "description": "", "schema": {"jsonSchema": {"required": ["account"], "type": "object", "properties": {"id": {"type": "integer", "description": "雪花Id", "format": "int64"}, "createTime": {"type": ["string", "null"], "description": "创建时间", "format": "date-time"}, "updateTime": {"type": ["string", "null"], "description": "更新时间", "format": "date-time"}, "createUserId": {"type": ["integer", "null"], "description": "创建者Id", "format": "int64"}, "createUserName": {"type": ["string", "null"], "description": "创建者姓名"}, "updateUserId": {"type": ["integer", "null"], "description": "修改者Id", "format": "int64"}, "updateUserName": {"type": ["string", "null"], "description": "修改者姓名"}, "isDelete": {"type": "boolean", "description": "软删除"}, "tenantId": {"type": ["integer", "null"], "description": "租户Id", "format": "int64"}, "account": {"maxLength": 32, "minLength": 1, "type": "string", "description": "账号"}, "realName": {"maxLength": 32, "type": ["string", "null"], "description": "真实姓名"}, "nickName": {"maxLength": 32, "type": ["string", "null"], "description": "昵称"}, "avatar": {"maxLength": 512, "type": ["string", "null"], "description": "头像"}, "sex": {"$ref": "#/definitions/********"}, "age": {"type": "integer", "description": "年龄", "format": "int32"}, "birthday": {"type": ["string", "null"], "description": "出生日期", "format": "date-time"}, "nation": {"maxLength": 32, "type": ["string", "null"], "description": "民族"}, "phone": {"maxLength": 16, "type": ["string", "null"], "description": "手机号码"}, "cardType": {"$ref": "#/definitions/********"}, "idCardNum": {"maxLength": 32, "type": ["string", "null"], "description": "身份证号"}, "email": {"maxLength": 64, "type": ["string", "null"], "description": "邮箱"}, "address": {"maxLength": 256, "type": ["string", "null"], "description": "地址"}, "cultureLevel": {"$ref": "#/definitions/84275267"}, "politicalOutlook": {"maxLength": 16, "type": ["string", "null"], "description": "政治面貌"}, "college": {"maxLength": 128, "type": ["string", "null"], "description": "毕业院校"}, "officePhone": {"maxLength": 16, "type": ["string", "null"], "description": "办公电话"}, "emergencyContact": {"maxLength": 32, "type": ["string", "null"], "description": "紧急联系人"}, "emergencyPhone": {"maxLength": 16, "type": ["string", "null"], "description": "紧急联系人电话"}, "emergencyAddress": {"maxLength": 256, "type": ["string", "null"], "description": "紧急联系人地址"}, "introduction": {"maxLength": 512, "type": ["string", "null"], "description": "个人简介"}, "orderNo": {"type": "integer", "description": "排序", "format": "int32"}, "status": {"$ref": "#/definitions/********"}, "remark": {"maxLength": 256, "type": ["string", "null"], "description": "备注"}, "accountType": {"$ref": "#/definitions/********"}, "orgId": {"type": "integer", "description": "直属机构Id", "format": "int64"}, "sysOrg": {"$ref": "#/definitions/********"}, "managerUserId": {"type": ["integer", "null"], "description": "直属主管Id", "format": "int64"}, "posId": {"type": "integer", "description": "职位Id", "format": "int64"}, "jobNum": {"maxLength": 32, "type": ["string", "null"], "description": "工号"}, "posLevel": {"maxLength": 32, "type": ["string", "null"], "description": "职级"}, "posTitle": {"maxLength": 32, "type": ["string", "null"], "description": "职称"}, "expertise": {"maxLength": 32, "type": ["string", "null"], "description": "擅长领域"}, "officeZone": {"maxLength": 32, "type": ["string", "null"], "description": "办公区域"}, "office": {"maxLength": 32, "type": ["string", "null"], "description": "办公室"}, "joinDate": {"type": ["string", "null"], "description": "入职日期", "format": "date-time"}, "lastLoginIp": {"maxLength": 256, "type": ["string", "null"], "description": "最新登录Ip"}, "lastLoginAddress": {"maxLength": 128, "type": ["string", "null"], "description": "最新登录地点"}, "lastLoginTime": {"type": ["string", "null"], "description": "最新登录时间", "format": "date-time"}, "lastLoginDevice": {"maxLength": 128, "type": ["string", "null"], "description": "最新登录设备"}, "signature": {"maxLength": 512, "type": ["string", "null"], "description": "电子签名"}, "orgName": {"type": ["string", "null"], "description": "机构名称"}, "posName": {"type": ["string", "null"], "description": "职位名称"}, "roleName": {"type": ["string", "null"], "description": "角色名称"}}, "additionalProperties": false, "x-apifox-orders": ["id", "createTime", "updateTime", "createUserId", "createUserName", "updateUserId", "updateUserName", "isDelete", "tenantId", "account", "realName", "nick<PERSON><PERSON>", "avatar", "sex", "age", "birthday", "nation", "phone", "cardType", "idCardNum", "email", "address", "cultureLevel", "politicalOutlook", "college", "officePhone", "emergencyContact", "emergencyPhone", "emergencyAddress", "introduction", "orderNo", "status", "remark", "accountType", "orgId", "sysOrg", "managerUserId", "posId", "job<PERSON>um", "posLevel", "posTitle", "expertise", "officeZone", "office", "joinDate", "lastLoginIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lastLoginTime", "lastLoginDevice", "signature", "orgName", "posName", "<PERSON><PERSON><PERSON>"]}}}, {"name": "UserRoleInput", "displayName": "", "id": "#/definitions/********", "description": "授权用户角色", "schema": {"jsonSchema": {"type": "object", "properties": {"userId": {"type": "integer", "description": "用户Id", "format": "int64"}, "roleIdList": {"type": ["array", "null"], "items": {"type": "integer", "format": "int64"}, "description": "角色Id集合"}}, "additionalProperties": false, "description": "授权用户角色", "x-apifox-orders": ["userId", "roleIdList"]}}}, {"name": "WechatPayOutput", "displayName": "", "id": "#/definitions/********", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"openId": {"type": ["string", "null"], "description": "OpenId"}, "total": {"type": "integer", "description": "订单金额", "format": "int32"}, "attachment": {"type": ["string", "null"], "description": "附加数据"}, "goodsTag": {"type": ["string", "null"], "description": "优惠标记"}}, "additionalProperties": false, "x-apifox-orders": ["openId", "total", "attachment", "goodsTag"]}}}, {"name": "WechatPayParaInput", "displayName": "", "id": "#/definitions/84275437", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"prepayId": {"type": ["string", "null"], "description": "订单Id"}}, "additionalProperties": false, "x-apifox-orders": ["prepayId"]}}}, {"name": "WechatPayTransactionInput", "displayName": "", "id": "#/definitions/84275438", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"openId": {"type": ["string", "null"], "description": "OpenId"}, "total": {"type": "integer", "description": "订单金额", "format": "int32"}, "description": {"type": ["string", "null"], "description": "商品描述"}, "attachment": {"type": ["string", "null"], "description": "附加数据"}, "goodsTag": {"type": ["string", "null"], "description": "优惠标记"}}, "additionalProperties": false, "x-apifox-orders": ["openId", "total", "description", "attachment", "goodsTag"]}}}, {"name": "WechatUserInput", "displayName": "", "id": "#/definitions/84275439", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码", "format": "int32"}, "pageSize": {"type": "integer", "description": "页码容量", "format": "int32"}, "field": {"type": ["string", "null"], "description": "排序字段"}, "order": {"type": ["string", "null"], "description": "排序方向"}, "descStr": {"type": ["string", "null"], "description": "降序排序"}, "nickName": {"type": ["string", "null"], "description": "昵称"}, "phoneNumber": {"type": ["string", "null"], "description": "手机号码"}}, "additionalProperties": false, "x-apifox-orders": ["page", "pageSize", "field", "order", "descStr", "nick<PERSON><PERSON>", "phoneNumber"]}}}, {"name": "WechatUserLogin", "displayName": "", "id": "#/definitions/84275440", "description": "微信用户登录", "schema": {"jsonSchema": {"required": ["openId"], "type": "object", "properties": {"openId": {"minLength": 10, "type": "string", "description": "OpenId"}}, "additionalProperties": false, "description": "微信用户登录", "x-apifox-orders": ["openId"]}}}, {"name": "WxOpenIdLoginInput", "displayName": "", "id": "#/definitions/84275441", "description": "微信小程序登录", "schema": {"jsonSchema": {"required": ["openId"], "type": "object", "properties": {"openId": {"minLength": 10, "type": "string", "description": "OpenId"}}, "additionalProperties": false, "description": "微信小程序登录", "x-apifox-orders": ["openId"]}}}, {"name": "WxOpenIdOutput", "displayName": "", "id": "#/definitions/84275442", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"openId": {"type": ["string", "null"]}}, "additionalProperties": false, "x-apifox-orders": ["openId"]}}}, {"name": "WxPhoneOutput", "displayName": "", "id": "#/definitions/84275443", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"phoneNumber": {"type": ["string", "null"]}}, "additionalProperties": false, "x-apifox-orders": ["phoneNumber"]}}}, {"name": "YesNoEnum", "displayName": "", "id": "#/definitions/84275444", "description": "是否枚举<br />&nbsp;是 Y = 1<br />&nbsp;否 N = 2<br />", "schema": {"jsonSchema": {"enum": [1, 2], "type": "integer", "description": "是否枚举<br />&nbsp;是 Y = 1<br />&nbsp;否 N = 2<br />", "format": "int32"}}}]}]}], "responseCollection": [{"id": 4088603, "createdAt": "2024-02-29T09:40:10.000Z", "updatedAt": "2024-02-29T09:40:10.000Z", "deletedAt": null, "name": "根目录", "type": "root", "description": "", "children": [], "auth": {}, "projectId": 4084018, "projectBranchId": 0, "parentId": 0, "createdById": 1145276, "updatedById": 1145276, "items": []}], "environments": [{"name": "开发环境", "parameters": {"cookie": [], "query": [], "header": [], "body": []}, "variables": [], "type": "normal", "visibility": "protected", "ordering": 0, "tags": [{"name": "", "color": "#9373EE"}], "id": "18499638", "baseUrl": "http://localhost:5005", "baseUrls": {"default": "http://localhost:5005"}}], "commonScripts": [], "globalVariables": [], "commonParameters": null, "projectSetting": {"id": "4084141", "auth": {"type": "bearer", "bearer": {"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEzMDAwMDAwMDAxMTEsIlRlbmFudElkIjoxMzAwMDAwMDAwMDAxLCJBY2NvdW50IjoiYWRtaW4iLCJSZWFsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsIkFjY291bnRUeXBlIjo4ODgsIk9yZ0lkIjoxMzAwMDAwMDAwMTAxLCJPcmdOYW1lIjoi5aSn5ZCN56eR5oqAIiwiT3JnVHlwZSI6IjEwMSIsImlhdCI6MTcwOTgwMjI5NiwibmJmIjoxNzA5ODAyMjk2LCJleHAiOjE3MTA0MDcwOTYsImlzcyI6IkFkbWluLk5FVCIsImF1ZCI6IkFkbWluLk5FVCJ9.w18JSugPFU50eMOkaLDwq2gWxPNO_3znLeUTgu2i7Ec"}}, "servers": [{"id": "default", "name": "默认服务"}], "gateway": [], "language": "zh-CN", "apiStatuses": ["developing", "testing", "released", "deprecated"], "mockSettings": {}, "preProcessors": [], "postProcessors": [], "advancedSettings": {"responseValidate": false, "enableJsonc": true, "isDefaultUrlEncoding": 2, "enableBigint": false, "preferredHttpVersion": {}, "enableTestScenarioSetting": false, "enableYAPICompatScript": false}, "initialDisabledMockIds": [], "cloudMock": {"security": "free", "enable": false, "tokenKey": "apifoxToken"}}, "projectAssociations": []}