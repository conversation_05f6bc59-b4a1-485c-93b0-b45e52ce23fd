﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

namespace Admin.NET.Application;

/// <summary>
/// 会议室表基础输入参数
/// </summary>
public class MeetingRoomsBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 房间名称
    /// </summary>
    public virtual string? room_name { get; set; }
    
    /// <summary>
    /// 房间编号
    /// </summary>
    public virtual string? room_code { get; set; }
    
    /// <summary>
    /// 房间位置
    /// </summary>
    public virtual string? room_location { get; set; }
    
    /// <summary>
    /// 会议室容量
    /// </summary>
    public virtual int? capacity { get; set; }
    
    /// <summary>
    /// 绑定设备
    /// </summary>
    public virtual string? equipment { get; set; }
    
    /// <summary>
    /// 会议室状态
    /// </summary>
    public virtual int? meeting_status { get; set; }
    
}

/// <summary>
/// 会议室表分页查询输入参数
/// </summary>
public class PageMeetingRoomsInput : BasePageInput
{
    /// <summary>
    /// 房间名称
    /// </summary>
    public string? room_name { get; set; }
    
    /// <summary>
    /// 房间编号
    /// </summary>
    public string? room_code { get; set; }
    
    /// <summary>
    /// 房间位置
    /// </summary>
    public string? room_location { get; set; }
    
    /// <summary>
    /// 会议室容量
    /// </summary>
    public int? capacity { get; set; }
    
    /// <summary>
    /// 绑定设备
    /// </summary>
    public string? equipment { get; set; }
    
    /// <summary>
    /// 会议室状态
    /// </summary>
    public int? meeting_status { get; set; }
    
}

/// <summary>
/// 会议室表增加输入参数
/// </summary>
public class AddMeetingRoomsInput
{
    /// <summary>
    /// 房间名称
    /// </summary>
    [MaxLength(200, ErrorMessage = "房间名称字符长度不能超过200")]
    public string? room_name { get; set; }
    
    /// <summary>
    /// 房间编号
    /// </summary>
    [MaxLength(50, ErrorMessage = "房间编号字符长度不能超过50")]
    public string? room_code { get; set; }
    
    /// <summary>
    /// 房间位置
    /// </summary>
    [MaxLength(300, ErrorMessage = "房间位置字符长度不能超过300")]
    public string? room_location { get; set; }
    
    /// <summary>
    /// 会议室容量
    /// </summary>
    public int? capacity { get; set; }
    
    /// <summary>
    /// 绑定设备
    /// </summary>
    [MaxLength(1, ErrorMessage = "绑定设备字符长度不能超过1")]
    public string? equipment { get; set; }
    
    /// <summary>
    /// 会议室状态
    /// </summary>
    public int? meeting_status { get; set; }
    
}

/// <summary>
/// 会议室表删除输入参数
/// </summary>
public class DeleteMeetingRoomsInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 会议室表更新输入参数
/// </summary>
public class UpdateMeetingRoomsInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 房间名称
    /// </summary>    
    [MaxLength(200, ErrorMessage = "房间名称字符长度不能超过200")]
    public string? room_name { get; set; }
    
    /// <summary>
    /// 房间编号
    /// </summary>    
    [MaxLength(50, ErrorMessage = "房间编号字符长度不能超过50")]
    public string? room_code { get; set; }
    
    /// <summary>
    /// 房间位置
    /// </summary>    
    [MaxLength(300, ErrorMessage = "房间位置字符长度不能超过300")]
    public string? room_location { get; set; }
    
    /// <summary>
    /// 会议室容量
    /// </summary>    
    public int? capacity { get; set; }
    
    /// <summary>
    /// 绑定设备
    /// </summary>    
    [MaxLength(1, ErrorMessage = "绑定设备字符长度不能超过1")]
    public string? equipment { get; set; }
    
    /// <summary>
    /// 会议室状态
    /// </summary>    
    public int? meeting_status { get; set; }
    
}

/// <summary>
/// 会议室表主键查询输入参数
/// </summary>
public class QueryByIdMeetingRoomsInput : DeleteMeetingRoomsInput
{
}

