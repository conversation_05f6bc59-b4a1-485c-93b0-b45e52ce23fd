using Admin.NET.Plugin.GreenDisplay.Service;
using System.Collections.Concurrent;

namespace Admin.NET.Plugin.GreenDisplay.Dto;

/// <summary>
/// 批量操作结果
/// </summary>
/// <typeparam name="T">操作项类型</typeparam>
public class BatchOperationResult<T>
{
    /// <summary>
    /// 总数
    /// </summary>
    public int Total { get; set; }

    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailureCount { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => Total > 0 ? (double)SuccessCount / Total : 0;

    /// <summary>
    /// 成功项列表
    /// </summary>
    public List<T> SuccessItems { get; set; } = new();

    /// <summary>
    /// 失败项列表
    /// </summary>
    public List<BatchOperationError<T>> FailureItems { get; set; } = new();

    /// <summary>
    /// 是否全部成功
    /// </summary>
    public bool IsAllSuccess => FailureCount == 0;

    /// <summary>
    /// 是否有失败
    /// </summary>
    public bool HasFailures => FailureCount > 0;
}

/// <summary>
/// 批量操作错误信息
/// </summary>
/// <typeparam name="T">操作项类型</typeparam>
public class BatchOperationError<T>
{
    /// <summary>
    /// 操作项
    /// </summary>
    public T? Item { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 错误代码
    /// </summary>
    public string ErrorCode { get; set; } = string.Empty;

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception? Exception { get; set; }
}

/// <summary>
/// 批量设备绑定输入
/// </summary>
public class BatchDeviceBindInput
{
    /// <summary>
    /// 设备绑定列表
    /// </summary>
    public List<BindMultipleLabelsInput> DeviceBindings { get; set; } = new();

    /// <summary>
    /// 并发限制（默认5）
    /// </summary>
    public int ConcurrencyLimit { get; set; } = 5;

    /// <summary>
    /// 遇到错误时是否继续处理（默认true）
    /// </summary>
    public bool ContinueOnError { get; set; } = true;
}

/// <summary>
/// 批量设备查询输入
/// </summary>
public class BatchDeviceQueryInput
{
    /// <summary>
    /// 设备MAC地址列表
    /// </summary>
    public List<string> DeviceMacs { get; set; } = new();

    /// <summary>
    /// 并发限制（默认10）
    /// </summary>
    public int ConcurrencyLimit { get; set; } = 10;

    /// <summary>
    /// 遇到错误时是否继续处理（默认true）
    /// </summary>
    public bool ContinueOnError { get; set; } = true;
}

/// <summary>
/// 批量标签更新输入
/// </summary>
public class BatchLabelUpdateInput
{
    /// <summary>
    /// 标签更新列表
    /// </summary>
    public List<UpdateLabelInput> LabelUpdates { get; set; } = new();

    /// <summary>
    /// 并发限制（默认5）
    /// </summary>
    public int ConcurrencyLimit { get; set; } = 5;

    /// <summary>
    /// 遇到错误时是否继续处理（默认true）
    /// </summary>
    public bool ContinueOnError { get; set; } = true;
}

/// <summary>
/// 批量操作统计信息
/// </summary>
public class BatchOperationStats
{
    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 持续时间
    /// </summary>
    public TimeSpan? Duration => EndTime?.Subtract(StartTime);

    /// <summary>
    /// 平均处理时间（毫秒）
    /// </summary>
    public double AverageProcessingTime { get; set; }

    /// <summary>
    /// 吞吐量（每秒处理数量）
    /// </summary>
    public double Throughput { get; set; }
}