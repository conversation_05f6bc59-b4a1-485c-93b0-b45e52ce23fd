﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Mapster;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Application.Entity;
namespace Admin.NET.Application;

/// <summary>
/// 设备绑定表服务 🧩
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public partial class DeviceBindingsService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<DeviceBindings> _deviceBindingsRep;

    public DeviceBindingsService(SqlSugarRepository<DeviceBindings> deviceBindingsRep)
    {
        _deviceBindingsRep = deviceBindingsRep;
    }

    /// <summary>
    /// 分页查询设备绑定表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询设备绑定表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<DeviceBindingsOutput>> Page(PageDeviceBindingsInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _deviceBindingsRep.AsQueryable()
            .WhereIF(input.binding_type != null, u => u.binding_type == input.binding_type)
            .Select<DeviceBindingsOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取设备绑定表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取设备绑定表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<DeviceBindings> Detail([FromQuery] QueryByIdDeviceBindingsInput input)
    {
        return await _deviceBindingsRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加设备绑定表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加设备绑定表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddDeviceBindingsInput input)
    {
        var entity = input.Adapt<DeviceBindings>();
        return await _deviceBindingsRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新设备绑定表 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新设备绑定表")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateDeviceBindingsInput input)
    {
        var entity = input.Adapt<DeviceBindings>();
        await _deviceBindingsRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除设备绑定表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除设备绑定表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteDeviceBindingsInput input)
    {
        var entity = await _deviceBindingsRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _deviceBindingsRep.FakeDeleteAsync(entity);   //假删除
        //await _deviceBindingsRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除设备绑定表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除设备绑定表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteDeviceBindingsInput> input)
    {
        var exp = Expressionable.Create<DeviceBindings>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _deviceBindingsRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _deviceBindingsRep.FakeDeleteAsync(list);   //假删除
        //return await _deviceBindingsRep.DeleteAsync(list);   //真删除
    }
}
