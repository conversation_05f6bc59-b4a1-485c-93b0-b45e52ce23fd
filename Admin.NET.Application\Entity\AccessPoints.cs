﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using SqlSugar;
namespace Admin.NET.Application.Entity;

/// <summary>
/// 接入点/网关表
/// </summary>
[Tenant("1300000000001")]
[SugarTable("access_points", "接入点/网关表")]
public partial class AccessPoints : EntityBaseDel
{
    /// <summary>
    /// AP名称
    /// </summary>
    [SugarColumn(ColumnName = "ap_name", ColumnDescription = "AP名称", Length = 200)]
    public virtual string? ap_name { get; set; }
    
    /// <summary>
    /// 物理地址
    /// </summary>
    [SugarColumn(ColumnName = "mac_address", ColumnDescription = "物理地址", Length = 17)]
    public virtual string? mac_address { get; set; }
    
    /// <summary>
    /// IP地址
    /// </summary>
    [SugarColumn(ColumnName = "ip_address", ColumnDescription = "IP地址", Length = 15)]
    public virtual string? ip_address { get; set; }
    
    /// <summary>
    /// ap地址
    /// </summary>
    [SugarColumn(ColumnName = "ap_location", ColumnDescription = "ap地址", Length = 300)]
    public virtual string? ap_location { get; set; }
    
    /// <summary>
    /// ap状态1：在线；2：离线；3：异常；
    /// </summary>
    [SugarColumn(ColumnName = "ap_status", ColumnDescription = "ap状态1：在线；2：离线；3：异常；")]
    public virtual int? ap_status { get; set; }
    
    /// <summary>
    /// 固件版本
    /// </summary>
    [SugarColumn(ColumnName = "firmware_version", ColumnDescription = "固件版本", Length = 50)]
    public virtual string? firmware_version { get; set; }
    
    /// <summary>
    /// 信号强度
    /// </summary>
    [SugarColumn(ColumnName = "signal_strength", ColumnDescription = "信号强度")]
    public virtual int? signal_strength { get; set; }
    
    /// <summary>
    /// 链接设备连接数
    /// </summary>
    [SugarColumn(ColumnName = "connected_devices_count", ColumnDescription = "链接设备连接数")]
    public virtual int? connected_devices_count { get; set; }
    
    /// <summary>
    /// 最大链接数
    /// </summary>
    [SugarColumn(ColumnName = "max_devices", ColumnDescription = "最大链接数")]
    public virtual int? max_devices { get; set; }
    
}
