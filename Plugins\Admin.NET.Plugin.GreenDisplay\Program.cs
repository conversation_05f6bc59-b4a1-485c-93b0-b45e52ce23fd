// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Plugin.GreenDisplay;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Admin.NET.Plugin.GreenDisplay;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== RefitJsonHandler 测试 ===");
        Console.WriteLine();

        // 创建测试用的拦截器
        var interceptor = new TestInterceptor();
        var refitHandler = new RefitJsonHandler()
        {
            InnerHandler = interceptor
        };

        var httpClient = new HttpClient(refitHandler);

        // 测试1: JSON内容
        Console.WriteLine("测试1: JSON内容");
        var jsonContent = new StringContent(
            "{\"username\":\"20250529002\",\"password\":\"20250529002\"}", 
            Encoding.UTF8, 
            "text/plain" // 故意设置错误的Content-Type
        );
        
        var request1 = new HttpRequestMessage(HttpMethod.Post, "http://test.com/api/login")
        {
            Content = jsonContent
        };

        Console.WriteLine($"处理前 Content-Type: {request1.Content.Headers.ContentType}");
        
        await httpClient.SendAsync(request1);
        
        Console.WriteLine($"处理后 Content-Type: {interceptor.LastRequest?.Content?.Headers.ContentType}");
        Console.WriteLine($"请求体: {await interceptor.LastRequest?.Content?.ReadAsStringAsync()!}");
        Console.WriteLine();

        // 测试2: 非JSON内容
        Console.WriteLine("测试2: 非JSON内容");
        var formContent = new StringContent(
            "username=test&password=test", 
            Encoding.UTF8, 
            "application/x-www-form-urlencoded"
        );
        
        var request2 = new HttpRequestMessage(HttpMethod.Post, "http://test.com/api/login")
        {
            Content = formContent
        };

        Console.WriteLine($"处理前 Content-Type: {request2.Content.Headers.ContentType}");
        
        await httpClient.SendAsync(request2);
        
        Console.WriteLine($"处理后 Content-Type: {interceptor.LastRequest?.Content?.Headers.ContentType}");
        Console.WriteLine($"请求体: {await interceptor.LastRequest?.Content?.ReadAsStringAsync()!}");
        
        Console.WriteLine();
        Console.WriteLine("测试完成！");
    }
}

/// <summary>
/// 测试拦截器
/// </summary>
public class TestInterceptor : HttpMessageHandler
{
    public HttpRequestMessage? LastRequest { get; private set; }

    protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        LastRequest = request;
        
        // 返回模拟响应
        var response = new HttpResponseMessage(System.Net.HttpStatusCode.OK)
        {
            Content = new StringContent("{\"success\": true}", Encoding.UTF8, "application/json")
        };
        
        return Task.FromResult(response);
    }
}