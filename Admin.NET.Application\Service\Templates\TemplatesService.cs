﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Mapster;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Application.Entity;
namespace Admin.NET.Application;

/// <summary>
/// 模板管理模块服务 🧩
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public partial class TemplatesService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<Templates> _templatesRep;

    public TemplatesService(SqlSugarRepository<Templates> templatesRep)
    {
        _templatesRep = templatesRep;
    }

    /// <summary>
    /// 分页查询模板管理模块 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询模板管理模块")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<TemplatesOutput>> Page(PageTemplatesInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _templatesRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.template_name.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.template_name), u => u.template_name.Contains(input.template_name.Trim()))
            .WhereIF(input.template_type != null, u => u.template_type == input.template_type)
            .WhereIF(input.template_status != null, u => u.template_status == input.template_status)
            .Select<TemplatesOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取模板管理模块详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取模板管理模块详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<Templates> Detail([FromQuery] QueryByIdTemplatesInput input)
    {
        return await _templatesRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加模板管理模块 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加模板管理模块")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddTemplatesInput input)
    {
        var entity = input.Adapt<Templates>();
        return await _templatesRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新模板管理模块 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新模板管理模块")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateTemplatesInput input)
    {
        var entity = input.Adapt<Templates>();
        await _templatesRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除模板管理模块 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除模板管理模块")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteTemplatesInput input)
    {
        var entity = await _templatesRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _templatesRep.FakeDeleteAsync(entity);   //假删除
        //await _templatesRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除模板管理模块 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除模板管理模块")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteTemplatesInput> input)
    {
        var exp = Expressionable.Create<Templates>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _templatesRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _templatesRep.FakeDeleteAsync(list);   //假删除
        //return await _templatesRep.DeleteAsync(list);   //真删除
    }
}
