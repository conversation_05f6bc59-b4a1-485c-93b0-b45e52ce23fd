// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Plugin.GreenDisplay.Service;

/// <summary>
/// 通用API响应
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class GreenDisplayResponse<T>
{
    /// <summary>
    /// 状态码
    /// </summary>
    public int Code { get; set; }

    /// <summary>
    /// 消息
    /// </summary>
    public string msg { get; set; }

    /// <summary>
    /// 数据
    /// </summary>
    public T Data { get; set; }

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success => Code == 0;
}

/// <summary>
/// 分页响应
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class PagedResponse<T>
{
    /// <summary>
    /// 总记录数
    /// </summary>
    public int Total { get; set; }

    /// <summary>
    /// 当前页码
    /// </summary>
    public int PageNo { get; set; }

    /// <summary>
    /// 每页记录数
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// 总页数
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// 数据列表
    /// </summary>
    public List<T> list { get; set; }
}

/// <summary>
/// 设备信息
/// </summary>
public class DeviceInfo
{
    /// <summary>
    /// 设备MAC地址
    /// </summary>
    public string Mac { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 设备状态
    /// </summary>
    public string Status { get; set; }

    /// <summary>
    /// 电池电量
    /// </summary>
    public int Battery { get; set; }

    /// <summary>
    /// 信号强度
    /// </summary>
    public int Rssi { get; set; }

    /// <summary>
    /// 版本号
    /// </summary>
    public string Version { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public long LastUpdateTime { get; set; }
}

/// <summary>
/// 会议室信息
/// </summary>
public class MeetingRoomInfo
{
    /// <summary>
    /// 会议室ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 会议室名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 模板ID
    /// </summary>
    public int? TemplateId { get; set; }

    /// <summary>
    /// 桌牌MAC地址
    /// </summary>
    public string LabelMac { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public long CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public long UpdateTime { get; set; }
}

/// <summary>
/// 会议人员信息
/// </summary>
public class MeetingStaffInfo
{
    /// <summary>
    /// 人员ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 人员编号
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string Mobile { get; set; }

    /// <summary>
    /// 公司
    /// </summary>
    public string Company { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string Position { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public long CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public long UpdateTime { get; set; }
}