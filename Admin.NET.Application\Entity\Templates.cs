﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using SqlSugar;
namespace Admin.NET.Application.Entity;

/// <summary>
/// 模板管理模块
/// </summary>
[Tenant("1300000000001")]
[SugarTable("templates", "模板管理模块")]
public partial class Templates : EntityBaseDel
{
    /// <summary>
    /// 模版名称
    /// </summary>
    [SugarColumn(ColumnName = "template_name", ColumnDescription = "模版名称", Length = 200)]
    public virtual string? template_name { get; set; }
    
    /// <summary>
    /// 1：姓名牌；2：会议牌；3：自定义牌
    /// </summary>
    [SugarColumn(ColumnName = "template_type", ColumnDescription = "1：姓名牌；2：会议牌；3：自定义牌")]
    public virtual int? template_type { get; set; }
    
    /// <summary>
    /// 设计内容
    /// </summary>
    [SugarColumn(IsJson = true, ColumnName = "design_data", ColumnDescription = "设计内容"),]
    public virtual object? design_data { get; set; }
    
    /// <summary>
    /// 预览图片
    /// </summary>
    [SugarColumn(ColumnName = "preview_image", ColumnDescription = "预览图片", Length = 500)]
    public virtual string? preview_image { get; set; }
    
    /// <summary>
    /// 宽度
    /// </summary>
    [SugarColumn(ColumnName = "width", ColumnDescription = "宽度")]
    public virtual int? width { get; set; }
    
    /// <summary>
    /// 高度
    /// </summary>
    [SugarColumn(ColumnName = "height", ColumnDescription = "高度")]
    public virtual int? height { get; set; }
    
    /// <summary>
    /// 是否默认
    /// </summary>
    [SugarColumn(ColumnName = "is_default", ColumnDescription = "是否默认")]
    public virtual bool? is_default { get; set; }
    
    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(ColumnName = "template_status", ColumnDescription = "是否启用")]
    public virtual int? template_status { get; set; }
    
}
