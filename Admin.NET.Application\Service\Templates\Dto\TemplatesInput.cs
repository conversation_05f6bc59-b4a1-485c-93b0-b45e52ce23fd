﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

namespace Admin.NET.Application;

/// <summary>
/// 模板管理模块基础输入参数
/// </summary>
public class TemplatesBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 模版名称
    /// </summary>
    public virtual string? template_name { get; set; }
    
    /// <summary>
    /// 1：姓名牌；2：会议牌；3：自定义牌
    /// </summary>
    public virtual int? template_type { get; set; }
    
    /// <summary>
    /// 设计内容
    /// </summary>
    public virtual string? design_data { get; set; }
    
    /// <summary>
    /// 预览图片
    /// </summary>
    public virtual string? preview_image { get; set; }
    
    /// <summary>
    /// 宽度
    /// </summary>
    public virtual int? width { get; set; }
    
    /// <summary>
    /// 高度
    /// </summary>
    public virtual int? height { get; set; }
    
    /// <summary>
    /// 是否默认
    /// </summary>
    public virtual bool? is_default { get; set; }
    
    /// <summary>
    /// 是否启用
    /// </summary>
    public virtual int? template_status { get; set; }
    
}

/// <summary>
/// 模板管理模块分页查询输入参数
/// </summary>
public class PageTemplatesInput : BasePageInput
{
    /// <summary>
    /// 模版名称
    /// </summary>
    public string? template_name { get; set; }
    
    /// <summary>
    /// 1：姓名牌；2：会议牌；3：自定义牌
    /// </summary>
    public int? template_type { get; set; }
    
    /// <summary>
    /// 是否启用
    /// </summary>
    public int? template_status { get; set; }
    
}

/// <summary>
/// 模板管理模块增加输入参数
/// </summary>
public class AddTemplatesInput
{
    /// <summary>
    /// 模版名称
    /// </summary>
    [MaxLength(200, ErrorMessage = "模版名称字符长度不能超过200")]
    public string? template_name { get; set; }
    
    /// <summary>
    /// 1：姓名牌；2：会议牌；3：自定义牌
    /// </summary>
    public int? template_type { get; set; }
    
    /// <summary>
    /// 设计内容
    /// </summary>
    [MaxLength(1, ErrorMessage = "设计内容字符长度不能超过1")]
    public string? design_data { get; set; }
    
    /// <summary>
    /// 预览图片
    /// </summary>
    [MaxLength(500, ErrorMessage = "预览图片字符长度不能超过500")]
    public string? preview_image { get; set; }
    
    /// <summary>
    /// 宽度
    /// </summary>
    public int? width { get; set; }
    
    /// <summary>
    /// 高度
    /// </summary>
    public int? height { get; set; }
    
    /// <summary>
    /// 是否默认
    /// </summary>
    public bool? is_default { get; set; }
    
    /// <summary>
    /// 是否启用
    /// </summary>
    public int? template_status { get; set; }
    
}

/// <summary>
/// 模板管理模块删除输入参数
/// </summary>
public class DeleteTemplatesInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 模板管理模块更新输入参数
/// </summary>
public class UpdateTemplatesInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 模版名称
    /// </summary>    
    [MaxLength(200, ErrorMessage = "模版名称字符长度不能超过200")]
    public string? template_name { get; set; }
    
    /// <summary>
    /// 1：姓名牌；2：会议牌；3：自定义牌
    /// </summary>    
    public int? template_type { get; set; }
    
    /// <summary>
    /// 设计内容
    /// </summary>    
    [MaxLength(1, ErrorMessage = "设计内容字符长度不能超过1")]
    public string? design_data { get; set; }
    
    /// <summary>
    /// 预览图片
    /// </summary>    
    [MaxLength(500, ErrorMessage = "预览图片字符长度不能超过500")]
    public string? preview_image { get; set; }
    
    /// <summary>
    /// 宽度
    /// </summary>    
    public int? width { get; set; }
    
    /// <summary>
    /// 高度
    /// </summary>    
    public int? height { get; set; }
    
    /// <summary>
    /// 是否默认
    /// </summary>    
    public bool? is_default { get; set; }
    
    /// <summary>
    /// 是否启用
    /// </summary>    
    public int? template_status { get; set; }
    
}

/// <summary>
/// 模板管理模块主键查询输入参数
/// </summary>
public class QueryByIdTemplatesInput : DeleteTemplatesInput
{
}

