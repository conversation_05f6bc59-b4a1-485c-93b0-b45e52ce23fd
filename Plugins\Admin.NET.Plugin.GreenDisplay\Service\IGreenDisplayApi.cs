// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Plugin.GreenDisplay.Service;

/// <summary>
/// 蓝牙桌牌API接口
/// </summary>
[HttpClientName("GreenDisplay")]
public interface IGreenDisplayApi : IHttpDeclarative
{
    #region 认证相关

    /// <summary>
    /// 用户登录认证
    /// </summary>
    /// <param name="input">登录信息</param>
    /// <returns>认证结果</returns>
    [Post("/admin-api/api/auth/login")]
    Task<GreenDisplayResponse<GreenDisplayLoginOutput>> LoginAsync([Body] GreenDisplayLoginInput input);

    #endregion

    #region 设备管理

    /// <summary>
    /// 绑定多个标签
    /// </summary>
    /// <param name="input">绑定信息</param>
    /// <returns>绑定结果</returns>
    [Post("/admin-api/api/device/label/bind_multi")]
    Task<GreenDisplayResponse<object>> BindMultipleLabelsAsync([Body] BindMultipleLabelsInput input, [Header("Authorization")] string authorization);

    /// <summary>
    /// 绑定标签数据
    /// </summary>
    /// <param name="input">绑定数据信息</param>
    /// <returns>绑定结果</returns>
    [Post("/admin-api/api/device/label/bind_data_multi")]
    Task<GreenDisplayResponse<object>> BindDataMultipleAsync([Body] BindDataMultipleInput input, [Header("Authorization")] string authorization);

    /// <summary>
    /// 闪烁标签
    /// </summary>
    /// <param name="input">闪烁信息</param>
    /// <returns>操作结果</returns>
    [Post("/admin-api/api/device/label/flash")]
    Task<GreenDisplayResponse<object>> FlashLabelAsync([Body] FlashLabelInput input, [Header("Authorization")] string authorization);

    /// <summary>
    /// 获取单个设备信息
    /// </summary>
    /// <param name="mac">设备MAC地址</param>
    /// <returns>设备信息</returns>
    [Get("/admin-api/api/device/label/get")]
    Task<GreenDisplayResponse<DeviceInfo>> GetDeviceAsync([Query] string mac, [Header("Authorization")] string authorization);

    /// <summary>
    /// 查询所有设备
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>设备列表</returns>
    [Post("/admin-api/api/device/label/query")]
    Task<GreenDisplayResponse<PagedResponse<DeviceInfo>>> QueryDevicesAsync([Body] QueryDevicesInput input, [Header("Authorization")] string authorization);

    /// <summary>
    /// 更新标签
    /// </summary>
    /// <param name="input">更新信息</param>
    /// <returns>更新结果</returns>
    [Post("/admin-api/api/device/label/update")]
    Task<GreenDisplayResponse<object>> UpdateLabelAsync([Body] UpdateLabelInput input, [Header("Authorization")] string authorization);

    #endregion

    #region 会议管理

    /// <summary>
    /// 创建会议室
    /// </summary>
    /// <param name="input">会议室信息</param>
    /// <returns>创建结果</returns>
    [Post("/admin-api/meeting/room/create")]
    Task<GreenDisplayResponse<int>> CreateMeetingRoomAsync([Body] CreateMeetingRoomInput input, [Header("Authorization")] string authorization);

    /// <summary>
    /// 查询会议室
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>会议室列表</returns>
    [Post("/admin-api/meeting/room/query")]
    Task<GreenDisplayResponse<PagedResponse<MeetingRoomInfo>>> QueryMeetingRoomsAsync([Body] QueryMeetingRoomsInput input, [Header("Authorization")] string authorization);

    /// <summary>
    /// 更新会议室
    /// </summary>
    /// <param name="input">更新信息</param>
    /// <returns>更新结果</returns>
    [Put("/admin-api/meeting/room/update")]
    Task<GreenDisplayResponse<object>> UpdateMeetingRoomAsync([Body] UpdateMeetingRoomInput input, [Header("Authorization")] string authorization);

    /// <summary>
    /// 删除会议室
    /// </summary>
    /// <param name="id">会议室ID</param>
    /// <returns>删除结果</returns>
    [Delete("/admin-api/meeting/room/delete")]
    Task<GreenDisplayResponse<object>> DeleteMeetingRoomAsync([Query] int id, [Header("Authorization")] string authorization);

    /// <summary>
    /// 创建会议人员
    /// </summary>
    /// <param name="input">人员信息</param>
    /// <returns>创建结果，包含新增的人员ID</returns>
    [Post("/admin-api/meeting/staff/create")]
    Task<GreenDisplayResponse<int>> CreateMeetingStaffAsync([Body] CreateMeetingStaffInput input, [Header("Authorization")] string authorization);

    /// <summary>
    /// 查询会议人员
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>人员列表</returns>
    [Post("/admin-api/meeting/staff/query")]
    Task<GreenDisplayResponse<PagedResponse<MeetingStaffInfo>>> QueryMeetingStaffAsync([Body] QueryMeetingStaffInput input, [Header("Authorization")] string authorization);

    /// <summary>
    /// 更新会议人员
    /// </summary>
    /// <param name="input">更新信息</param>
    /// <returns>更新结果</returns>
    [Post("/admin-api/api/meeting/staff/update")]
    Task<GreenDisplayResponse<object>> UpdateMeetingStaffAsync([Body] UpdateMeetingStaffInput input, [Header("Authorization")] string authorization);

    /// <summary>
    /// 删除会议人员
    /// </summary>
    /// <param name="id">人员ID</param>
    /// <returns>删除结果</returns>
    [Delete("/admin-api/meeting/staff/delete")]
    Task<GreenDisplayResponse<object>> DeleteMeetingStaffAsync([Query] int id, [Header("Authorization")] string authorization);

    #endregion

    #region 模板管理

    /// <summary>
    /// 查询模板
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>模板列表</returns>
    [Post("/admin-api/api/resource/template/query")]
    Task<GreenDisplayResponse<PagedResponse<TemplateOutput>>> QueryTemplatesAsync([Body] QueryTemplatesInput input, [Header("Authorization")] string authorization);

    /// <summary>
    /// 创建模板
    /// </summary>
    /// <param name="input">模板信息</param>
    /// <returns>创建结果</returns>
    [Post("/admin-api/api/resource/template/create")]
    Task<GreenDisplayResponse<object>> CreateTemplateAsync([Body] CreateTemplateInput input, [Header("Authorization")] string authorization);

    #endregion

    #region AP管理

    /// <summary>
    /// 获取网关列表
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>网关列表</returns>
    [Get("/admin-api/device/ap/page")]
    Task<GreenDisplayResponse<PagedResponse<APOutput>>> GetAPListAsync([Body] QueryAPInput input, [Header("Authorization")] string authorization);

    /// <summary>
    /// 创建网关
    /// </summary>
    /// <param name="input">网关信息</param>
    /// <returns>创建结果</returns>
    [Post("/admin-api/device/ap/create")]
    Task<GreenDisplayResponse<object>> CreateAPAsync([Body] CreateAPInput input, [Header("Authorization")] string authorization);

    /// <summary>
    /// 根据网关MAC获取网关基本信息
    /// </summary>
    /// <param name="apMac">网关MAC地址</param>
    /// <returns>网关信息</returns>
    [Get("/admin-api/device/ap/get")]
    Task<GreenDisplayResponse<APOutput>> GetAPAsync([Query] string apMac, [Header("Authorization")] string authorization);

    /// <summary>
    /// 修改网关信息
    /// </summary>
    /// <param name="input">更新信息</param>
    /// <returns>更新结果</returns>
    [Put("/admin-api/device/ap/update")]
    Task<GreenDisplayResponse<object>> UpdateAPAsync([Body] UpdateAPInput input, [Header("Authorization")] string authorization);

    /// <summary>
    /// 删除网关
    /// </summary>
    /// <param name="apMac">网关MAC地址</param>
    /// <returns>删除结果</returns>
    [Delete("/admin-api/device/ap/delete")]
    Task<GreenDisplayResponse<object>> DeleteAPAsync([Query] string apMac, [Header("Authorization")] string authorization);

    #endregion
}