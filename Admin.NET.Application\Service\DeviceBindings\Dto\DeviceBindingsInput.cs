﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

namespace Admin.NET.Application;

/// <summary>
/// 设备绑定表基础输入参数
/// </summary>
public class DeviceBindingsBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 设备ID
    /// </summary>
    public virtual long? device_id { get; set; }
    
    /// <summary>
    /// 员工ID
    /// </summary>
    public virtual long? staff_id { get; set; }
    
    /// <summary>
    /// 模板ID
    /// </summary>
    public virtual long? template_id { get; set; }
    
    /// <summary>
    /// 会议室ID
    /// </summary>
    public virtual long? meeting_room_id { get; set; }
    
    /// <summary>
    /// 绑定类型
    /// </summary>
    public virtual int? binding_type { get; set; }
    
    /// <summary>
    /// 绑定数据
    /// </summary>
    public virtual string? binding_data { get; set; }
    
}

/// <summary>
/// 设备绑定表分页查询输入参数
/// </summary>
public class PageDeviceBindingsInput : BasePageInput
{
    /// <summary>
    /// 绑定类型
    /// </summary>
    public int? binding_type { get; set; }
    
}

/// <summary>
/// 设备绑定表增加输入参数
/// </summary>
public class AddDeviceBindingsInput
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public long? device_id { get; set; }
    
    /// <summary>
    /// 员工ID
    /// </summary>
    public long? staff_id { get; set; }
    
    /// <summary>
    /// 模板ID
    /// </summary>
    public long? template_id { get; set; }
    
    /// <summary>
    /// 会议室ID
    /// </summary>
    public long? meeting_room_id { get; set; }
    
    /// <summary>
    /// 绑定类型
    /// </summary>
    public int? binding_type { get; set; }
    
    /// <summary>
    /// 绑定数据
    /// </summary>
    [MaxLength(1, ErrorMessage = "绑定数据字符长度不能超过1")]
    public string? binding_data { get; set; }
    
}

/// <summary>
/// 设备绑定表删除输入参数
/// </summary>
public class DeleteDeviceBindingsInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 设备绑定表更新输入参数
/// </summary>
public class UpdateDeviceBindingsInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 设备ID
    /// </summary>    
    public long? device_id { get; set; }
    
    /// <summary>
    /// 员工ID
    /// </summary>    
    public long? staff_id { get; set; }
    
    /// <summary>
    /// 模板ID
    /// </summary>    
    public long? template_id { get; set; }
    
    /// <summary>
    /// 会议室ID
    /// </summary>    
    public long? meeting_room_id { get; set; }
    
    /// <summary>
    /// 绑定类型
    /// </summary>    
    public int? binding_type { get; set; }
    
    /// <summary>
    /// 绑定数据
    /// </summary>    
    [MaxLength(1, ErrorMessage = "绑定数据字符长度不能超过1")]
    public string? binding_data { get; set; }
    
}

/// <summary>
/// 设备绑定表主键查询输入参数
/// </summary>
public class QueryByIdDeviceBindingsInput : DeleteDeviceBindingsInput
{
}

