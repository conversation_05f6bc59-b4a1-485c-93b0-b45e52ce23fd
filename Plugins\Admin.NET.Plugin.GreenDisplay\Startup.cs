// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Plugin.GreenDisplay.Options;
using Admin.NET.Plugin.GreenDisplay.Service;
using Furion;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging; 
using Polly;
using Polly.Extensions.Http;
using System;
using System.Net.Http;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace Admin.NET.Plugin.GreenDisplay;

/// <summary>
/// GreenDisplay插件启动配置
/// </summary>
public class Startup : AppStartup
{
    /// <summary>
    /// 配置服务
    /// </summary>
    /// <param name="services">服务集合</param>
    public void ConfigureServices(IServiceCollection services)
    {
        // 注册配置选项
        services.AddConfigurableOptions<GreenDisplayOptions>();

        // 获取配置选项
        var options = App.GetOptions<GreenDisplayOptions>();
        
        // 配置验证
        ValidateGreenDisplayOptions(options);
        
        // 配置HttpClient
        services.AddHttpClient("GreenDisplay", client =>
        {
            client.BaseAddress = new Uri(options.BaseUrl);
            client.Timeout = TimeSpan.FromSeconds(options.TimeoutSeconds);
             
            // 添加User-Agent标识
            client.DefaultRequestHeaders.UserAgent.ParseAdd("GreenDisplay-Client/1.0");
        })
        // 添加Refit JSON处理器，确保正确的JSON序列化和Content-Type
        .AddHttpMessageHandler(() => new RefitJsonHandler())
        // 添加重试策略
        .AddPolicyHandler(GetRetryPolicy())
        // 添加超时策略
        .AddPolicyHandler(Policy.TimeoutAsync<HttpResponseMessage>(TimeSpan.FromSeconds(options.TimeoutSeconds + 10)))
        .ConfigurePrimaryHttpMessageHandler(() =>
        {
            var handler = new HttpClientHandler();
            
            // 根据配置决定是否验证SSL证书
            if (!options.EnableSslVerification)
            {
                handler.ServerCertificateCustomValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;
            }
            
            return handler;
        });

        // 注册缓存服务（如果需要）
        services.AddMemoryCache();

        // 注册API服务
        services.AddHttpRemote(builder =>
        {
            builder.AddHttpDeclarative<IGreenDisplayApi>();
        });
        
        // 配置全局JSON序列化器为Newtonsoft.Json
        services.Configure<MvcNewtonsoftJsonOptions>(options =>
        {
            options.SerializerSettings.DateFormatString = "yyyy-MM-dd HH:mm:ss";
            options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
            options.SerializerSettings.ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver();
        });
        
        // 注册业务服务
        services.AddTransient<GreenDisplayService>();
    }

    /// <summary>
    /// 验证GreenDisplay配置选项
    /// </summary>
    /// <param name="options">配置选项</param>
    /// <exception cref="ArgumentException">配置无效时抛出异常</exception>
    private static void ValidateGreenDisplayOptions(GreenDisplayOptions options)
    {
        if (string.IsNullOrWhiteSpace(options.BaseUrl))
            throw new ArgumentException("GreenDisplay BaseUrl cannot be null or empty", nameof(options.BaseUrl));
        
        if (!Uri.TryCreate(options.BaseUrl, UriKind.Absolute, out _))
            throw new ArgumentException("GreenDisplay BaseUrl must be a valid absolute URI", nameof(options.BaseUrl));
        
        if (options.TimeoutSeconds <= 0)
            throw new ArgumentException("GreenDisplay TimeoutSeconds must be greater than 0", nameof(options.TimeoutSeconds));
        
        if (options.TimeoutSeconds > 300) // 5分钟
            throw new ArgumentException("GreenDisplay TimeoutSeconds should not exceed 300 seconds", nameof(options.TimeoutSeconds));
    }

    /// <summary>
    /// 获取HTTP重试策略
    /// </summary>
    /// <returns>重试策略</returns>
    private static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy()
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError() // 处理HttpRequestException和5XX、408状态码
            .OrResult(msg => !msg.IsSuccessStatusCode) // 处理其他非成功状态码
            .WaitAndRetryAsync(
                retryCount: 3,
                sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), // 指数退避
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    var logger = App.GetService<ILogger<Startup>>();
                    logger?.LogWarning("GreenDisplay HTTP请求重试 {RetryCount}/3, 延迟 {Delay}ms, 原因: {Reason}",
                        retryCount, timespan.TotalMilliseconds, outcome.Exception?.Message ?? outcome.Result?.StatusCode.ToString());
                });
    }
}