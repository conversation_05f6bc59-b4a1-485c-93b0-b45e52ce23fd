// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Plugin.GreenDisplay.Service;

/// <summary>
/// 模板查询输入
/// </summary>
public class QueryTemplatesInput
{
    /// <summary>
    /// 页码
    /// </summary>
    public int PageNo { get; set; } = 1;

    /// <summary>
    /// 每页记录数
    /// </summary>
    public int PageSize { get; set; } = 100;
}

/// <summary>
/// 模板创建输入
/// </summary>
public class CreateTemplateInput
{
    /// <summary>
    /// 模板名称
    /// </summary>
    public string TemplateName { get; set; }

    /// <summary>
    /// 模板分类
    /// </summary>
    public string TemplateCategory { get; set; }

    /// <summary>
    /// 模板型号
    /// </summary>
    public string TemplateModel { get; set; }

    /// <summary>
    /// 模板方向
    /// </summary>
    public int TemplateDirection { get; set; }

    /// <summary>
    /// 是否可用
    /// </summary>
    public bool TemplateUsable { get; set; }

    /// <summary>
    /// 模板图片
    /// </summary>
    public string TemplatePicture { get; set; }

    /// <summary>
    /// 组织ID
    /// </summary>
    public int OrganizationId { get; set; }

    /// <summary>
    /// 模板JSON配置
    /// </summary>
    public string TemplateJson { get; set; }
}

/// <summary>
/// 模板输出
/// </summary>
public class TemplateOutput
{
    /// <summary>
    /// 模板ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 模板名称
    /// </summary>
    public string TemplateName { get; set; }

    /// <summary>
    /// 模板分类
    /// </summary>
    public string TemplateCategory { get; set; }

    /// <summary>
    /// 模板型号
    /// </summary>
    public string TemplateModel { get; set; }

    /// <summary>
    /// 模板方向
    /// </summary>
    public int TemplateDirection { get; set; }

    /// <summary>
    /// 是否可用
    /// </summary>
    public bool TemplateUsable { get; set; }

    /// <summary>
    /// 模板图片
    /// </summary>
    public string TemplatePicture { get; set; }

    /// <summary>
    /// 组织ID
    /// </summary>
    public int OrganizationId { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public long CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public long UpdateTime { get; set; }

    /// <summary>
    /// 模板JSON配置
    /// </summary>
    public string TemplateJson { get; set; }
}