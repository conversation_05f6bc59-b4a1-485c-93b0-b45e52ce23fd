﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using SqlSugar;
namespace Admin.NET.Application.Entity;

/// <summary>
/// 设备绑定表
/// </summary>
[Tenant("1300000000001")]
[SugarTable("device_bindings", "设备绑定表")]
public partial class DeviceBindings : EntityBaseDel
{
    /// <summary>
    /// 设备ID
    /// </summary>
    [SugarColumn(ColumnName = "device_id", ColumnDescription = "设备ID")]
    public virtual long? device_id { get; set; }
    
    /// <summary>
    /// 员工ID
    /// </summary>
    [SugarColumn(ColumnName = "staff_id", ColumnDescription = "员工ID")]
    public virtual long? staff_id { get; set; }
    
    /// <summary>
    /// 模板ID
    /// </summary>
    [SugarColumn(ColumnName = "template_id", ColumnDescription = "模板ID")]
    public virtual long? template_id { get; set; }
    
    /// <summary>
    /// 会议室ID
    /// </summary>
    [SugarColumn(ColumnName = "meeting_room_id", ColumnDescription = "会议室ID")]
    public virtual long? meeting_room_id { get; set; }
    
    /// <summary>
    /// 1、员工；2、房间；3：模版；
    /// </summary>
    [SugarColumn(ColumnName = "binding_type", ColumnDescription = "1、员工；2、房间；3：模版；")]
    public virtual int? binding_type { get; set; }
    
    /// <summary>
    /// 绑定数据
    /// </summary>
    [SugarColumn(IsJson = true, ColumnName = "binding_data", ColumnDescription = "绑定数据")]
    public virtual object? binding_data { get; set; }
    
}
