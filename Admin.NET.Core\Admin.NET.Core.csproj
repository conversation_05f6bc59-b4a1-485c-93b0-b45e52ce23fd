<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
    <NoWarn>1701;1702;1591;8632</NoWarn>
    <DocumentationFile></DocumentationFile>
    <ImplicitUsings>enable</ImplicitUsings>
    <PreserveCompilationContext>true</PreserveCompilationContext>
    <Nullable>disable</Nullable>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <Copyright>Admin.NET</Copyright>
    <Description>Admin.NET 通用权限开发平台</Description>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AlibabaCloud.SDK.Dysmsapi20170525" Version="4.0.0" />
    <PackageReference Include="AlipaySDKNet.Standard" Version="4.9.627" />
    <PackageReference Include="AngleSharp" Version="1.3.0" />
    <PackageReference Include="AspectCore.Extensions.Reflection" Version="2.4.0" />
    <PackageReference Include="AspNetCoreRateLimit" Version="5.0.0" />
    <PackageReference Include="Elastic.Clients.Elasticsearch" Version="9.0.7" />
    <PackageReference Include="Furion.Extras.Authentication.JwtBearer" Version="********" />
    <PackageReference Include="Furion.Extras.ObjectMapper.Mapster" Version="********" />
    <PackageReference Include="Furion.Pure" Version="********" />
	<PackageReference Include="Hardware.Info" Version="*********" />
    <PackageReference Include="Hashids.net" Version="1.7.0" />
    <PackageReference Include="IPTools.China" Version="1.6.0" />
    <PackageReference Include="IPTools.International" Version="1.6.0" />
    <PackageReference Include="log4net" Version="3.1.0" />
    <PackageReference Include="Magicodes.IE.Excel" Version="2.7.6" />
    <PackageReference Include="Magicodes.IE.Pdf" Version="2.7.6" />
    <PackageReference Include="Magicodes.IE.Word" Version="2.7.6" />
    <PackageReference Include="MailKit" Version="4.13.0" />
    <PackageReference Include="MiniExcel" Version="1.41.3" />
    <PackageReference Include="MiniWord" Version="0.9.2" />
    <PackageReference Include="NewLife.Redis" Version="6.3.2025.701" />
    <PackageReference Include="Novell.Directory.Ldap.NETStandard" Version="4.0.0" />
	<PackageReference Include="OnceMi.AspNetCore.OSS" Version="1.2.0" />
    <PackageReference Include="QRCoder" Version="1.6.0" />
    <PackageReference Include="RabbitMQ.Client" Version="7.1.2" />
    <PackageReference Include="SixLabors.ImageSharp.Web" Version="3.1.5" />
    <PackageReference Include="SKIT.FlurlHttpClient.Wechat.Api" Version="3.10.0" />
    <PackageReference Include="SKIT.FlurlHttpClient.Wechat.TenpayV3" Version="3.12.0" />
    <PackageReference Include="SqlSugarCore" Version="5.1.4.197" />
    <PackageReference Include="SSH.NET" Version="2025.0.0" />
    <PackageReference Include="System.Linq.Dynamic.Core" Version="1.6.6" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="System.Private.Uri" Version="4.3.2" />
    <PackageReference Include="TencentCloudSDK.Sms" Version="3.0.1273" />
    <PackageReference Include="UAParser" Version="3.1.47" />
    <PackageReference Include="Yitter.IdGenerator" Version="1.0.14" />
    <PackageReference Include="BouncyCastle.Cryptography" Version="2.6.1" Aliases="BouncyCastleV2" />
  </ItemGroup>

  <ItemGroup Condition=" '$(TargetFramework)' == 'net8.0' ">
    <PackageReference Include="AspNet.Security.OAuth.Gitee" Version="8.3.0" />
    <PackageReference Include="AspNet.Security.OAuth.Weixin" Version="8.3.0" />
    <PackageReference Include="Lazy.Captcha.Core" Version="2.0.9" />
    <PackageReference Include="Microsoft.AspNetCore.DataProtection.StackExchangeRedis" Version="8.0.11" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson" Version="8.0.11" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.StackExchangeRedis" Version="8.0.11" />
  </ItemGroup>

  <ItemGroup Condition=" '$(TargetFramework)' == 'net9.0' ">
    <PackageReference Include="AspNet.Security.OAuth.Gitee" Version="9.4.0" />
    <PackageReference Include="AspNet.Security.OAuth.Weixin" Version="9.4.0" />
    <PackageReference Include="Lazy.Captcha.Core" Version="2.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.DataProtection.StackExchangeRedis" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.StackExchangeRedis" Version="9.0.6" />
	<PackageReference Include="XiHan.Framework.Utils" Version="0.11.3" />
  </ItemGroup>

</Project>
