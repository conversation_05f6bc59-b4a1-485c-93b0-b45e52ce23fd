fail: 2025-07-11 11:34:33.3734204 +08:00 Friday L Admin.NET.Application.AccessPointsService[0] #5 '00-c73f95be77fe5ce31177aed4474d7be2-b50b4425344fab92-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      第三方平台更新失败，MAC地址: 25-52-55-55-55-55
fail: 2025-07-11 11:34:33.8872624 +08:00 Friday L Admin.NET.Application.AccessPointsService[0] #5 '00-c73f95be77fe5ce31177aed4474d7be2-b50b4425344fab92-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      更新接入点时发生异常，ID: 696476679385157      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Furion.FriendlyException.AppFriendlyException: 第三方平台更新失败
         at Admin.NET.Application.AccessPointsService.<>c__DisplayClass8_0.<<Update>b__0>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\AccessPointsService.cs:line 248
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-11 11:34:44.7533185 +08:00 Friday L Admin.NET.Application.AccessPointsService[0] #5 '00-2f2c8b43f211b5610c35274b434035aa-acb8b67f60d40d50-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      第三方平台更新失败，MAC地址: 25-52-55-55-55-55
fail: 2025-07-11 11:34:45.0131512 +08:00 Friday L Admin.NET.Application.AccessPointsService[0] #5 '00-2f2c8b43f211b5610c35274b434035aa-acb8b67f60d40d50-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      更新接入点时发生异常，ID: 696476679385157      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Furion.FriendlyException.AppFriendlyException: 第三方平台更新失败
         at Admin.NET.Application.AccessPointsService.<>c__DisplayClass8_0.<<Update>b__0>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\AccessPointsService.cs:line 248
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-11 11:35:35.4765295 +08:00 Friday L Admin.NET.Application.AccessPointsService[0] #23 '00-d7b13ffc67bdd743ecdc4609409378ba-da44d326d6d314f1-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      第三方平台更新失败，MAC地址: 25-52-55-55-55-55
fail: 2025-07-11 11:35:35.7805048 +08:00 Friday L Admin.NET.Application.AccessPointsService[0] #23 '00-d7b13ffc67bdd743ecdc4609409378ba-da44d326d6d314f1-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      更新接入点时发生异常，ID: 696476679385157      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Furion.FriendlyException.AppFriendlyException: 第三方平台更新失败
         at Admin.NET.Application.AccessPointsService.<>c__DisplayClass8_0.<<Update>b__0>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\AccessPointsService.cs:line 248
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-11 15:26:58.9074063 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #10 '00-f2a4f3376d062d57510f96941f2aa646-c5081fd4ee5b49d9-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      第三方平台更新会议室失败，会议室ID: 696539040927813, 名称: 测试车2025
fail: 2025-07-11 15:26:59.4094358 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #10 '00-f2a4f3376d062d57510f96941f2aa646-c5081fd4ee5b49d9-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      第三方平台同步失败，会议室ID: 696539040927813, 名称: 测试车2025      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Exception: 第三方平台同步失败
         at Admin.NET.Application.MeetingRoomsService.<>c__DisplayClass7_0.<<Update>b__0>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingRooms\MeetingRoomsService.cs:line 180
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-11 15:26:59.7564442 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #10 '00-f2a4f3376d062d57510f96941f2aa646-c5081fd4ee5b49d9-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      更新会议室事务失败，错误信息: 第三方平台同步失败
fail: 2025-07-11 15:29:19.3744202 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #50 '00-ad6856d3cdbb8bda416c8a9a9d117c79-c509e711f89f0a92-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      第三方平台更新会议室失败，会议室ID: 696539040927813, 名称: 测试车 2025
fail: 2025-07-11 15:29:19.7366151 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #50 '00-ad6856d3cdbb8bda416c8a9a9d117c79-c509e711f89f0a92-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      第三方平台同步失败，会议室ID: 696539040927813, 名称: 测试车 2025      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Exception: 第三方平台同步失败
         at Admin.NET.Application.MeetingRoomsService.<>c__DisplayClass7_0.<<Update>b__0>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingRooms\MeetingRoomsService.cs:line 180
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-11 15:29:20.0923073 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #50 '00-ad6856d3cdbb8bda416c8a9a9d117c79-c509e711f89f0a92-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      更新会议室事务失败，错误信息: 第三方平台同步失败
fail: 2025-07-11 16:35:22.8410256 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #20 '00-23803e95ce9e5459af96ca4d824c600a-3ea13faba058aac5-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      第三方平台更新会议室失败，会议室ID: 696539040927813, 名称: 测试车2025
fail: 2025-07-11 16:35:23.3455339 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #20 '00-23803e95ce9e5459af96ca4d824c600a-3ea13faba058aac5-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      第三方平台同步失败，会议室ID: 696539040927813, 名称: 测试车2025      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.Exception: 第三方平台同步失败
         at Admin.NET.Application.MeetingRoomsService.<>c__DisplayClass7_0.<<Update>b__0>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingRooms\MeetingRoomsService.cs:line 175
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-11 16:35:23.7868482 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #20 '00-23803e95ce9e5459af96ca4d824c600a-3ea13faba058aac5-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      更新会议室事务失败，错误信息: 第三方平台同步失败
fail: 2025-07-11 16:35:55.4496193 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #21 '00-37650e4984f6a88116adc8bb5c18056c-5533015d549dfb42-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      第三方平台同步失败，会议室ID: 696539040927813, 名称: 测试车       
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.ArgumentException: 会议室ID必须大于0 (Parameter 'id')
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.DeleteMeetingRoomAsync(Int32 id) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 572
         at Admin.NET.Application.MeetingRoomsService.<>c__DisplayClass8_0.<<Delete>b__1>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingRooms\MeetingRoomsService.cs:line 235
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-11 16:35:55.5871205 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #21 '00-37650e4984f6a88116adc8bb5c18056c-5533015d549dfb42-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      删除会议室事务失败，错误信息: 会议室ID必须大于0 (Parameter 'id')
fail: 2025-07-11 16:36:00.1592955 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #18 '00-3d43621513b08d570e752f1eb4729620-385e9afb186c661a-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      第三方平台同步失败，会议室ID: 696539040927813, 名称: 测试车       
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.ArgumentException: 会议室ID必须大于0 (Parameter 'id')
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.DeleteMeetingRoomAsync(Int32 id) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 572
         at Admin.NET.Application.MeetingRoomsService.<>c__DisplayClass8_0.<<Delete>b__1>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingRooms\MeetingRoomsService.cs:line 235
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-11 16:36:00.2765737 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #18 '00-3d43621513b08d570e752f1eb4729620-385e9afb186c661a-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      删除会议室事务失败，错误信息: 会议室ID必须大于0 (Parameter 'id')
fail: 2025-07-11 16:36:41.9627065 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #17 '00-e674e4eee007a033b52d69d8b5bb51ca-1b0990dce9c43a1b-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建会议室时发生异常，名称：21      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidCastException: Unable to cast object of type 'System.Text.Json.JsonElement' to type 'System.IConvertible'.
         at System.Convert.ToInt32(Object value)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateMeetingRoomAsync(CreateMeetingRoomInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 469
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-11 16:36:42.2833383 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #17 '00-e674e4eee007a033b52d69d8b5bb51ca-1b0990dce9c43a1b-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      第三方平台创建会议室失败，会议室名称: 21
fail: 2025-07-11 16:37:09.1423755 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #20 '00-3a3f1f49cddcd16001e408ad8b3c74eb-8978c8248bc7d7ca-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建会议室时发生异常，名称：252525      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidCastException: Unable to cast object of type 'System.Text.Json.JsonElement' to type 'System.IConvertible'.
         at System.Convert.ToInt32(Object value)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateMeetingRoomAsync(CreateMeetingRoomInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 469
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-11 16:37:09.3963337 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #20 '00-3a3f1f49cddcd16001e408ad8b3c74eb-8978c8248bc7d7ca-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      第三方平台创建会议室失败，会议室名称: 252525
fail: 2025-07-11 16:38:51.5707348 +08:00 Friday L Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService[0] #18 '00-96c5e24a60e13684171d6020af071737-75121cbed5737b42-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      创建会议室时发生异常，名称：212      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidCastException: Unable to cast object of type 'System.Text.Json.JsonElement' to type 'System.IConvertible'.
         at System.Convert.ToInt32(Object value)
         at Admin.NET.Plugin.GreenDisplay.Service.GreenDisplayService.CreateMeetingRoomAsync(CreateMeetingRoomInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Plugins\Admin.NET.Plugin.GreenDisplay\Service\GreenDisplayService.cs:line 469
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-11 16:38:51.8527787 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #18 '00-96c5e24a60e13684171d6020af071737-75121cbed5737b42-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      第三方平台创建会议室失败，会议室名称: 212
fail: 2025-07-11 16:51:03.7383809 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #48 '00-f8523926f87c9e0a496ee0e263043923-ee212d6e69deb0c4-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      第三方平台同步失败，会议室ID: 696559284789317, 名称: 测试21      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Nullable object must have a value.
         at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingRoomsService.<>c__DisplayClass7_0.<<Update>b__0>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingRooms\MeetingRoomsService.cs:line 165
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-11 16:51:04.0362299 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #48 '00-f8523926f87c9e0a496ee0e263043923-ee212d6e69deb0c4-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      更新会议室事务失败，错误信息: Nullable object must have a value.
fail: 2025-07-11 16:51:40.0201983 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #14 '00-6da2f0ab6aed396be3646a4cc7649d5e-c546023b3a323193-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      第三方平台同步失败，会议室ID: 696559284789317, 名称: 测试21      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Nullable object must have a value.
         at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingRoomsService.<>c__DisplayClass7_0.<<Update>b__0>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingRooms\MeetingRoomsService.cs:line 165
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-11 16:51:40.1575087 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #14 '00-6da2f0ab6aed396be3646a4cc7649d5e-c546023b3a323193-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      更新会议室事务失败，错误信息: Nullable object must have a value.
fail: 2025-07-11 16:52:06.7710702 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #22 '00-1edca391abf25fbe8c89b7e54202da6b-5884031481af97f1-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      第三方平台同步失败，会议室ID: 696559284789317, 名称: 测试21      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Nullable object must have a value.
         at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingRoomsService.<>c__DisplayClass7_0.<<Update>b__0>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingRooms\MeetingRoomsService.cs:line 165
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-11 16:52:06.8831546 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #22 '00-1edca391abf25fbe8c89b7e54202da6b-5884031481af97f1-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      更新会议室事务失败，错误信息: Nullable object must have a value.
fail: 2025-07-11 16:52:16.4775521 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #5 '00-2c745eca6774cff16da40720be57cf58-7b9d75a589b2f3d0-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      第三方平台同步失败，会议室ID: 696559284789317, 名称: 测试22      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Nullable object must have a value.
         at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingRoomsService.<>c__DisplayClass7_0.<<Update>b__0>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingRooms\MeetingRoomsService.cs:line 165
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-11 16:56:18.4997172 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #3 '00-2c745eca6774cff16da40720be57cf58-7b9d75a589b2f3d0-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      第三方平台同步失败，会议室ID: 696559284789317, 名称: 测试22      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Nullable object must have a value.
         at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingRoomsService.<>c__DisplayClass7_0.<<Update>b__0>d.MoveNext() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingRooms\MeetingRoomsService.cs:line 165
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-11 16:56:18.6129080 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #3 '00-2c745eca6774cff16da40720be57cf58-7b9d75a589b2f3d0-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      更新会议室事务失败，错误信息: Nullable object must have a value.
fail: 2025-07-11 17:01:01.3197918 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #17 '00-ed7a06ea494d735e268d6bf6f87061aa-fb2c813bccc5ecec-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      第三方平台同步失败，会议室ID: 696559284789317, 名称: 测试22      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Nullable object must have a value.
         at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingRoomsService.<>c__DisplayClass7_0.<<Update>b__0>d.MoveNext()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-11 17:01:01.5913237 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #17 '00-ed7a06ea494d735e268d6bf6f87061aa-fb2c813bccc5ecec-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      更新会议室事务失败，错误信息: Nullable object must have a value.
fail: 2025-07-11 17:01:34.2450663 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #17 '00-b8d6f8f52b284f3b86af48eabfbc853b-46f1674dc19a21e2-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      第三方平台同步失败，会议室ID: 696559284789317, 名称: 测试22      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Nullable object must have a value.
         at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingRoomsService.<>c__DisplayClass7_0.<<Update>b__0>d.MoveNext()
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-11 17:01:34.4340366 +08:00 Friday L Admin.NET.Application.MeetingRoomsService[0] #17 '00-b8d6f8f52b284f3b86af48eabfbc853b-46f1674dc19a21e2-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      更新会议室事务失败，错误信息: Nullable object must have a value.
