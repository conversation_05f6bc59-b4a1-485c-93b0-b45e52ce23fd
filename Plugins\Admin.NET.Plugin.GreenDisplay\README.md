# Admin.NET.Plugin.GreenDisplay

蓝牙桌牌插件，用于与蓝牙桌牌系统进行集成。

## 功能特性

- **设备管理**：支持标签绑定、设备查询、标签闪烁等功能
- **会议管理**：支持会议室和会议人员的创建、查询、更新、删除
- **模板管理**：支持模板的查询和创建
- **AP管理**：支持网关设备的管理
- **认证管理**：自动处理访问令牌的获取和缓存
- **错误处理**：完善的异常处理和日志记录
- **重试机制**：支持API调用失败时的自动重试

## 配置说明

在 `appsettings.json` 中添加以下配置：

```json
{
  "GreenDisplay": {
    "BaseUrl": "https://your-greendisplay-api.com",
    "Username": "your-username",
    "Password": "your-password",
    "TimeoutSeconds": 30,
    "EnableSslVerification": true,
    "RetryCount": 3,
    "CacheExpirationMinutes": 30
  }
}
```

### 配置项说明

- `BaseUrl`：蓝牙桌牌API的基础URL
- `Username`：登录用户名
- `Password`：登录密码
- `TimeoutSeconds`：HTTP请求超时时间（秒）
- `EnableSslVerification`：是否启用SSL证书验证
- `RetryCount`：API调用失败时的重试次数
- `CacheExpirationMinutes`：访问令牌缓存过期时间（分钟）

## 使用示例

### 1. 注入服务

```csharp
public class YourController : ControllerBase
{
    private readonly GreenDisplayService _greenDisplayService;
    
    public YourController(GreenDisplayService greenDisplayService)
    {
        _greenDisplayService = greenDisplayService;
    }
}
```

### 2. 设备管理

```csharp
// 获取设备信息
var device = await _greenDisplayService.GetDeviceAsync("AA:BB:CC:DD:EE:FF");

// 查询设备列表
var devices = await _greenDisplayService.QueryDevicesAsync(new QueryDevicesInput
{
    PageNo = 1,
    PageSize = 10
});

// 闪烁标签
var result = await _greenDisplayService.FlashLabelAsync("AA:BB:CC:DD:EE:FF", 5);
```

### 3. 会议室管理

```csharp
// 创建会议室
var createResult = await _greenDisplayService.CreateMeetingRoomAsync(new CreateMeetingRoomInput
{
    Name = "会议室A",
    Description = "大型会议室",
    LabelMac = "AA:BB:CC:DD:EE:FF"
});

// 查询会议室列表
var rooms = await _greenDisplayService.QueryMeetingRoomsAsync(new QueryMeetingRoomsInput
{
    PageNo = 1,
    PageSize = 10
});
```

### 4. 模板管理

```csharp
// 查询模板列表
var templates = await _greenDisplayService.QueryTemplatesAsync(new QueryTemplatesInput
{
    PageNo = 1,
    PageSize = 10
});
```

### 5. 直接使用API接口

如果需要更细粒度的控制，可以直接注入 `IGreenDisplayApi` 接口：

```csharp
public class YourController : ControllerBase
{
    private readonly IGreenDisplayApi _api;
    
    public YourController(IGreenDisplayApi api)
    {
        _api = api;
    }
    
    public async Task<IActionResult> Login()
    {
        var response = await _api.LoginAsync(new GreenDisplayLoginInput
        {
            Username = "username",
            Password = "password"
        });
        
        return Ok(response);
    }
}
```

## API接口说明

### 认证接口
- `POST /auth/login` - 用户登录认证

### 设备管理接口
- `POST /device/label/bind_multi` - 绑定多个标签
- `POST /device/label/bind_data_multi` - 绑定标签数据
- `POST /device/label/flash` - 闪烁标签
- `GET /device/label/get` - 获取单个设备信息
- `POST /device/label/query` - 查询所有设备
- `POST /device/label/update` - 更新标签

### 会议管理接口
- `POST /admin-api/meeting/room/create` - 创建会议室
- `POST /meeting/room/query` - 查询会议室
- `POST /admin-api/meeting/room/update` - 更新会议室
- `DELETE /admin-api/meeting/room/delete` - 删除会议室
- `POST /admin-api/meeting/staff/create` - 创建会议人员
- `POST /meeting/staff/query` - 查询会议人员
- `POST /admin-api/meeting/staff/update` - 更新会议人员
- `DELETE /admin-api/meeting/staff/delete` - 删除会议人员

### 模板管理接口
- `POST /resource/template/query` - 查询模板
- `POST /admin-api/resource/template/create` - 创建模板

### AP管理接口
- `POST /admin-api/device/ap/page` - 获取网关列表
- `POST /admin-api/device/ap/create` - 创建网关
- `GET /admin-api/device/ap/get` - 根据网关MAC获取网关基本信息
- `POST /admin-api/device/ap/update` - 修改网关信息

## 注意事项

1. 所有API调用都需要认证，插件会自动处理访问令牌的获取和缓存
2. 访问令牌会根据配置的过期时间自动刷新
3. API调用失败时会根据配置的重试次数进行自动重试
4. 建议在生产环境中启用SSL证书验证
5. 请根据实际网络环境调整超时时间和重试次数

## 依赖项

- Admin.NET.Core
- Furion
- Microsoft.Extensions.Http
- Microsoft.Extensions.Caching.Memory
- Polly

## 完整调用实例

以下是一个完整的控制器示例，展示如何使用GreenDisplay插件的各种功能：

```csharp
using Admin.NET.Plugin.GreenDisplay.Service;
using Admin.NET.Plugin.GreenDisplay.Service.Dto;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace YourProject.Controllers
{
    /// <summary>
    /// 蓝牙桌牌管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class GreenDisplayController : ControllerBase
    {
        private readonly GreenDisplayService _greenDisplayService;
        private readonly ILogger<GreenDisplayController> _logger;

        public GreenDisplayController(
            GreenDisplayService greenDisplayService,
            ILogger<GreenDisplayController> logger)
        {
            _greenDisplayService = greenDisplayService;
            _logger = logger;
        }

        /// <summary>
        /// 设备管理示例
        /// </summary>
        [HttpPost("device-demo")]
        public async Task<IActionResult> DeviceDemo()
        {
            try
            {
                // 1. 查询设备列表
                var queryInput = new QueryDevicesInput
                {
                    PageNo = 1,
                    PageSize = 10,
                    LabelMac = "AA:BB:CC:DD:EE:FF"
                };
                var devices = await _greenDisplayService.QueryDevicesAsync(queryInput);
                _logger.LogInformation("查询到 {Count} 个设备", devices?.Data?.Count ?? 0);

                // 2. 获取单个设备信息
                var device = await _greenDisplayService.GetDeviceAsync("AA:BB:CC:DD:EE:FF");
                if (device != null)
                {
                    _logger.LogInformation("设备状态：{Status}", device.Status);
                }

                // 3. 绑定标签
                var bindInput = new BindMultipleLabelsInput
                {
                    LabelMac = "AA:BB:CC:DD:EE:FF",
                    TemplateId = 1,
                    StaffCode = "EMP001"
                };
                var bindResult = await _greenDisplayService.BindMultipleLabelsAsync(bindInput);
                _logger.LogInformation("标签绑定结果：{Result}", bindResult);

                // 4. 闪烁标签（持续5秒）
                var flashResult = await _greenDisplayService.FlashLabelAsync("AA:BB:CC:DD:EE:FF", 5);
                _logger.LogInformation("标签闪烁结果：{Result}", flashResult);

                return Ok(new { Message = "设备操作演示完成", DeviceCount = devices?.Data?.Count });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设备操作演示失败");
                return StatusCode(500, new { Message = "操作失败", Error = ex.Message });
            }
        }

        /// <summary>
        /// 会议室管理示例
        /// </summary>
        [HttpPost("meeting-demo")]
        public async Task<IActionResult> MeetingDemo()
        {
            try
            {
                // 1. 创建会议室
                var createRoomInput = new CreateMeetingRoomInput
                {
                    Name = "技术讨论室A",
                    Description = "用于技术团队日常讨论的会议室",
                    LabelMac = "BB:CC:DD:EE:FF:AA"
                };
                var createRoomResult = await _greenDisplayService.CreateMeetingRoomAsync(createRoomInput);
                _logger.LogInformation("会议室创建结果：{Result}", createRoomResult);

                // 2. 查询会议室列表
                var queryRoomsInput = new QueryMeetingRoomsInput
                {
                    PageNo = 1,
                    PageSize = 20,
                    Name = "技术"
                };
                var rooms = await _greenDisplayService.QueryMeetingRoomsAsync(queryRoomsInput);
                _logger.LogInformation("查询到 {Count} 个会议室", rooms?.Data?.Count ?? 0);

                // 3. 创建会议人员
                var createStaffInput = new CreateMeetingStaffInput
                {
                    Name = "张三",
                    StaffCode = "EMP001",
                    Department = "技术部",
                    Position = "高级工程师"
                };
                var createStaffResult = await _greenDisplayService.CreateMeetingStaffAsync(createStaffInput);
                _logger.LogInformation("会议人员创建结果：{Result}", createStaffResult);

                return Ok(new { 
                    Message = "会议管理演示完成", 
                    RoomCount = rooms?.Data?.Count,
                    CreateResults = new { Room = createRoomResult, Staff = createStaffResult }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "会议管理演示失败");
                return StatusCode(500, new { Message = "操作失败", Error = ex.Message });
            }
        }

        /// <summary>
        /// 模板管理示例
        /// </summary>
        [HttpPost("template-demo")]
        public async Task<IActionResult> TemplateDemo()
        {
            try
            {
                // 查询模板列表
                var queryInput = new QueryTemplatesInput
                {
                    PageNo = 1,
                    PageSize = 10,
                    Name = "标准"
                };
                var templates = await _greenDisplayService.QueryTemplatesAsync(queryInput);
                _logger.LogInformation("查询到 {Count} 个模板", templates?.Data?.Count ?? 0);

                // 显示模板信息
                if (templates?.Data?.Any() == true)
                {
                    foreach (var template in templates.Data.Take(3))
                    {
                        _logger.LogInformation("模板：{Name}, ID：{Id}", template.Name, template.Id);
                    }
                }

                return Ok(new { 
                    Message = "模板查询演示完成", 
                    TemplateCount = templates?.Data?.Count,
                    Templates = templates?.Data?.Take(3).Select(t => new { t.Id, t.Name })
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "模板查询演示失败");
                return StatusCode(500, new { Message = "操作失败", Error = ex.Message });
            }
        }

        /// <summary>
        /// 综合业务流程示例
        /// </summary>
        [HttpPost("workflow-demo")]
        public async Task<IActionResult> WorkflowDemo([FromBody] WorkflowDemoInput input)
        {
            try
            {
                var results = new List<string>();

                // 步骤1：创建会议室
                var room = await _greenDisplayService.CreateMeetingRoomAsync(new CreateMeetingRoomInput
                {
                    Name = input.RoomName,
                    Description = input.RoomDescription,
                    LabelMac = input.LabelMac
                });
                results.Add($"会议室创建：{(room ? "成功" : "失败")}");

                // 步骤2：创建会议人员
                var staff = await _greenDisplayService.CreateMeetingStaffAsync(new CreateMeetingStaffInput
                {
                    Name = input.StaffName,
                    StaffCode = input.StaffCode,
                    Department = input.Department,
                    Position = input.Position
                });
                results.Add($"人员创建：{(staff ? "成功" : "失败")}");

                // 步骤3：绑定设备
                var bind = await _greenDisplayService.BindMultipleLabelsAsync(new BindMultipleLabelsInput
                {
                    LabelMac = input.LabelMac,
                    TemplateId = input.TemplateId,
                    StaffCode = input.StaffCode
                });
                results.Add($"设备绑定：{(bind ? "成功" : "失败")}");

                // 步骤4：闪烁提示
                var flash = await _greenDisplayService.FlashLabelAsync(input.LabelMac, 3);
                results.Add($"闪烁提示：{(flash ? "成功" : "失败")}");

                return Ok(new { 
                    Message = "业务流程演示完成", 
                    Steps = results,
                    Success = results.All(r => r.Contains("成功"))
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "业务流程演示失败");
                return StatusCode(500, new { Message = "操作失败", Error = ex.Message });
            }
        }
    }

    /// <summary>
    /// 工作流程演示输入参数
    /// </summary>
    public class WorkflowDemoInput
    {
        public string RoomName { get; set; } = "演示会议室";
        public string RoomDescription { get; set; } = "用于演示的会议室";
        public string LabelMac { get; set; } = "AA:BB:CC:DD:EE:FF";
        public string StaffName { get; set; } = "演示用户";
        public string StaffCode { get; set; } = "DEMO001";
        public string Department { get; set; } = "演示部门";
        public string Position { get; set; } = "演示职位";
        public int TemplateId { get; set; } = 1;
    }
}
```

### 使用说明

1. **依赖注入配置**：确保在 `Program.cs` 或 `Startup.cs` 中正确配置了插件
2. **配置文件**：在 `appsettings.json` 中添加必要的配置项
3. **错误处理**：示例中包含了完整的异常处理和日志记录
4. **业务流程**：`workflow-demo` 端点展示了一个完整的业务流程
5. **参数验证**：在实际使用中建议添加参数验证特性

### 测试调用

```bash
# 设备管理演示
curl -X POST "https://localhost:5001/api/greendisplay/device-demo"

# 会议管理演示
curl -X POST "https://localhost:5001/api/greendisplay/meeting-demo"

# 模板查询演示
curl -X POST "https://localhost:5001/api/greendisplay/template-demo"

# 综合业务流程演示
curl -X POST "https://localhost:5001/api/greendisplay/workflow-demo" \
  -H "Content-Type: application/json" \
  -d '{
    "roomName": "测试会议室",
    "labelMac": "AA:BB:CC:DD:EE:FF",
    "staffName": "测试用户",
    "staffCode": "TEST001",
    "templateId": 1
  }'
```

## CRUD操作完整实例

以下是一个完整的增删查改(CRUD)操作示例，展示如何使用GreenDisplay插件进行各种数据操作：

```csharp
using Admin.NET.Plugin.GreenDisplay.Service;
using Admin.NET.Plugin.GreenDisplay.Service.Dto;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace YourProject.Controllers
{
    /// <summary>
    /// GreenDisplay CRUD操作示例控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class GreenDisplayCrudController : ControllerBase
    {
        private readonly GreenDisplayService _greenDisplayService;
        private readonly ILogger<GreenDisplayCrudController> _logger;

        public GreenDisplayCrudController(
            GreenDisplayService greenDisplayService,
            ILogger<GreenDisplayCrudController> logger)
        {
            _greenDisplayService = greenDisplayService;
            _logger = logger;
        }

        #region 会议室CRUD操作

        /// <summary>
        /// 创建会议室 (Create)
        /// </summary>
        /// <param name="input">会议室创建参数</param>
        /// <returns></returns>
        [HttpPost("meeting-room")]
        public async Task<IActionResult> CreateMeetingRoom([FromBody] CreateMeetingRoomInput input)
        {
            try
            {
                var result = await _greenDisplayService.CreateMeetingRoomAsync(input);
                if (result)
                {
                    _logger.LogInformation("会议室创建成功：{Name}", input.Name);
                    return Ok(new { Success = true, Message = "会议室创建成功", Data = input });
                }
                else
                {
                    return BadRequest(new { Success = false, Message = "会议室创建失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建会议室时发生错误");
                return StatusCode(500, new { Success = false, Message = "服务器内部错误", Error = ex.Message });
            }
        }

        /// <summary>
        /// 查询会议室列表 (Read)
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns></returns>
        [HttpGet("meeting-rooms")]
        public async Task<IActionResult> GetMeetingRooms([FromQuery] QueryMeetingRoomsInput input)
        {
            try
            {
                var result = await _greenDisplayService.QueryMeetingRoomsAsync(input);
                _logger.LogInformation("查询会议室列表成功，共{Count}条记录", result?.Data?.Count ?? 0);
                return Ok(new { Success = true, Message = "查询成功", Data = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询会议室列表时发生错误");
                return StatusCode(500, new { Success = false, Message = "服务器内部错误", Error = ex.Message });
            }
        }

        /// <summary>
        /// 更新会议室信息 (Update)
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns></returns>
        [HttpPut("meeting-room")]
        public async Task<IActionResult> UpdateMeetingRoom([FromBody] UpdateMeetingRoomInput input)
        {
            try
            {
                var result = await _greenDisplayService.UpdateMeetingRoomAsync(input);
                if (result)
                {
                    _logger.LogInformation("会议室更新成功：ID={Id}", input.Id);
                    return Ok(new { Success = true, Message = "会议室更新成功", Data = input });
                }
                else
                {
                    return BadRequest(new { Success = false, Message = "会议室更新失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新会议室时发生错误");
                return StatusCode(500, new { Success = false, Message = "服务器内部错误", Error = ex.Message });
            }
        }

        /// <summary>
        /// 删除会议室 (Delete)
        /// </summary>
        /// <param name="id">会议室ID</param>
        /// <returns></returns>
        [HttpDelete("meeting-room/{id}")]
        public async Task<IActionResult> DeleteMeetingRoom(long id)
        {
            try
            {
                var input = new DeleteMeetingRoomInput { Id = id };
                var result = await _greenDisplayService.DeleteMeetingRoomAsync(input);
                if (result)
                {
                    _logger.LogInformation("会议室删除成功：ID={Id}", id);
                    return Ok(new { Success = true, Message = "会议室删除成功" });
                }
                else
                {
                    return BadRequest(new { Success = false, Message = "会议室删除失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除会议室时发生错误");
                return StatusCode(500, new { Success = false, Message = "服务器内部错误", Error = ex.Message });
            }
        }

        #endregion

        #region 会议人员CRUD操作

        /// <summary>
        /// 创建会议人员 (Create)
        /// </summary>
        /// <param name="input">人员创建参数</param>
        /// <returns></returns>
        [HttpPost("meeting-staff")]
        public async Task<IActionResult> CreateMeetingStaff([FromBody] CreateMeetingStaffInput input)
        {
            try
            {
                var result = await _greenDisplayService.CreateMeetingStaffAsync(input);
                if (result)
                {
                    _logger.LogInformation("会议人员创建成功：{Name}({StaffCode})", input.Name, input.StaffCode);
                    return Ok(new { Success = true, Message = "会议人员创建成功", Data = input });
                }
                else
                {
                    return BadRequest(new { Success = false, Message = "会议人员创建失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建会议人员时发生错误");
                return StatusCode(500, new { Success = false, Message = "服务器内部错误", Error = ex.Message });
            }
        }

        /// <summary>
        /// 查询会议人员列表 (Read)
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns></returns>
        [HttpGet("meeting-staff")]
        public async Task<IActionResult> GetMeetingStaff([FromQuery] QueryMeetingStaffInput input)
        {
            try
            {
                var result = await _greenDisplayService.QueryMeetingStaffAsync(input);
                _logger.LogInformation("查询会议人员列表成功，共{Count}条记录", result?.Data?.Count ?? 0);
                return Ok(new { Success = true, Message = "查询成功", Data = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询会议人员列表时发生错误");
                return StatusCode(500, new { Success = false, Message = "服务器内部错误", Error = ex.Message });
            }
        }

        /// <summary>
        /// 更新会议人员信息 (Update)
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns></returns>
        [HttpPut("meeting-staff")]
        public async Task<IActionResult> UpdateMeetingStaff([FromBody] UpdateMeetingStaffInput input)
        {
            try
            {
                var result = await _greenDisplayService.UpdateMeetingStaffAsync(input);
                if (result)
                {
                    _logger.LogInformation("会议人员更新成功：ID={Id}", input.Id);
                    return Ok(new { Success = true, Message = "会议人员更新成功", Data = input });
                }
                else
                {
                    return BadRequest(new { Success = false, Message = "会议人员更新失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新会议人员时发生错误");
                return StatusCode(500, new { Success = false, Message = "服务器内部错误", Error = ex.Message });
            }
        }

        /// <summary>
        /// 删除会议人员 (Delete)
        /// </summary>
        /// <param name="id">人员ID</param>
        /// <returns></returns>
        [HttpDelete("meeting-staff/{id}")]
        public async Task<IActionResult> DeleteMeetingStaff(long id)
        {
            try
            {
                var input = new DeleteMeetingStaffInput { Id = id };
                var result = await _greenDisplayService.DeleteMeetingStaffAsync(input);
                if (result)
                {
                    _logger.LogInformation("会议人员删除成功：ID={Id}", id);
                    return Ok(new { Success = true, Message = "会议人员删除成功" });
                }
                else
                {
                    return BadRequest(new { Success = false, Message = "会议人员删除失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除会议人员时发生错误");
                return StatusCode(500, new { Success = false, Message = "服务器内部错误", Error = ex.Message });
            }
        }

        #endregion

        #region 设备管理CRUD操作

        /// <summary>
        /// 绑定设备标签 (Create)
        /// </summary>
        /// <param name="input">绑定参数</param>
        /// <returns></returns>
        [HttpPost("device/bind")]
        public async Task<IActionResult> BindDevice([FromBody] BindMultipleLabelsInput input)
        {
            try
            {
                var result = await _greenDisplayService.BindMultipleLabelsAsync(input);
                if (result)
                {
                    _logger.LogInformation("设备绑定成功：{LabelMac}", input.LabelMac);
                    return Ok(new { Success = true, Message = "设备绑定成功", Data = input });
                }
                else
                {
                    return BadRequest(new { Success = false, Message = "设备绑定失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "绑定设备时发生错误");
                return StatusCode(500, new { Success = false, Message = "服务器内部错误", Error = ex.Message });
            }
        }

        /// <summary>
        /// 查询设备列表 (Read)
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns></returns>
        [HttpGet("devices")]
        public async Task<IActionResult> GetDevices([FromQuery] QueryDevicesInput input)
        {
            try
            {
                var result = await _greenDisplayService.QueryDevicesAsync(input);
                _logger.LogInformation("查询设备列表成功，共{Count}条记录", result?.Data?.Count ?? 0);
                return Ok(new { Success = true, Message = "查询成功", Data = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询设备列表时发生错误");
                return StatusCode(500, new { Success = false, Message = "服务器内部错误", Error = ex.Message });
            }
        }

        /// <summary>
        /// 获取单个设备信息 (Read)
        /// </summary>
        /// <param name="labelMac">设备MAC地址</param>
        /// <returns></returns>
        [HttpGet("device/{labelMac}")]
        public async Task<IActionResult> GetDevice(string labelMac)
        {
            try
            {
                var result = await _greenDisplayService.GetDeviceAsync(labelMac);
                if (result != null)
                {
                    _logger.LogInformation("获取设备信息成功：{LabelMac}", labelMac);
                    return Ok(new { Success = true, Message = "查询成功", Data = result });
                }
                else
                {
                    return NotFound(new { Success = false, Message = "设备不存在" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备信息时发生错误");
                return StatusCode(500, new { Success = false, Message = "服务器内部错误", Error = ex.Message });
            }
        }

        /// <summary>
        /// 更新设备标签 (Update)
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns></returns>
        [HttpPut("device")]
        public async Task<IActionResult> UpdateDevice([FromBody] UpdateLabelInput input)
        {
            try
            {
                var result = await _greenDisplayService.UpdateLabelAsync(input);
                if (result)
                {
                    _logger.LogInformation("设备更新成功：{LabelMac}", input.LabelMac);
                    return Ok(new { Success = true, Message = "设备更新成功", Data = input });
                }
                else
                {
                    return BadRequest(new { Success = false, Message = "设备更新失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新设备时发生错误");
                return StatusCode(500, new { Success = false, Message = "服务器内部错误", Error = ex.Message });
            }
        }

        /// <summary>
        /// 设备闪烁操作 (特殊操作)
        /// </summary>
        /// <param name="labelMac">设备MAC地址</param>
        /// <param name="duration">闪烁持续时间(秒)</param>
        /// <returns></returns>
        [HttpPost("device/{labelMac}/flash")]
        public async Task<IActionResult> FlashDevice(string labelMac, [FromQuery] int duration = 3)
        {
            try
            {
                var result = await _greenDisplayService.FlashLabelAsync(labelMac, duration);
                if (result)
                {
                    _logger.LogInformation("设备闪烁成功：{LabelMac}, 持续{Duration}秒", labelMac, duration);
                    return Ok(new { Success = true, Message = "设备闪烁成功" });
                }
                else
                {
                    return BadRequest(new { Success = false, Message = "设备闪烁失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设备闪烁时发生错误");
                return StatusCode(500, new { Success = false, Message = "服务器内部错误", Error = ex.Message });
            }
        }

        #endregion

        #region 模板管理CRUD操作

        /// <summary>
        /// 创建模板 (Create)
        /// </summary>
        /// <param name="input">模板创建参数</param>
        /// <returns></returns>
        [HttpPost("template")]
        public async Task<IActionResult> CreateTemplate([FromBody] CreateTemplateInput input)
        {
            try
            {
                var result = await _greenDisplayService.CreateTemplateAsync(input);
                if (result)
                {
                    _logger.LogInformation("模板创建成功：{Name}", input.Name);
                    return Ok(new { Success = true, Message = "模板创建成功", Data = input });
                }
                else
                {
                    return BadRequest(new { Success = false, Message = "模板创建失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建模板时发生错误");
                return StatusCode(500, new { Success = false, Message = "服务器内部错误", Error = ex.Message });
            }
        }

        /// <summary>
        /// 查询模板列表 (Read)
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns></returns>
        [HttpGet("templates")]
        public async Task<IActionResult> GetTemplates([FromQuery] QueryTemplatesInput input)
        {
            try
            {
                var result = await _greenDisplayService.QueryTemplatesAsync(input);
                _logger.LogInformation("查询模板列表成功，共{Count}条记录", result?.Data?.Count ?? 0);
                return Ok(new { Success = true, Message = "查询成功", Data = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询模板列表时发生错误");
                return StatusCode(500, new { Success = false, Message = "服务器内部错误", Error = ex.Message });
            }
        }

        #endregion

        #region AP设备CRUD操作

        /// <summary>
        /// 创建AP设备 (Create)
        /// </summary>
        /// <param name="input">AP创建参数</param>
        /// <returns></returns>
        [HttpPost("ap")]
        public async Task<IActionResult> CreateAP([FromBody] CreateAPInput input)
        {
            try
            {
                var result = await _greenDisplayService.CreateAPAsync(input);
                if (result)
                {
                    _logger.LogInformation("AP设备创建成功：{ApMac}", input.ApMac);
                    return Ok(new { Success = true, Message = "AP设备创建成功", Data = input });
                }
                else
                {
                    return BadRequest(new { Success = false, Message = "AP设备创建失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建AP设备时发生错误");
                return StatusCode(500, new { Success = false, Message = "服务器内部错误", Error = ex.Message });
            }
        }

        /// <summary>
        /// 查询AP设备列表 (Read)
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns></returns>
        [HttpGet("aps")]
        public async Task<IActionResult> GetAPList([FromQuery] GetAPListInput input)
        {
            try
            {
                var result = await _greenDisplayService.GetAPListAsync(input);
                _logger.LogInformation("查询AP设备列表成功，共{Count}条记录", result?.Data?.Count ?? 0);
                return Ok(new { Success = true, Message = "查询成功", Data = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询AP设备列表时发生错误");
                return StatusCode(500, new { Success = false, Message = "服务器内部错误", Error = ex.Message });
            }
        }

        /// <summary>
        /// 获取单个AP设备信息 (Read)
        /// </summary>
        /// <param name="apMac">AP设备MAC地址</param>
        /// <returns></returns>
        [HttpGet("ap/{apMac}")]
        public async Task<IActionResult> GetAP(string apMac)
        {
            try
            {
                var result = await _greenDisplayService.GetAPAsync(apMac);
                if (result != null)
                {
                    _logger.LogInformation("获取AP设备信息成功：{ApMac}", apMac);
                    return Ok(new { Success = true, Message = "查询成功", Data = result });
                }
                else
                {
                    return NotFound(new { Success = false, Message = "AP设备不存在" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取AP设备信息时发生错误");
                return StatusCode(500, new { Success = false, Message = "服务器内部错误", Error = ex.Message });
            }
        }

        /// <summary>
        /// 更新AP设备信息 (Update)
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns></returns>
        [HttpPut("ap")]
        public async Task<IActionResult> UpdateAP([FromBody] UpdateAPInput input)
        {
            try
            {
                var result = await _greenDisplayService.UpdateAPAsync(input);
                if (result)
                {
                    _logger.LogInformation("AP设备更新成功：{ApMac}", input.ApMac);
                    return Ok(new { Success = true, Message = "AP设备更新成功", Data = input });
                }
                else
                {
                    return BadRequest(new { Success = false, Message = "AP设备更新失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新AP设备时发生错误");
                return StatusCode(500, new { Success = false, Message = "服务器内部错误", Error = ex.Message });
            }
        }

        #endregion

        #region 综合CRUD操作示例

        /// <summary>
        /// 完整的CRUD操作流程示例
        /// </summary>
        /// <param name="input">操作参数</param>
        /// <returns></returns>
        [HttpPost("crud-demo")]
        public async Task<IActionResult> CrudDemo([FromBody] CrudDemoInput input)
        {
            var operationResults = new List<string>();
            
            try
            {
                // 1. 创建会议室
                var createRoomResult = await _greenDisplayService.CreateMeetingRoomAsync(new CreateMeetingRoomInput
                {
                    Name = input.RoomName,
                    Description = input.RoomDescription,
                    LabelMac = input.LabelMac
                });
                operationResults.Add($"创建会议室: {(createRoomResult ? "成功" : "失败")}");

                // 2. 查询会议室
                var queryRoomsResult = await _greenDisplayService.QueryMeetingRoomsAsync(new QueryMeetingRoomsInput
                {
                    PageNo = 1,
                    PageSize = 10,
                    Name = input.RoomName
                });
                operationResults.Add($"查询会议室: 找到{queryRoomsResult?.Data?.Count ?? 0}条记录");

                // 3. 创建会议人员
                var createStaffResult = await _greenDisplayService.CreateMeetingStaffAsync(new CreateMeetingStaffInput
                {
                    Name = input.StaffName,
                    StaffCode = input.StaffCode,
                    Department = input.Department,
                    Position = input.Position
                });
                operationResults.Add($"创建会议人员: {(createStaffResult ? "成功" : "失败")}");

                // 4. 查询会议人员
                var queryStaffResult = await _greenDisplayService.QueryMeetingStaffAsync(new QueryMeetingStaffInput
                {
                    PageNo = 1,
                    PageSize = 10,
                    Name = input.StaffName
                });
                operationResults.Add($"查询会议人员: 找到{queryStaffResult?.Data?.Count ?? 0}条记录");

                // 5. 绑定设备
                var bindDeviceResult = await _greenDisplayService.BindMultipleLabelsAsync(new BindMultipleLabelsInput
                {
                    LabelMac = input.LabelMac,
                    TemplateId = input.TemplateId,
                    StaffCode = input.StaffCode
                });
                operationResults.Add($"绑定设备: {(bindDeviceResult ? "成功" : "失败")}");

                // 6. 查询设备
                var queryDevicesResult = await _greenDisplayService.QueryDevicesAsync(new QueryDevicesInput
                {
                    PageNo = 1,
                    PageSize = 10,
                    LabelMac = input.LabelMac
                });
                operationResults.Add($"查询设备: 找到{queryDevicesResult?.Data?.Count ?? 0}条记录");

                // 7. 闪烁设备
                var flashResult = await _greenDisplayService.FlashLabelAsync(input.LabelMac, 3);
                operationResults.Add($"设备闪烁: {(flashResult ? "成功" : "失败")}");

                return Ok(new
                {
                    Success = true,
                    Message = "CRUD操作演示完成",
                    Operations = operationResults,
                    Summary = new
                    {
                        TotalOperations = operationResults.Count,
                        SuccessfulOperations = operationResults.Count(r => r.Contains("成功") || r.Contains("找到")),
                        Input = input
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CRUD操作演示失败");
                return StatusCode(500, new
                {
                    Success = false,
                    Message = "CRUD操作演示失败",
                    Error = ex.Message,
                    CompletedOperations = operationResults
                });
            }
        }

        #endregion
    }

    /// <summary>
    /// CRUD演示输入参数
    /// </summary>
    public class CrudDemoInput
    {
        /// <summary>
        /// 会议室名称
        /// </summary>
        public string RoomName { get; set; } = "CRUD演示会议室";
        
        /// <summary>
        /// 会议室描述
        /// </summary>
        public string RoomDescription { get; set; } = "用于CRUD操作演示的会议室";
        
        /// <summary>
        /// 设备MAC地址
        /// </summary>
        public string LabelMac { get; set; } = "AA:BB:CC:DD:EE:FF";
        
        /// <summary>
        /// 人员姓名
        /// </summary>
        public string StaffName { get; set; } = "CRUD演示用户";
        
        /// <summary>
        /// 人员编码
        /// </summary>
        public string StaffCode { get; set; } = "CRUD001";
        
        /// <summary>
        /// 部门
        /// </summary>
        public string Department { get; set; } = "演示部门";
        
        /// <summary>
        /// 职位
        /// </summary>
        public string Position { get; set; } = "演示职位";
        
        /// <summary>
        /// 模板ID
        /// </summary>
        public int TemplateId { get; set; } = 1;
    }
}
```

### CRUD操作API测试示例

```bash
# 1. 会议室CRUD操作

# 创建会议室
curl -X POST "https://localhost:5001/api/greendisplaycrud/meeting-room" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "技术会议室A",
    "description": "用于技术讨论的会议室",
    "labelMac": "AA:BB:CC:DD:EE:FF"
  }'

# 查询会议室列表
curl -X GET "https://localhost:5001/api/greendisplaycrud/meeting-rooms?pageNo=1&pageSize=10&name=技术"

# 更新会议室
curl -X PUT "https://localhost:5001/api/greendisplaycrud/meeting-room" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "name": "技术会议室A(已更新)",
    "description": "更新后的会议室描述",
    "labelMac": "AA:BB:CC:DD:EE:FF"
  }'

# 删除会议室
curl -X DELETE "https://localhost:5001/api/greendisplaycrud/meeting-room/1"

# 2. 会议人员CRUD操作

# 创建会议人员
curl -X POST "https://localhost:5001/api/greendisplaycrud/meeting-staff" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "张三",
    "staffCode": "EMP001",
    "department": "技术部",
    "position": "高级工程师"
  }'

# 查询会议人员列表
curl -X GET "https://localhost:5001/api/greendisplaycrud/meeting-staff?pageNo=1&pageSize=10&name=张三"

# 更新会议人员
curl -X PUT "https://localhost:5001/api/greendisplaycrud/meeting-staff" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "name": "张三(已更新)",
    "staffCode": "EMP001",
    "department": "技术部",
    "position": "资深工程师"
  }'

# 删除会议人员
curl -X DELETE "https://localhost:5001/api/greendisplaycrud/meeting-staff/1"

# 3. 设备管理操作

# 绑定设备
curl -X POST "https://localhost:5001/api/greendisplaycrud/device/bind" \
  -H "Content-Type: application/json" \
  -d '{
    "labelMac": "AA:BB:CC:DD:EE:FF",
    "templateId": 1,
    "staffCode": "EMP001"
  }'

# 查询设备列表
curl -X GET "https://localhost:5001/api/greendisplaycrud/devices?pageNo=1&pageSize=10"

# 获取单个设备信息
curl -X GET "https://localhost:5001/api/greendisplaycrud/device/AA:BB:CC:DD:EE:FF"

# 更新设备
curl -X PUT "https://localhost:5001/api/greendisplaycrud/device" \
  -H "Content-Type: application/json" \
  -d '{
    "labelMac": "AA:BB:CC:DD:EE:FF",
    "templateId": 2,
    "staffCode": "EMP001"
  }'

# 设备闪烁
curl -X POST "https://localhost:5001/api/greendisplaycrud/device/AA:BB:CC:DD:EE:FF/flash?duration=5"

# 4. 模板管理操作

# 创建模板
curl -X POST "https://localhost:5001/api/greendisplaycrud/template" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "标准模板",
    "description": "标准显示模板",
    "content": "模板内容"
  }'

# 查询模板列表
curl -X GET "https://localhost:5001/api/greendisplaycrud/templates?pageNo=1&pageSize=10"

# 5. AP设备管理操作

# 创建AP设备
curl -X POST "https://localhost:5001/api/greendisplaycrud/ap" \
  -H "Content-Type: application/json" \
  -d '{
    "apMac": "BB:CC:DD:EE:FF:AA",
    "name": "网关设备1",
    "location": "一楼大厅"
  }'

# 查询AP设备列表
curl -X GET "https://localhost:5001/api/greendisplaycrud/aps?pageNo=1&pageSize=10"

# 获取单个AP设备信息
curl -X GET "https://localhost:5001/api/greendisplaycrud/ap/BB:CC:DD:EE:FF:AA"

# 更新AP设备
curl -X PUT "https://localhost:5001/api/greendisplaycrud/ap" \
  -H "Content-Type: application/json" \
  -d '{
    "apMac": "BB:CC:DD:EE:FF:AA",
    "name": "网关设备1(已更新)",
    "location": "二楼会议区"
  }'

# 6. 综合CRUD操作演示
curl -X POST "https://localhost:5001/api/greendisplaycrud/crud-demo" \
  -H "Content-Type: application/json" \
  -d '{
    "roomName": "综合演示会议室",
    "roomDescription": "用于综合CRUD演示",
    "labelMac": "CC:DD:EE:FF:AA:BB",
    "staffName": "李四",
    "staffCode": "EMP002",
    "department": "产品部",
    "position": "产品经理",
    "templateId": 1
  }'
```

### CRUD操作最佳实践

1. **统一响应格式**：所有API都返回统一的响应格式，包含Success、Message和Data字段
2. **完善的错误处理**：每个操作都包含try-catch块，确保异常被正确处理
3. **详细的日志记录**：记录操作成功和失败的详细信息，便于调试和监控
4. **参数验证**：建议在实际项目中添加参数验证特性
5. **RESTful设计**：遵循RESTful API设计原则，使用合适的HTTP方法
6. **分模块管理**：将不同类型的操作分组管理，便于维护
7. **综合演示**：提供完整的业务流程演示，展示各个操作的协作

## 版本历史

### v1.0.0
- 初始版本
- 支持基本的设备、会议、模板、AP管理功能
- 集成认证和缓存机制
- 支持重试和错误处理
- 提供完整的CRUD操作示例