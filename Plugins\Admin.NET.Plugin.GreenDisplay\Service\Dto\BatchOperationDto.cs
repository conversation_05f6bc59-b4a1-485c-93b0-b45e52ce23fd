// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Plugin.GreenDisplay.Service.Dto;

/// <summary>
/// 批量操作结果
/// </summary>
/// <typeparam name="T">操作项类型</typeparam>
public class BatchOperationResult<T>
{
    /// <summary>
    /// 总数
    /// </summary>
    public int Total { get; set; }

    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailureCount { get; set; }

    /// <summary>
    /// 成功的项目
    /// </summary>
    public List<T> SuccessItems { get; set; } = new List<T>();

    /// <summary>
    /// 失败的项目及错误信息
    /// </summary>
    public List<BatchOperationError<T>> FailureItems { get; set; } = new List<BatchOperationError<T>>();

    /// <summary>
    /// 是否全部成功
    /// </summary>
    public bool IsAllSuccess => FailureCount == 0;

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => Total > 0 ? (double)SuccessCount / Total : 0;
}

/// <summary>
/// 批量操作错误信息
/// </summary>
/// <typeparam name="T">操作项类型</typeparam>
public class BatchOperationError<T>
{
    /// <summary>
    /// 失败的项目
    /// </summary>
    public T Item { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; }

    /// <summary>
    /// 错误代码
    /// </summary>
    public string ErrorCode { get; set; }

    /// <summary>
    /// 异常详情
    /// </summary>
    public Exception Exception { get; set; }
}

/// <summary>
/// 批量设备绑定输入
/// </summary>
public class BatchDeviceBindInput
{
    /// <summary>
    /// 设备绑定列表
    /// </summary>
    public List<BindMultipleLabelsInput> DeviceBindings { get; set; } = new List<BindMultipleLabelsInput>();

    /// <summary>
    /// 并发数量限制（默认5）
    /// </summary>
    public int ConcurrencyLimit { get; set; } = 5;

    /// <summary>
    /// 是否在遇到错误时继续处理其他项目
    /// </summary>
    public bool ContinueOnError { get; set; } = true;
}

/// <summary>
/// 批量设备查询输入
/// </summary>
public class BatchDeviceQueryInput
{
    /// <summary>
    /// 设备MAC地址列表
    /// </summary>
    public List<string> DeviceMacs { get; set; } = new List<string>();

    /// <summary>
    /// 并发数量限制（默认10）
    /// </summary>
    public int ConcurrencyLimit { get; set; } = 10;

    /// <summary>
    /// 是否在遇到错误时继续处理其他项目
    /// </summary>
    public bool ContinueOnError { get; set; } = true;
}

/// <summary>
/// 批量标签更新输入
/// </summary>
public class BatchLabelUpdateInput
{
    /// <summary>
    /// 标签更新列表
    /// </summary>
    public List<UpdateLabelInput> LabelUpdates { get; set; } = new List<UpdateLabelInput>();

    /// <summary>
    /// 并发数量限制（默认5）
    /// </summary>
    public int ConcurrencyLimit { get; set; } = 5;

    /// <summary>
    /// 是否在遇到错误时继续处理其他项目
    /// </summary>
    public bool ContinueOnError { get; set; } = true;
}