// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Plugin.GreenDisplay.Service;

/// <summary>
/// AP查询输入
/// </summary>
public class QueryAPInput
{
    /// <summary>
    /// 页码
    /// </summary>
    public int PageNo { get; set; } = 1;

    /// <summary>
    /// 分页大小
    /// </summary>
    public int PageSize { get; set; } = 10;
}

/// <summary>
/// AP创建输入
/// </summary>
public class CreateAPInput
{
    /// <summary>
    /// 网关MAC
    /// </summary>
    public string ApMac { get; set; }

    /// <summary>
    /// 网关名称
    /// </summary>
    public string ApName { get; set; }

    /// <summary>
    /// 网关描述
    /// </summary>
    public string Description { get; set; }
}

/// <summary>
/// AP更新输入
/// </summary>
public class UpdateAPInput
{
    /// <summary>
    /// 网关MAC
    /// </summary>
    public string ApMac { get; set; }

    /// <summary>
    /// 网关名称
    /// </summary>
    public string ApName { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public string Status { get; set; }

    /// <summary>
    /// 版本
    /// </summary>
    public string Version { get; set; }

    /// <summary>
    /// 网关IP
    /// </summary>
    public string ApIp { get; set; }

    /// <summary>
    /// 网关描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public long CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public long UpdateTime { get; set; }
}

/// <summary>
/// AP输出
/// </summary>
public class APOutput
{
    /// <summary>
    /// 网关MAC
    /// </summary>
    public string ApMac { get; set; }

    /// <summary>
    /// 网关名称
    /// </summary>
    public string ApName { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public string Status { get; set; }

    /// <summary>
    /// 版本
    /// </summary>
    public string Version { get; set; }

    /// <summary>
    /// 网关IP
    /// </summary>
    public string ApIp { get; set; }

    /// <summary>
    /// 网关描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public long CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public long UpdateTime { get; set; }
}